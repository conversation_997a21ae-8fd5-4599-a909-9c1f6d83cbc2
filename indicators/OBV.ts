import { pluck, reverse } from 'ramda';
import { OBV } from 'technicalindicators';
import { DbCandleStickDocument } from '@app/database/db-candlesticks/db-candlesticks.model';
import { OBVInput } from 'technicalindicators/declarations/generated';

const calcOBV = (close: number[], volume: number[]): number[] => {
  const input: OBVInput = {
    close: close,
    volume: volume,
  };

  return OBV.calculate(input);
};

export const calcOBVFromCandles = (
  candles: DbCandleStickDocument[],
): {
  obv: number;
  openTime: number;
  coinPriceUsd: number;
}[] => {
  const close = pluck('close')(candles);
  const volume = pluck('quoteVolume')(candles);

  const resultValues = calcOBV(close, volume);
  const result = [];
  const candlesReverse = reverse(candles);
  const resultReverse = reverse(resultValues);

  resultReverse.forEach((value, idx) => {
    result.push({
      obv: value,
      openTime: candlesReverse.at(idx).openTime,
      coinPriceUsd: candlesReverse.at(idx).open,
    });
  });

  return reverse(result);
};
