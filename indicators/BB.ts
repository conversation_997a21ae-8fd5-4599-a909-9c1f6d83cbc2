import { pluck, reverse } from 'ramda';
import { BollingerBands, OBV } from 'technicalindicators';
import { DbCandleStickDocument } from '@app/database/db-candlesticks/db-candlesticks.model';
import {
  BollingerBandsInput,
  BollingerBandsOutput,
  OBVInput,
} from 'technicalindicators/declarations/generated';

const calcBB = (
  period: number,
  stdDev: number,
  values: number[],
): BollingerBandsOutput[] => {
  const input: BollingerBandsInput = {
    period,
    stdDev,
    values,
  };

  return BollingerBands.calculate(input);
};

export const calcBBFromCandles = (
  candles: DbCandleStickDocument[],
  period: number = 14,
  stdDev: number = 2,
): {
  middle: number;
  lower: number;
  upper: number;
  pb: number;
  openTime: number;
  coinPriceUsd: number;
}[] => {
  const open = pluck('open')(candles);

  const resultValues = calcBB(period, stdDev, open);
  const result = [];
  const candlesReverse = reverse(candles);
  const resultReverse = reverse(resultValues);

  resultReverse.forEach((value, idx) => {
    result.push({
      middle: value.middle,
      lower: value.lower,
      upper: value.upper,
      pb: value.pb,
      openTime: candlesReverse.at(idx).openTime,
      coinPriceUsd: candlesReverse.at(idx).open,
    });
  });

  return reverse(result);
};
