import { pluck, reverse } from 'ramda';
import { VWAP } from 'technicalindicators';
import { DbCandleStickDocument } from '@app/database/db-candlesticks/db-candlesticks.model';
import { VWAPInput } from 'technicalindicators/declarations/volume/VWAP';

const calc = (
  high: number[],
  low: number[],
  close: number[],
  volume: number[],
): number[] => {
  const input: VWAPInput = {
    high,
    low,
    close,
    volume,
  };

  return VWAP.calculate(input);
};

export const calcVWAP = (
  candles: DbCandleStickDocument[],
): {
  vwap: number;
  openTime: number;
  coinPriceUsd: number;
}[] => {
  const high = pluck('high')(candles);
  const low = pluck('low')(candles);
  const close = pluck('close')(candles);
  const volume = pluck('quoteVolume')(candles);

  const resultValues = calc(high, low, close, volume);
  const result = [];
  const candlesReverse = reverse(candles);
  const resultReverse = reverse(resultValues);

  resultReverse.forEach((value, idx) => {
    result.push({
      vwap: isNaN(value) ? 0 : value,
      openTime: candlesReverse.at(idx).openTime,
      coinPriceUsd: candlesReverse.at(idx).open,
    });
  });

  return reverse(result);
};
