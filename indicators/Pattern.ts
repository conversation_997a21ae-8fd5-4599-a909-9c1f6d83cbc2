import { pluck } from 'ramda';
import { DbCandleStickDocument } from '@app/database/db-candlesticks/db-candlesticks.model';

const predictPatterns = async (open: number[]): Promise<any> => {
  const input = {
    values: open,
  };

  // return PatternDetector.predictPattern(input);
};

export const predictPatternsFromCandles = async (
  candles: DbCandleStickDocument[],
): Promise<any> => {
  const open = pluck('open')(candles);

  return await predictPatterns(open);
};
