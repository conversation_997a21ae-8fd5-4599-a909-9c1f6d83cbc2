import { last, pluck, reverse } from 'ramda';
import { AwesomeOscillator } from 'technicalindicators';
import { AwesomeOscillatorInput } from 'technicalindicators/declarations/oscillators/AwesomeOscillator';
import { DbCandleStickDocument } from '@app/database/db-candlesticks/db-candlesticks.model';

/**
 * Calculate AO
 let input = {
 high :   [24.63,24.69,24.99,25.36,25.19,25.17,25.01,24.96,25.08,25.25,25.21,25.37,25.61,25.58,25.46,25.33,25.09,25.03,24.91,24.89,25.13,24.63,24.69,24.99,25.36,25.19,25.17,25.01,24.96,25.08,25.25,25.21,25.37,25.61,25.58,25.46,25.33,25.09,25.03,24.91,24.89,25.13],
 low  :   [24.63,24.69,24.99,25.36,25.19,25.17,25.01,24.96,25.08,25.25,25.21,25.37,25.61,25.58,25.46,25.33,25.09,25.03,24.91,24.89,25.13,24.63,24.69,24.99,25.36,25.19,25.17,25.01,24.96,25.08,25.25,25.21,25.37,25.61,25.58,25.46,25.33,25.09,25.03,24.91,24.89,25.13],
 fastPeriod : 5,
 slowPeriod : 34,
 format : (a)=>parseFloat(a.toFixed(2))
 }

 let expectResult = [0.17,
 0.24,
 0.26,
 0.28,
 0.23,
 0.12,
 -0.01,
 -0.12,
 -0.16]
 */
const calcAO = (low: number[], high: number[]): number[] => {
  const input: AwesomeOscillatorInput = {
    low,
    high,
    fastPeriod: 5,
    slowPeriod: 34,
  };

  return AwesomeOscillator.calculate(input);
};

export const calcAOFromTimestamp = (
  openTime: number,
  candles: any[],
): number => {
  const output = calcAORangeFromTimestamp(openTime, candles);
  return last(output);
};

export const calcAORangeFromTimestamp = (
  openTime: number,
  candles: any[],
): number[] => {
  const filtered = candles.filter((x) => x.openTime <= openTime).slice(-34);

  const low = pluck('low')(filtered);
  const high = pluck('high')(filtered);

  return calcAO(low, high);
};

export const calcAORangeFromTimestampNoFilter = (
  candles: DbCandleStickDocument[],
): {
  aoValue: number;
  openTime: number;
  coinPriceUsd: number;
}[] => {
  const low = pluck('low')(candles);
  const high = pluck('high')(candles);

  const aoValues = calcAO(low, high);
  const result = [];
  const candlesReverse = reverse(candles);
  const aoReverse = reverse(aoValues);

  aoReverse.forEach((aoValue, idx) => {
    result.push({
      aoValue,
      openTime: candlesReverse.at(idx).openTime,
      coinPriceUsd: candlesReverse.at(idx).open,
    });
  });

  return reverse(result);
};
