// Bcrypt hashing, for use with passwords
import * as bcrypt from 'bcrypt';

// ----------------------

// Number of rounds to run the bcrypt hashing.  The bigger this number, the
// longer it'd take to compare hashes (and thus, the securer it is.)  However,
// bigger numbers also mean longer waiting times when logging in -- 10 is
// generally considered reasonable for today's hardware
const ROUNDS = 10;

/* BCRYPT */

// Generate a password hash, using the bcrypt library.  We wrap this in a
// Promise to avoid using bcrypt's default callbacks
export async function generatePasswordHash(plainTextPassword) {
  return new Promise((ok, reject) => {
    bcrypt.genSalt(ROUNDS, (saltError, salt) => {
      if (saltError) {
        return reject(saltError);
      }

      return bcrypt.hash(plainTextPassword, salt, (_hashError, hash) => {
        if (saltError) {
          return reject(saltError);
        }

        return ok(hash);
      });
    });
  });
}

// Check a hashed password
export async function checkPassword(plainTextPassword, hash) {
  return bcrypt.compare(plainTextPassword, hash);
}
