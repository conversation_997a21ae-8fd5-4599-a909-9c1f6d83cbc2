import { RedisPubSub } from 'graphql-redis-subscriptions';
import { PubSubRedisOptions } from 'graphql-redis-subscriptions/dist/redis-pubsub';
import * as process from 'process';
import 'dotenv/config';

const REDIS_DOMAIN_NAME = process.env.REDIS_HOST;
const PORT_NUMBER = 6379;

const redisOptions: PubSubRedisOptions = {
  connection: {
    maxRetriesPerRequest: 1,
    host: REDIS_DOMAIN_NAME,
    port: PORT_NUMBER,
    keyPrefix: 'ws:',
    retryStrategy: (times) => Math.min(times * 50, 2000),
  },
};

export const pubsub = new RedisPubSub(redisOptions);

// import { PubSub } from 'graphql-subscriptions';
// export const pubsub = new PubSub();

export const MARKET_HISTORY_SUBSCRIPTION = 'MARKET_HISTORY_SUBSCRIPTION';
export const DEPTH_SUBSCRIPTION = 'DEPTH_SUBSCRIPTION';
export const DEPTH_NEW_SYMBOL = 'DEPTH_NEW_SYMBOL';
export const TICKER_NEW_SYMBOL = 'TICKER_NEW_SYMBOL';
export const MARKET_HISTORY_SYMBOL = 'MARKET_HISTORY_SYMBOL';
export const ALL_TICKERS = 'ALL_TICKERS';
export const USER_SUBSCRIPTION = 'USER_SUBSCRIPTION';
export const COIN_PRICES_SUBSCRIPTION = 'COIN_PRICES_SUBSCRIPTION';
