import { Module } from '@nestjs/common';
import { DatabaseModule } from '@app/database';
import { JobsService } from './jobs.service';
import { PumpBotService } from './pump-bot/pump-bot.service';
import { ScheduleModule } from '@nestjs/schedule';

@Module({
  imports: [DatabaseModule, ScheduleModule.forRoot()],
  controllers: [],
  providers: [JobsService, PumpBotService],
})
export class PumpModule {}
