import { Injectable } from '@nestjs/common';
import { PumpBotService } from './pump-bot/pump-bot.service';
import { Cron } from '@nestjs/schedule';

@Injectable()
export class JobsService {
  constructor(private pumpBotService: PumpBotService) {}

  @Cron('*/10 * * * * *')
  async pumpBot() {
    await this.pumpBotService.pumpBot();
  }

  @Cron('3 */5 * * * *')
  async clearPumpBots() {
    await this.pumpBotService.clearPumpBots();
  }

  @Cron('0 0 0 10 * *')
  async deleteOldPumps() {
    await this.pumpBotService.removeOldPumps();
  }
}
