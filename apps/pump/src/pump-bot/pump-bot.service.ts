import { Injectable } from '@nestjs/common';
import { sendChannelMessage } from '../../../../utils/notifications';
import { DbExchangeInfoService } from '@app/database/db-exchange-info/db-exchange-info.service';
import PriceCalculation from '../../../../utils/PriceCalculation';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import { DbCandlesticksService } from '@app/database/db-candlesticks/db-candlesticks.service';
import { DbPumpService } from '@app/database/db-pump/db-pump.service';
import { Exchange } from '../../../../types/exchanges';

@Injectable()
export class PumpBotService {
  constructor(
    private exchangeInfoService: DbExchangeInfoService,
    private logger: DbLogService,
    private coinPriceService: DbCoinPriceService,
    private candleService: DbCandlesticksService,
    private pumpService: DbPumpService,
  ) {}

  pumpBot = async () => {
    const symbols =
      await this.exchangeInfoService.getUsdtSymbolsWithoutExcluded(
        Exchange.BINANCE,
      );

    for (const symbol of symbols) {
      const lastCandle = await this.candleService.dbGetLastCandle(
        symbol,
        '5',
        Exchange.BINANCE,
      );

      if (!lastCandle) {
        continue;
      }

      const price = await this.coinPriceService.dbGetCoinPrice(
        symbol,
        Exchange.BINANCE,
      );
      const percentDiff = PriceCalculation.toPercentGain(
        lastCandle.close,
        price,
      );

      if (percentDiff < 4) {
        continue;
      }

      const notifiedPump = await this.pumpService.dbGetPump(
        symbol,
        Exchange.BINANCE,
      );

      if (notifiedPump) {
        continue;
      }

      await this.pumpService.dbAddPump(
        symbol,
        price,
        percentDiff,
        Exchange.BINANCE,
      );
      const msg = `PUMP on ${symbol} +${percentDiff}% https://moonbot.dsserv.de/coin/${symbol}`;
      this.logger.logPump(msg);

      try {
        await sendChannelMessage(msg);
      } catch (e) {
        this.logger.errorPump('sendTelegramMessage for ', symbol, e);
      }
    }
  };

  clearPumpBots = async () => {
    await this.pumpService.dbClearPump();
  };

  async removeOldPumps() {
    await this.pumpService.dbClearOldPumps();
  }
}
