import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbAutoBotService } from '@app/database/db-auto-bot/db-auto-bot.service';
import { DbMarketBotService } from '@app/database/db-market-bot/db-market-bot.service';
import { DbWhiteListService } from '@app/database/db-white-list/db-white-list.service';
import { DbExchangeInfoService } from '@app/database/db-exchange-info/db-exchange-info.service';
import {
  DbAutoBot,
  DbAutoBotDocument,
} from '@app/database/db-auto-bot/db-auto-bot.model';
import { BuySellService } from '@app/auto-bot-processor/buy-sell/buy-sell.service';
import { Job } from 'bull';
import { DbIndicatorDocument } from '@app/database/db-indicator/db-indicator.model';
import { BinanceAccountInfoService } from '@app/binance-api/binance-account-info/binance-account-info.service';
import { DbUserService } from '@app/database/db-user/db-user.service';
import { GateioAccountInfoService } from '@app/gateio-api/gateio-account-info/gateio-account-info.service';
import { Exchange } from '../../../types/exchanges';

export class OrdersConsumer {
  constructor(
    private logger: DbLogService,
    private autobotService: DbAutoBotService,
    private marketBotService: DbMarketBotService,
    private buysellService: BuySellService,
    private whitelistService: DbWhiteListService,
    private exchangeService: DbExchangeInfoService,
    private binanceAccountInfoService: BinanceAccountInfoService,
    private gateioAccountInfoService: GateioAccountInfoService,
    private userService: DbUserService,
  ) {}

  // async handle(job: Job<unknown>) {
  //   const indicatorDocument: DbIndicatorDocument =
  //     job.data as DbIndicatorDocument;
  //   const { interval, indicator } = indicatorDocument;
  //
  //   const autobots =
  //     await this.autobotService.dbGetAllAutobotsByModeAndInterval(
  //       indicator,
  //       interval,
  //     );
  //
  //   for (const autobot of autobots) {
  //     switch (indicatorDocument.action) {
  //       case 'long':
  //         await this.checkForBuy(autobot, indicatorDocument);
  //         break;
  //       case 'short':
  //       default:
  //         await this.checkForSell(autobot, indicatorDocument);
  //         break;
  //     }
  //   }
  // }
  //
  // private async checkForSell(
  //   autobot: DbAutoBotDocument,
  //   indicatorDocument: DbIndicatorDocument,
  // ) {
  //   const { interval, indicator, symbol } = indicatorDocument;
  //
  //   const marketBots =
  //     await this.marketBotService.dbGetMarketBotsFromAutobot(autobot);
  //
  //   const marketBot = marketBots.find((x) => x.symbol == symbol);
  //
  //   if (marketBot) {
  //     let accountInfo = null;
  //     switch (autobot.exchange) {
  //       case Exchange.BINANCE:
  //         accountInfo =
  //           await this.binanceAccountInfoService.getAccountInfoWithRetry(
  //             autobot.userId,
  //           );
  //         break;
  //       case Exchange.GATEIO:
  //         accountInfo =
  //           await this.gateioAccountInfoService.getAccountInfoWithRetry(
  //             autobot.userId,
  //           );
  //         break;
  //     }
  //
  //     try {
  //       const { symbol } = marketBot;
  //
  //       if (indicatorDocument.action === 'short') {
  //         this.logger.logOrders(
  //           indicator,
  //           autobot.exchange,
  //           symbol,
  //           'SELL advice',
  //           'Strategy:',
  //           autobot.strategy,
  //           'Interval:',
  //           interval,
  //         );
  //         await this.buysellService.processSell(
  //           marketBot,
  //           autobot,
  //           accountInfo,
  //         );
  //       }
  //
  //       await this.sleep(() => {});
  //     } catch (e) {
  //       this.logger.errorOrders(e);
  //     }
  //   }
  // }
  //
  // private async checkForBuy(
  //   autobot: DbAutoBotDocument,
  //   indicatorDocument: DbIndicatorDocument,
  // ) {
  //   const { interval, indicator, symbol } = indicatorDocument;
  //   const allowedMarkets = await this.getMarketsToCheck(autobot);
  //
  //   if (!allowedMarkets.includes(symbol)) {
  //     return;
  //   }
  //
  //   // Смотрим купили ли уже этот коин
  //   const coinExistInPortfolio =
  //     await this.marketBotService.dbGetMarketBotForAutobotAndSymbol(
  //       autobot._id.toString(),
  //       symbol,
  //     );
  //
  //   if (coinExistInPortfolio) {
  //     return;
  //   }
  //
  //   if (indicatorDocument.action === 'long') {
  //     this.logger.logOrders(
  //       indicator,
  //       autobot.exchange,
  //       symbol,
  //       'BUY advice',
  //       'Strategy:',
  //       autobot.strategy,
  //       'Interval:',
  //       interval,
  //     );
  //
  //     let accountInfo = null;
  //
  //     switch (autobot.exchange) {
  //       case Exchange.BINANCE:
  //         await this.binanceAccountInfoService.getAccountInfoWithRetry(
  //           autobot.userId,
  //         );
  //         break;
  //       case Exchange.GATEIO:
  //         await this.gateioAccountInfoService.getAccountInfoWithRetry(
  //           autobot.userId,
  //         );
  //         break;
  //     }
  //
  //     await this.buysellService.processBuy(symbol, autobot, accountInfo);
  //   }
  // }
  //
  // private async getMarketsToCheck(autobot: DbAutoBot) {
  //   if (autobot.btcOnly) {
  //     return ['BTCUSDT'];
  //   } else if (autobot.buyOnlyFromWhitelist) {
  //     const result = await this.whitelistService.dbGetWhiteListCoins(
  //       autobot.userId,
  //       autobot.exchange,
  //     );
  //
  //     return [...result.map((x) => x.symbol)];
  //   }
  //   const exchange = await this.userService.getUserExchange(autobot.userId);
  //   return this.exchangeService.getUsdtSymbolsWithoutExcluded(exchange);
  // }
  //
  // async sleep(fn: any, ...args: any[]) {
  //   await this.timeout(1000);
  //   return fn(...args);
  // }
  //
  // async timeout(ms: number) {
  //   return new Promise((resolve) => setTimeout(resolve, ms));
  // }
}
