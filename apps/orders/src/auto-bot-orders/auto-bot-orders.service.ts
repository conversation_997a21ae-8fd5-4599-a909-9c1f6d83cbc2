import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { SendMailService } from '@app/send-mail';
import { serverUrl } from '../../../../utils/currentServer';
import { DbAutoBotDocument } from '@app/database/db-auto-bot/db-auto-bot.model';
import { DbMarketBotDocument } from '@app/database/db-market-bot/db-market-bot.model';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import { DbAutoBotService } from '@app/database/db-auto-bot/db-auto-bot.service';
import { DbMarketBotService } from '@app/database/db-market-bot/db-market-bot.service';
import { ShortFunctionsService } from '@app/auto-bot-processor/short-functions/short-functions.service';
import { BinanceAccountInfoService } from '@app/binance-api/binance-account-info/binance-account-info.service';
import { DbIndicator } from '@app/database/db-indicator/db-indicator.model';
import { GateioAccountInfoService } from '@app/gateio-api/gateio-account-info/gateio-account-info.service';
import { Exchange } from '../../../../types/exchanges';

@Injectable()
export class AutoBotOrdersService {
  constructor(
    private logger: DbLogService,
    private sendMailService: SendMailService,
    private coinPriceService: DbCoinPriceService,
    private autobotService: DbAutoBotService,
    private marketbotService: DbMarketBotService,
    private shortFunctionsService: ShortFunctionsService,
    private binanceAccountInfoService: BinanceAccountInfoService,
    private gateioAccountInfoService: GateioAccountInfoService,
  ) {}

  async processTPSLOrders(exchange: string) {
    const marketBots =
      await this.marketbotService.dbGetMarketBotsBySymbolAndStrategy(exchange);

    for (const marketBot of marketBots) {
      const autobot = await this.autobotService.dbGetAutoBot(
        marketBot.autobotId,
      );

      await this.processTakeProfit(marketBot, autobot);
      await this.processStopLoss(marketBot, autobot);
    }
  }

  async processTakeProfit(
    marketBot: DbMarketBotDocument,
    autobot: DbAutoBotDocument,
  ) {
    const { symbol, startPrice } = marketBot;
    const { takeProfitPercent, enableTakeProfit, exchange } = autobot;

    if (!enableTakeProfit || !takeProfitPercent || !startPrice) {
      return;
    }

    const currentCoinPrice = await this.coinPriceService.dbGetCoinPrice(
      symbol,
      exchange,
    );
    const trimmedCurrentCoinPrice = Number(Number(currentCoinPrice).toFixed(8));
    const sellAboveThisPrice = Number(
      (startPrice * (100 + takeProfitPercent)) / 100,
    );

    if (trimmedCurrentCoinPrice < sellAboveThisPrice) {
      return;
    }

    this.logger.logOrders(
      symbol,
      exchange,
      `-> Sell on autobot take profit ${takeProfitPercent}%`,
    );
    this.logger.logOrders(
      symbol,
      exchange,
      '-> Current coin price',
      trimmedCurrentCoinPrice.toFixed(8),
    );
    this.logger.logOrders(
      symbol,
      exchange,
      '-> Target sell above',
      sellAboveThisPrice.toFixed(8),
    );
    this.logger.logOrders(symbol, exchange, '-> Fetching account infos...');

    let accountInfo = null;

    switch (autobot.exchange) {
      case Exchange.BINANCE:
        await this.binanceAccountInfoService.getAccountInfoWithRetry(
          autobot.userId,
        );
        break;
      case Exchange.GATEIO:
        await this.gateioAccountInfoService.getAccountInfoWithRetry(
          autobot.userId,
        );
        break;
    }

    try {
      await this.shortFunctionsService.shortAutoBot(
        autobot,
        {
          price: trimmedCurrentCoinPrice,
          action: 'short',
          symbol,
          timestamp: new Date(),
          newAdded: true,
          interval: '15',
          profit: null,
          prediction: null,
          candleTime: new Date(),
          indicator: 'nesterov',
          exchange,
        } as DbIndicator,
        accountInfo,
      );

      await this.sendTPMail(
        autobot,
        symbol,
        sellAboveThisPrice,
        currentCoinPrice,
      );
    } catch (e) {
      this.logger.errorOrders(e);
    }

    this.logger.logOrders(symbol, exchange, '-> Sell finished');
  }

  async processStopLoss(
    marketBot: DbMarketBotDocument,
    autobot: DbAutoBotDocument,
  ) {
    const { symbol, startPrice } = marketBot;
    const { stoplossPercent, enableStoploss, exchange } = autobot;

    if (!enableStoploss || !startPrice || !stoplossPercent) {
      return;
    }

    const currentCoinPrice = await this.coinPriceService.dbGetCoinPrice(
      symbol,
      exchange,
    );
    const trimmedCurrentCoinPrice = Number(Number(currentCoinPrice).toFixed(8));
    const sellBelowThisPrice = (startPrice * (100 - stoplossPercent)) / 100;

    if (trimmedCurrentCoinPrice >= Number(sellBelowThisPrice)) {
      return;
    }

    this.logger.logOrders(
      symbol,
      exchange,
      `-> Sell on autobot stoploss ${stoplossPercent}%`,
    );
    this.logger.logOrders(
      symbol,
      exchange,
      '-> Current coin price',
      trimmedCurrentCoinPrice.toFixed(8),
    );
    this.logger.logOrders(
      symbol,
      exchange,
      '-> Target sell below price',
      sellBelowThisPrice.toFixed(8),
    );
    this.logger.logOrders(symbol, exchange, '-> Fetching account infos...');
    let accountInfo = null;

    switch (autobot.exchange) {
      case Exchange.BINANCE:
        await this.binanceAccountInfoService.getAccountInfoWithRetry(
          autobot.userId,
        );
        break;
      case Exchange.GATEIO:
        await this.gateioAccountInfoService.getAccountInfoWithRetry(
          autobot.userId,
        );
        break;
    }

    try {
      await this.shortFunctionsService.shortAutoBot(
        autobot,
        {
          price: trimmedCurrentCoinPrice,
          action: 'short',
          symbol,
          indicator: 'nesterov',
          interval: '15',
          profit: null,
          prediction: null,
          newAdded: false,
          timestamp: new Date(),
          candleTime: new Date(),
          exchange,
        } as DbIndicator,
        accountInfo,
      );

      await this.sendSLMail(
        autobot,
        symbol,
        sellBelowThisPrice,
        currentCoinPrice,
      );
    } catch (e) {
      this.logger.errorOrders(e);
    }

    this.logger.logOrders(symbol, exchange, '-> Sell finished');
  }

  async sendTPMail(
    autobot: DbAutoBotDocument,
    symbol: string,
    sellAboveThisPrice: number,
    currentCoinPrice: number,
  ) {
    await this.sendMailService.sendOrderMail(
      autobot.userId,
      'Moonbot ' + symbol + ' Take Profit',
      'Take Profit',
      {
        'Symbol:': `<a href="${serverUrl}/coin/${symbol}">${symbol}</a>`,
        'TP Price:': `${sellAboveThisPrice}`,
        'Sell Price:': `${currentCoinPrice}`,
        'Timestamp:': `${new Date().toLocaleString('de-DE')}`,
      },
      true,
    );
  }

  async sendSLMail(
    autobot: DbAutoBotDocument,
    symbol: string,
    sellBelowThisPrice: number,
    currentCoinPrice: number,
  ) {
    await this.sendMailService.sendOrderMail(
      autobot.userId,
      'Moonbot ' + symbol + ' Stop Loss',
      'Stop Loss',
      {
        'Symbol:': `<a href="${serverUrl}/coin/${symbol}">${symbol}</a>`,
        'SL Price:': `${sellBelowThisPrice}`,
        'Sell Price:': `${currentCoinPrice}`,
        'Timestamp:': `${new Date().toLocaleString('de-DE')}`,
      },
      true,
    );
  }
}
