import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbAutoBotDocument } from '@app/database/db-auto-bot/db-auto-bot.model';
import { DbAutoBotService } from '@app/database/db-auto-bot/db-auto-bot.service';
import { BinanceAccountInfoService } from '@app/binance-api/binance-account-info/binance-account-info.service';
import { LongFunctionsService } from '@app/auto-bot-processor/long-functions/long-functions.service';
import { ShortFunctionsService } from '@app/auto-bot-processor/short-functions/short-functions.service';
import { BuySellService } from '@app/auto-bot-processor/buy-sell/buy-sell.service';
import { DbIndicatorDocument } from '@app/database/db-indicator/db-indicator.model';
import { Exchange } from '../../../../types/exchanges';
import { GateioAccountInfoService } from '@app/gateio-api/gateio-account-info/gateio-account-info.service';
import { TradeProcessorChecksService } from '@app/auto-bot-processor/trade-processor/trade-processor-checks.service';
import { DbMarketBotTradeService } from '@app/database/db-market-bot-trade/db-market-bot-trade.service';
import { Advice } from '../../../../types/advice';
import { DbNoticedCoinService } from '@app/database/db-noticed-coin/db-noticed-coin.service';

@Injectable()
export class UpdateProfitService {
  constructor(
    private autobotService: DbAutoBotService,
    private binanceAccountInfoService: BinanceAccountInfoService,
    private gateioAccountInfoService: GateioAccountInfoService,
    private marketBotTradeService: DbMarketBotTradeService,
  ) {}

  updateProfit = async () => {
    const matchedAutobots = await this.autobotService.dbGetAllActiveAutobots();
    for (const autobot of matchedAutobots) {
      let accountInfo = null;
      switch (autobot.exchange) {
        case Exchange.BINANCE:
          accountInfo =
            await this.binanceAccountInfoService.getAccountInfoWithRetry(
              autobot.userId,
            );
          break;
        case Exchange.GATEIO:
          accountInfo =
            await this.gateioAccountInfoService.getAccountInfoWithRetry(
              autobot.userId,
            );
          break;
      }

      const usdValue = accountInfo.usdValue;

      let profitOverall = 0;
      let profitToday = 0;
      let profitTodayRelative = 0;
      let profitYesterday = 0;
      let profitThisWeek = 0;
      let profitLastMonth = 0;
      let profitThisMonth = 0;

      let profitOverallUsd = 0;
      let profitYesterdayUsd = 0;
      let profitThisWeekUsd = 0;
      let profitTodayUsd = 0;
      let profitLastMonthUsd = 0;
      let profitThisMonthUsd = 0;

      const today = new Date(
        new Date().getFullYear(),
        new Date().getMonth(),
        new Date().getDate(),
      ).getTime();
      const yesterday = new Date(
        new Date().getFullYear(),
        new Date().getMonth(),
        new Date().getDate() - 1,
      ).getTime();

      const thisWeekDate = new Date(
        new Date().getFullYear(),
        new Date().getMonth(),
        new Date().getDate(),
      );
      const thisWeekDay = thisWeekDate.getDay();
      const diff =
        thisWeekDate.getDate() - thisWeekDay + (thisWeekDay === 0 ? -6 : 1);
      thisWeekDate.setDate(diff);

      const thisWeek = thisWeekDate.getTime();
      const thisMonth = new Date(
        new Date().getFullYear(),
        new Date().getMonth(),
        1,
      ).getTime();
      const lastMonth = new Date(
        new Date().getFullYear(),
        new Date().getMonth() - 1,
        1,
      ).getTime();

      const trades =
        await this.marketBotTradeService.dbGetMarketBotProfitFromAutobot(
          autobot._id.toString(),
        );

      for (const trade of trades) {
        const profit = trade.profit;
        const tradeDate = new Date(trade.tradeDate).getTime();

        const shortUsd = trade.valueInUsd;
        const profitUsd = trade.profitUsd ? trade.profitUsd : 0;

        if (shortUsd && profit != null) {
          profitOverall += profit;
          profitOverallUsd += profitUsd;

          if (tradeDate > today) {
            profitToday += profit;
            profitTodayRelative += (shortUsd / (usdValue / 100)) * profit;
            profitTodayUsd += profitUsd;
          }

          if (tradeDate > yesterday && tradeDate < today) {
            profitYesterday += profit;
            profitYesterdayUsd += profitUsd;
          }

          if (tradeDate > thisWeek) {
            profitThisWeek += profit;
            profitThisWeekUsd += profitUsd;
          }

          if (tradeDate > thisMonth) {
            profitThisMonth += profit;
            profitThisMonthUsd += profitUsd;
          }

          if (tradeDate > lastMonth && tradeDate < thisMonth) {
            profitLastMonth += profit;
            profitLastMonthUsd += profitUsd;
          }
        }
      }

      await this.autobotService.dbUpdateAutoBotProfit(autobot, {
        profitToday,
        profitTodayRelative,
        profitYesterday,
        profitThisWeek,
        profitThisMonth,
        profitLastMonth,
        profitOverall,
        profitTodayUsd,
        profitYesterdayUsd,
        profitThisWeekUsd,
        profitLastMonthUsd,
        profitThisMonthUsd,
        profitOverallUsd,
      });
    }
  };
}
