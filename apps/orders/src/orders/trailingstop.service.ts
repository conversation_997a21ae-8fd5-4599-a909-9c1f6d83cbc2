import { Injectable } from '@nestjs/common';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import { DbBotOrderService } from '@app/database/db-bot-order/db-bot-order.service';
import { DbLogService } from '@app/database/db-log/db-log.service';
import PriceCalculation from '../../../../utils/PriceCalculation';
import { DbExchangeInfoService } from '@app/database/db-exchange-info/db-exchange-info.service';
import { DbUserService } from '@app/database/db-user/db-user.service';

@Injectable()
export class TrailingstopService {
  constructor(
    private coinPriceService: DbCoinPriceService,
    private botOrderService: DbBotOrderService,
    private logger: DbLogService,
    private dbExchangeInfoService: DbExchangeInfoService,
    private userService: DbUserService,
  ) {}

  async trailingstop() {
    const slPercent = 5;

    const matchedBots = await this.botOrderService.dbGetBotOrders(null, true);
    const slOrders = matchedBots.filter(
      (x) => x.type === 'BOT_STOP_LOSS' || x.type === 'AUTO_STOP_LOSS',
    );

    for (const slOrder of slOrders) {
      const exchangeInfo =
        await this.dbExchangeInfoService.getLatestExchangeInfo(
          slOrder.exchange,
          {
            symbol: slOrder.symbol,
          },
        );

      const trailingStops: any = [
        {
          profit: slPercent + 20,
          sl: slPercent + 10,
        },
        {
          profit: slPercent + 10,
          sl: slPercent + 5,
        },
        {
          profit: slPercent + 5, // -5% SL + 5% profit => 10% price increase
          sl: slPercent + 2, // -5% SL + 2% profit => 7%
        },
      ];
      const {
        price: slPrice,
        amount,
        user: userId,
        symbol,
        response,
      } = slOrder;
      const exchange = await this.userService.getUserExchange(userId);

      if (!response) {
        const currentCoinPrice = await this.coinPriceService.dbGetCoinPrice(
          symbol,
          exchange,
        );

        trailingStops.forEach((ts) => {
          ts.changeAbovePrice = PriceCalculation.getFixedPrice(
            slPrice * (100 / ts.profit + 1),
            exchangeInfo,
            symbol,
          );

          ts.newStopPrice = PriceCalculation.getFixedPrice(
            slPrice * (100 / ts.sl + 1),
            exchangeInfo,
            symbol,
          );
        });

        for (const trailingstop of trailingStops) {
          if (currentCoinPrice >= trailingstop.changeAbovePrice) {
            await this.botOrderService.dbUpdateSLBotOrder(
              trailingstop.newStopPrice,
              slOrder._id,
            );

            this.logger.logOrders(
              exchange,
              `TrailingStop: Price increased above ${trailingstop.profit - slPercent}% to ${currentCoinPrice}. 
              Updating stoploss from ${slPrice} to ${trailingstop.newStopPrice}`,
            );
          }
        }
      }
    }
  }
}
