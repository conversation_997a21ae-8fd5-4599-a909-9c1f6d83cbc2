import { Injectable } from '@nestjs/common';
import { DbAutoBotService } from '@app/database/db-auto-bot/db-auto-bot.service';
import { DbWhiteListService } from '@app/database/db-white-list/db-white-list.service';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { descend, prop, sortWith } from 'ramda';
import { DbUserService } from '@app/database/db-user/db-user.service';

@Injectable()
export class FillWhitelist {
  constructor(
    private autobotService: DbAutoBotService,
    private whitelistService: DbWhiteListService,
    private logger: DbLogService,
    private dbUserService: DbUserService,
  ) {}

  fillWhitelist = async () => {
    const allAutoBots = await this.autobotService.dbGetPlainAutobots();

    for (const autoBot of allAutoBots) {
      const userId = autoBot.userId;
      const isRefillEnabled = autoBot.autoRefillWhitelist;

      if (!isRefillEnabled) {
        continue;
      }
      const whitelistCoins = await this.whitelistService.dbGetWhiteListCoins(
        userId,
        autoBot.exchange,
      );
      const exchange = await this.dbUserService.getUserExchange(userId);
      const suggestedFuturesCoins = await this.suggestFuturesList();
      const suggestedSpotCoins = await this.suggestSpotList();
      const spotCoins = suggestedSpotCoins.map((x) => x.symbol);

      // Delete all previously coins
      for (const whitelistCoin of whitelistCoins) {
        if (whitelistCoin.autoAdded) {
          await this.whitelistService.dbDeleteWhiteListCoin(
            userId,
            {
              symbol: whitelistCoin.symbol,
            } as GQL.IDeleteWhiteListCoinMutationOnMutationArguments,
            exchange,
          );
        }
      }

      const manualAddedList = whitelistCoins
        .filter((x) => !x.autoAdded)
        .map((x) => x.symbol);

      // Add new coins
      this.logger.logOrders(
        exchange,
        `Filling bot whitelist. ${suggestedFuturesCoins.length} futures coins and ${suggestedSpotCoins.length} spot coins`,
      );

      const excluded = ['BNBUSDT', 'BTCUSDT', 'ETHUSDT', 'ETCUSDT'];
      const added = [];
      let i = 0;

      for (const suggestedCoin of suggestedFuturesCoins) {
        const { longCount, shortCount, symbol } = suggestedCoin;
        if (excluded.includes(symbol)) {
          continue;
        }

        if (manualAddedList.includes(symbol)) {
          continue;
        }

        if (!spotCoins.includes(symbol)) {
          continue;
        }

        if (longCount <= shortCount) {
          continue;
        }

        if (longCount < 40) {
          this.logger.logOrders(
            exchange,
            `${suggestedCoin.symbol} skipped. LongCount < 40 `,
          );
          continue;
        }

        if (longCount / 2 < shortCount) {
          this.logger.logOrders(
            exchange,
            `${suggestedCoin.symbol} skipped. Bad longCount quotient`,
          );
          continue;
        }

        await this.whitelistService.dbAddWhiteListCoin(
          userId,
          {
            symbol: suggestedCoin.symbol,
            autoAdded: true,
          },
          exchange,
        );

        added.push(suggestedCoin.symbol);
        i++;
      }

      this.logger.logOrders(
        exchange,
        `Added ${i} whitelist coins: ${added.join(',')}`,
      );
    }
  };

  async suggestFuturesList() {
    let coins: { symbol: string; longCount: number; shortCount: number }[] = [];
    try {
      const result = await fetch(
        'https://www.binance.com/bapi/futures/v1/public/future/common/strategy/landing-page/queryTopCount',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            strategyType: 2,
            rows: 150,
            page: 1,
          }),
        },
      );
      const responseJson: any = await result.json();

      if (responseJson && responseJson.data) {
        coins = responseJson.data.filter((x) => x.symbol.includes('USDT'));
        // @ts-ignore
        coins = sortWith([descend(prop('longCount'))])(coins);
      }
    } catch (e) {
      this.logger.errorServer(e);
    }

    return coins;
  }

  async suggestSpotList() {
    let coins: { symbol: string; longCount: number; shortCount: number }[] = [];
    try {
      const result = await fetch(
        'https://www.binance.com/bapi/futures/v1/public/future/common/strategy/landing-page/queryTopCount',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            strategyType: 1,
            rows: 300,
            page: 1,
          }),
        },
      );
      const responseJson: any = await result.json();

      if (responseJson && responseJson.data) {
        coins = responseJson.data.filter((x) => x.symbol.includes('USDT'));
      }
    } catch (e) {
      this.logger.errorServer(e);
    }

    return coins;
  }
}
