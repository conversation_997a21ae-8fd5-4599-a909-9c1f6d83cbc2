import { Injectable } from '@nestjs/common';
import { FillWhitelist } from './whitelist/fill-whitelist';
import { TakeProfitService } from './orders/take-profit.service';
import { StoplossService } from './orders/stoploss.service';
import { <PERSON>ron } from '@nestjs/schedule';
import { TrailingstopService } from './orders/trailingstop.service';
import { Exchange } from '../../../types/exchanges';
import { AdviceProcessorService } from '@app/auto-bot-processor/advice-processor/advice-processor.service';
import { TradeProcessorService } from '@app/auto-bot-processor/trade-processor/trade-processor.service';
import { AutoBotOrdersService } from './auto-bot-orders/auto-bot-orders.service';
import { UpdateProfitService } from './update-profit/update-profit.service';

/**
 *    *    *    *    *    *
 ┬    ┬    ┬    ┬    ┬    ┬
 │    │    │    │    │    │
 │    │    │    │    │    └ day of week (0 - 7) (0 or 7 is Sun)
 │    │    │    │    └───── month (1 - 12)
 │    │    │    └────────── day of month (1 - 31)
 │    │    └─────────────── hour (0 - 23)
 │    └──────────────────── minute (0 - 59)
 └───────────────────────── second (0 - 59, OPTIONAL)
 */

@Injectable()
export class JobsService {
  constructor(
    private takeprofitService: TakeProfitService,
    private stoplossService: StoplossService,
    private trailingStop: TrailingstopService,
    private actionFillWhitelist: FillWhitelist,
    private adviceProcessorService: AdviceProcessorService,
    private tradeProcessor: TradeProcessorService,
    private autobotOrdersService: AutoBotOrdersService,
    private updateProfitService: UpdateProfitService,
  ) {}

  /**
   * Process Take Profit and Stoploss orders created manually by user
   */
  @Cron('*/10 * * * * *')
  async takeprofitStopLoss() {
    await this.takeprofitService.takeprofit();
    await this.stoplossService.stopLoss();
    await this.trailingStop.trailingstop();
  }

  /**
   * Automatic fill whitelist from exchange
   */
  @Cron('30 30 0 * * *')
  async jobActionFillWhitelist() {
    await this.actionFillWhitelist.fillWhitelist();
  }

  /**
   * Process indicator advices
   */
  @Cron('0 */5 * * * *')
  async processAdvices() {
    await this.adviceProcessorService.processNewAdvices();
  }

  /**
   * Process Take Profit and Stoploss orders created by autobot (nesterov)
   */
  @Cron('30 */2 * * * *')
  async processTPSLOrdersBinance() {
    await this.autobotOrdersService.processTPSLOrders(Exchange.BINANCE);
  }

  /**
   * Process Take Profit and Stoploss orders created by autobot (nesterov)
   */
  @Cron('30 */2 * * * *')
  async processTPSLOrdersGateio() {
    await this.autobotOrdersService.processTPSLOrders(Exchange.GATEIO);
  }

  /**
   * Update profit of currently running autobot
   */
  @Cron('30 */5 * * * *')
  async updateProfit() {
    await this.updateProfitService.updateProfit();
  }

  async startJobs() {}
}
