FROM node:20-alpine as base

RUN npm i -g pnpm

# ---

FROM base as dependencies

ENV NODE_ENV build

WORKDIR /app

COPY package.json pnpm-lock.yaml ./
COPY .env.production .env

RUN pnpm install

# ---

FROM base as build

WORKDIR /app

ENV NODE_ENV production

COPY . .
COPY --from=dependencies /app/.env ./.env
COPY --from=dependencies /app/node_modules ./node_modules

RUN pnpm build orders
RUN pnpm prune --prod

# ---

FROM base as deploy

ENV NODE_ENV production

WORKDIR /app

COPY --from=build /app/dist/ ./dist/
COPY --from=build /app/node_modules/ ./node_modules/
COPY --from=build /app/.env ./
COPY --from=build /app/types/ ./types/
COPY --from=build /app/schema.graphql ./

CMD ["node", "dist/apps/orders/main.js"]