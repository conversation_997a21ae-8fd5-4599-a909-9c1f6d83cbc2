import { ApolloDriver, ApolloDriverConfig } from '@nestjs/apollo';
import * as path from 'path';

export const graphqlConfig = (): ApolloDriverConfig => {
  console.log('CWD:', process.cwd());

  return {
    driver: ApolloDriver,
    typePaths: [path.join(process.cwd(), 'schema.graphql')],
    definitions: {
      path: path.join(process.cwd(), 'generated/schema.ts'),
      outputAs: 'class',
    },
    context: ({ req }) => ({ req }),
    // autoSchemaFile: true,
    sortSchema: true,
    subscriptions: {
      'subscriptions-transport-ws': {
        path: '/graphql',
      },
    },
    installSubscriptionHandlers: true,
    playground: false,
  };
};
