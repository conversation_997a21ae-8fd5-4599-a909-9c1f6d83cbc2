import { SessionOptions } from 'express-session';
import { default as Redis } from 'ioredis';
import * as connectRedis from 'connect-redis';

export const sessionConfig = (secret: string): SessionOptions => {
  const redisClient = new Redis();

  return {
    store: new connectRedis.default({
      client: redisClient,
      prefix: 'mbql:',
    }),
    name: 'qid',
    secret: secret,
    resave: false,
    saveUninitialized: false,
    cookie: {
      httpOnly: false,
      maxAge: 1000 * 60 * 60 * 24 * 365,
    },
  };
};
