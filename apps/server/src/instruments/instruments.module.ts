import { Module } from '@nestjs/common';
import { InstrumentsResolver } from './instruments.resolver';
import { InstrumentsService } from './instruments.service';
import { BinanceApiModule } from '@app/binance-api';
import { DatabaseModule } from '@app/database';
import { WebsocketModule } from '../websocket/websocket.module';
import { GateioApiModule } from '@app/gateio-api';

@Module({
  imports: [BinanceApiModule, GateioApiModule, DatabaseModule, WebsocketModule],
  providers: [InstrumentsResolver, InstrumentsService],
  exports: [InstrumentsResolver, InstrumentsService],
})
export class InstrumentsModule {}
