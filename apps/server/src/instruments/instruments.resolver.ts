import { Args, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { InstrumentsService } from './instruments.service';
import { InstrumentsQuery } from '../../../../types/schema';
import { AuthUser, CurrentUser } from '../auth/jwt.user.decorator';

@Resolver(InstrumentsQuery)
@UseGuards(JwtAuthGuard)
export class InstrumentsResolver {
  constructor(private instrumentsService: InstrumentsService) {}

  @Query()
  async instruments() {
    return {};
  }

  @ResolveField()
  public async fetch(
    @Args() args: GQL.IFetchOnInstrumentsQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.instrumentsService.fetch(args, user.userId);
  }
}
