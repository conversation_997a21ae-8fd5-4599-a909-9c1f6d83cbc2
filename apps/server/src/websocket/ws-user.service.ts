import { Injectable } from '@nestjs/common';
import { BinanceWsUserService } from '@app/binance-api/binance-api/ws/binance-ws-user.service';
import { DbLogService } from '@app/database/db-log/db-log.service';

@Injectable()
export class WsUserService {
  constructor(
    private wsUserService: BinanceWsUserService,
    private logger: DbLogService,
  ) {}

  async getWSUser() {
    try {
      return this.wsUserService.user(() => {}, null);
    } catch (e) {
      this.logger.errorServer(e);
    }
  }
}
