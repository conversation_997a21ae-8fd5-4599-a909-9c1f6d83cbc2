import { Module } from '@nestjs/common';
import { DatabaseModule } from '@app/database';
import { WsAllTickersService } from './ws-all-tickers.service';
import { WsDepthService } from './ws-depth.service';
import { WsTradesService } from './ws-trades.service';
import { WsUserService } from './ws-user.service';
import { BinanceApiModule } from '@app/binance-api';
import { WebsocketResolver } from './websocket.resolver';
import { GateioApiModule } from '@app/gateio-api';

@Module({
  imports: [DatabaseModule, BinanceApiModule, GateioApiModule, GateioApiModule],
  providers: [
    WsAllTickersService,
    WsDepthService,
    WsTradesService,
    WsUserService,
    WebsocketResolver,
  ],
  exports: [
    WsAllTickersService,
    WsDepthService,
    WsTradesService,
    WsUserService,
    WebsocketResolver,
  ],
})
export class WebsocketModule {}
