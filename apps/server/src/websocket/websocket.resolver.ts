import { Args, Resolver, Subscription } from '@nestjs/graphql';
import {
  ALL_TICKERS,
  COIN_PRICES_SUBSCRIPTION,
  DEPTH_SUBSCRIPTION,
  MARKET_HISTORY_SUBSCRIPTION,
  pubsub,
} from '../../../../utils/pubSub';

@Resolver()
export class WebsocketResolver {
  @Subscription('depth', {
    resolve: (data) => {
      return {
        lastUpdateId: null,
        error: null,
        asks: data.asks,
        bids: data.bids,
      };
    },
    filter: (payload, args: any) => {
      return payload.symbol === args.symbol;
    },
  })
  async depth(@Args() args: GQL.IDepthOnSubscriptionArguments) {
    return pubsub.asyncIterator(DEPTH_SUBSCRIPTION);
  }

  @Subscription('marketHistory', {
    resolve: (data, args) => {
      if (data[args.symbol]) {
        return data[args.symbol].map((trade) => ({
          id: trade.tradeId,
          isBuyerMaker: trade.maker,
          price: trade.price,
          qty: trade.quantity,
          time: trade.eventTime,
          symbol: args.symbol,
          maker: args.maker,
          tradeId: args.tradeId,
          eventType: args.eventType,
          tradeTime: args.tradeTime,
        }));
      }

      return [];
    },
  })
  async marketHistory(@Args() args: GQL.IMarketHistoryOnSubscriptionArguments) {
    return pubsub.asyncIterator(MARKET_HISTORY_SUBSCRIPTION);
  }

  @Subscription('allTickers', {
    resolve: (data, args) => {
      let result = data;

      if (args.symbol) {
        result = data.filter((x) => (<any>x).symbol === args.symbol);
      }

      if (args.base) {
        result = data.filter((x) => (<any>x).symbol.substr(-4) === args.base);
      }

      return result;
    },
  })
  async allTickers(@Args() args: GQL.IAllTickersOnSubscriptionArguments) {
    return pubsub.asyncIterator(ALL_TICKERS);
  }

  @Subscription('coinPrices')
  async coinPrices(@Args() args: GQL.ICoinPriceOnSubscriptionArguments) {
    return pubsub.asyncIterator(COIN_PRICES_SUBSCRIPTION);
  }

  @Subscription('coinPrice', {
    resolve: (obj, args: any) => {
      const result = [];
      args.symbols.forEach((symbol) => {
        result.push(obj.find((x) => x.symbol === symbol));
      });

      return result;
    },
  })
  async coinPrice(@Args() args: GQL.ICoinPriceOnSubscriptionArguments) {
    return pubsub.asyncIterator(COIN_PRICES_SUBSCRIPTION);
  }
}
