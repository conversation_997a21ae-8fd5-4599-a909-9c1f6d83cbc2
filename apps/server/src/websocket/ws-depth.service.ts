import { v4 } from 'uuid';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { BinanceWsDepthService } from '@app/binance-api/binance-api/ws/binance-ws-depth.service';
import {
  DEPTH_NEW_SYMBOL,
  DEPTH_SUBSCRIPTION,
  pubsub,
} from '../../../../utils/pubSub';
import { Injectable } from '@nestjs/common';

@Injectable()
export class WsDepthService {
  constructor(
    private logger: DbLogService,
    private wsDepthService: BinanceWsDepthService,
  ) {}

  async initWSDepth() {
    const requestedDepthSymbols = new Set();

    await pubsub.subscribe(DEPTH_NEW_SYMBOL, (symbol: string) => {
      if (requestedDepthSymbols.has(symbol)) {
        return;
      }

      requestedDepthSymbols.add(symbol);
      try {
        this.wsDepthService.depth([{ symbol, level: 10 }], (ticker) => {
          ticker.bids = ticker.bids.map((t) => {
            return { ...t, rowId: v4() };
          });

          ticker.asks = ticker.asks.map((t) => {
            return { ...t, rowId: v4() };
          });

          pubsub.publish(DEPTH_SUBSCRIPTION, ticker);
        });
      } catch (e) {
        this.logger.errorServer(e);
      }
    });
  }
}
