import { uniq, values } from 'ramda';
import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { BinanceWsAlltickersService } from '@app/binance-api/binance-api/ws/binance-ws-alltickers.service';
import {
  ALL_TICKERS,
  pubsub,
  TICKER_NEW_SYMBOL,
} from '../../../../utils/pubSub';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import { DbAllTickerService } from '@app/database/db-all-ticker/db-all-ticker.service';
import { Ticker } from 'binance-api-node';
import { DbExchangeInfoService } from '@app/database/db-exchange-info/db-exchange-info.service';
import { Exchange } from '../../../../types/exchanges';
import { GateioWsAlltickersService } from '@app/gateio-api/ws/gateio-ws-alltickers.service';
import { GateioExchangeInfoService } from '@app/gateio-api/gateio-exchange-info/gateio-exchange-info.service';

@Injectable()
export class WsAllTickersService {
  alltickers = {};

  constructor(
    private logger: DbLogService,
    private binanceWsAlltickersService: BinanceWsAlltickersService,
    private gateioWsAlltickersService: GateioWsAlltickersService,
    private coinPriceService: DbCoinPriceService,
    private allTickersService: DbAllTickerService,
    private dbExchangeInfoService: DbExchangeInfoService,
    private exchangeService: GateioExchangeInfoService,
  ) {}

  public async initAllTickers(exchange: string) {
    const requestedSymbols = [];

    let counter = 0;
    let symbols =
      await this.dbExchangeInfoService.getUsdtSymbolsWithoutExcluded(exchange);

    try {
      switch (exchange) {
        case Exchange.BINANCE:
          this.binanceWsAlltickersService.allTickers((tickers: Ticker[]) => {
            counter++;

            if (counter > 10) {
              counter = 0;
              this.onPriceUpdateAction(tickers, symbols, Exchange.BINANCE);
            }
          });

          break;
        case Exchange.GATEIO:
          await pubsub.subscribe(TICKER_NEW_SYMBOL, async (symbol: string) => {
            if (requestedSymbols.includes(symbol)) {
              return;
            }

            // Unsubscribe
            await this.gateioWsAlltickersService.unsubscribeFromAll();
            requestedSymbols.push(symbol);

            const gateioSymbols = uniq(
              requestedSymbols.map((x) => x.slice(0, -4) + '_USDT'),
            );

            await this.gateioWsAlltickersService.allTickers(
              (tickers: Ticker[]) => {
                this.onPriceUpdateAction(
                  tickers,
                  gateioSymbols,
                  Exchange.GATEIO,
                );
              },
              gateioSymbols,
            );
          });
      }

      this.logger.logServer(exchange, 'All Tickers Socket initialized');
    } catch (e) {
      this.logger.errorServer(e);
    }
  }

  publishTickers() {
    pubsub.publish(ALL_TICKERS, values(this.alltickers));
  }

  getAllTickers = () => {
    return values(this.alltickers);
  };

  onPriceUpdateAction = async (
    tickers: Ticker[],
    symbols: string[],
    exchange: string,
  ) => {
    try {
      const btcPrice = await this.coinPriceService.dbGetCoinPrice(
        'BTCUSDT',
        Exchange.BINANCE,
      );

      for (const t of tickers) {
        if (t.symbol.slice(-4) !== 'USDT') continue;
        if (!symbols.includes(t.symbol)) continue;

        let ticker: any = t;

        ticker.volumeQuoteBTC = Number(ticker.volumeQuote) / btcPrice;

        if (exchange != Exchange.GATEIO) {
          ticker = await this.allTickersService.calcVolumeChange(t, exchange);

          ticker.percent4h = await this.allTickersService.calcPricePercents(
            ticker,
            exchange,
          );
        }

        this.alltickers[t.symbol] = ticker;
        await this.allTickersService.saveTickerData(t.symbol, ticker, exchange);
      }
    } catch (e) {
      this.logger.errorServer(e.message);
    }
  };
}
