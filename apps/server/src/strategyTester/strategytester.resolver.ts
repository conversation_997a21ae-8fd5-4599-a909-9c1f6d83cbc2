import { Args, Mu<PERSON>, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { StrategyQuery } from '../../../../types/schema';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AuthUser, CurrentUser } from '../auth/jwt.user.decorator';
import { StrategytesterService } from './strategytester.service';

@Resolver(StrategyQuery)
@UseGuards(JwtAuthGuard)
export class StrategytesterResolver {
  constructor(private strategytesterService: StrategytesterService) {}

  @Query()
  async strategy() {
    return {};
  }

  @ResolveField('getStrategyTests')
  public async getStrategyTests(@CurrentUser() user: AuthUser) {
    return this.strategytesterService.getStrategyTests(user.userId);
  }

  @ResolveField('getAllStrategyTests')
  public async getAllStrategyTests(@CurrentUser() user: AuthUser) {
    return this.strategytesterService.getAllStrategyTests(user.userId);
  }

  @Mutation()
  public async calcAllStrategy(
    @Args() args: GQL.ICalcAllStrategyOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    await this.strategytesterService.calcAllStrategy(args, user.userId);

    return {
      ok: true,
    };
  }

  @Mutation()
  public async backtestRecalcMutation(@CurrentUser() user: AuthUser) {
    return this.strategytesterService.backtestRecalcMutation(user.userId);
  }
}
