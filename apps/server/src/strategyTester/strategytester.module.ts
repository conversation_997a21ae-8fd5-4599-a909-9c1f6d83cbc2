import { Module } from '@nestjs/common';
import { BinanceApiModule } from '@app/binance-api';
import { DatabaseModule } from '@app/database';
import { WebsocketModule } from '../websocket/websocket.module';
import { StrategytesterResolver } from './strategytester.resolver';
import { StrategytesterService } from './strategytester.service';
import { AutoBotProcessorModule } from '@app/auto-bot-processor';
import { BullModule } from '@nestjs/bull';
import { GateioApiModule } from '@app/gateio-api';

@Module({
  imports: [
    BullModule.forRoot({
      redis: {
        host: '127.0.0.1',
        port: 6379,
      },
    }),
    BullModule.registerQueue(
      {
        name: 'allstrategies',
      },
      {
        settings: {
          stalledInterval: 0,
          maxStalledCount: 0,
          lockDuration: 3600000, // 1h
        },
      },
    ),
    BinanceApiModule,
    GateioApiModule,
    DatabaseModule,
    WebsocketModule,
    AutoBotProcessorModule,
  ],
  providers: [StrategytesterResolver, StrategytesterService],
  exports: [StrategytesterResolver, StrategytesterService],
})
export class StrategytesterModule {}
