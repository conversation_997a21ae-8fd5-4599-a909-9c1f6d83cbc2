import { Injectable } from '@nestjs/common';
import { InjectQueue } from '@nestjs/bull';
import { Queue } from 'bull';
import { DbStrategyTestService } from '@app/database/db-strategy-test/db-strategy-test.service';
import { DbStrategyAllTestService } from '@app/database/db-strategy-all-test/db-strategy-all-test.service';
import { Common } from '../../../../utils/Common';
import { DbWhiteListService } from '@app/database/db-white-list/db-white-list.service';
import { Strategies, StrategyIntervals } from '../../../../utils/Strategies';
import { DbUserService } from '@app/database/db-user/db-user.service';

@Injectable()
export class StrategytesterService {
  constructor(
    @InjectQueue('allstrategies') private allStrategiesQueue: Queue,
    private strategyTesterService: DbStrategyTestService,
    private strategyAllTestService: DbStrategyAllTestService,
    private whiteListService: DbWhiteListService,
    private dbUserService: DbUserService,
  ) {}

  public async getStrategyTests(userId: string) {
    const exchange = await this.dbUserService.getUserExchange(userId);
    return await this.strategyTesterService.dbGetStrategyTests(
      userId,
      exchange,
    );
  }

  public async getAllStrategyTests(userId: string): Promise<any> {
    const exchange = await this.dbUserService.getUserExchange(userId);
    return await this.strategyAllTestService.dbGetAllStrategyTests(
      userId,
      exchange,
    );
  }

  public async calcAllStrategy(
    args: GQL.ICalcAllStrategyOnMutationArguments,
    userId: string,
  ) {
    const invest = 1000;
    const endDate = new Date();
    const startDate = new Date(2023, 0, 1);
    const exchange = await this.dbUserService.getUserExchange(userId);

    const whitelist: string[] = (
      await this.whiteListService.dbGetWhiteListCoins(userId, exchange)
    )
      .map((x) => x.symbol)
      .sort((a, b) => a.localeCompare(b));

    let strategies = Strategies;

    if (args != null && args.strategy !== 'all') {
      strategies = [args.strategy];
    }

    for (const strategy of strategies) {
      let intervals = StrategyIntervals;
      if (strategy.includes('nesterov')) {
        intervals = [...StrategyIntervals];
      }

      for (const interval of intervals) {
        const newStrategy =
          await this.strategyAllTestService.dbCreateStrategyTest({
            userId: userId,
            interval: interval,
            strategy: strategy,
            invest: invest,
            symbols: whitelist,
            startDate: startDate,
            endDate: endDate,
            exchange: exchange,
          });

        await this.allStrategiesQueue.add(newStrategy, {
          removeOnFail: true,
          removeOnComplete: true,
        });
      }
    }
  }

  public async backtestRecalcMutation(userId: string) {
    try {
      await this.calcAllStrategy(null, userId);

      return new Common(true);
    } catch (e) {
      throw e;
    }
  }
}
