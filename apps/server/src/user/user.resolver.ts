import { Args, Mu<PERSON>, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { UserQuery } from '../../../../types/schema';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AuthUser, CurrentUser } from '../auth/jwt.user.decorator';
import { UserService } from './user.service';

@Resolver(UserQuery)
@UseGuards(JwtAuthGuard)
export class UserResolver {
  constructor(private userService: UserService) {}

  @Query()
  async user() {
    return {};
  }

  @ResolveField()
  public async saldo(
    @Args() args: GQL.ISaldoOnUserQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.userService.saldo(args, user.userId);
  }

  @ResolveField()
  public async saldoUsd(
    @Args() args: GQL.ISaldoUsdOnUserQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.userService.saldoUsd(args, user.userId);
  }

  @ResolveField()
  public async favorites(@CurrentUser() user: AuthUser) {
    return this.userService.favorites(user.userId);
  }

  @ResolveField()
  public async noticedCoins(@CurrentUser() user: AuthUser) {
    return this.userService.noticedCoins(user.userId);
  }

  @ResolveField()
  public async account(@CurrentUser() user: AuthUser) {
    return this.userService.account(user.userId);
  }

  @ResolveField()
  public async exchangeKeys(
    @Args() args: GQL.IExchangeKeysOnUserQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.userService.exchangeKeys(args, user.userId);
  }

  @ResolveField()
  public async splitview(@CurrentUser() user: AuthUser) {
    return this.userService.splitview(user.userId);
  }

  @ResolveField()
  public async logs(@Args() args: GQL.ILogsOnUserQueryArguments) {
    return this.userService.logs(args);
  }

  @Mutation()
  public async saveCurrentSaldo(@CurrentUser() user: AuthUser) {
    return this.userService.saveCurrentSaldo(user.userId);
  }

  @Mutation()
  public async saveSplitMutation(
    @Args() args: GQL.ISaveSplitMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.userService.saveSplitMutation(args, user.userId);
  }

  @Mutation()
  public async accountSettingsMutation(
    @Args() args: GQL.IAccountSettingsMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.userService.accountSettingsMutation(args, user.userId);
  }

  @Mutation()
  public async saveExchangeMutation(
    @Args() args: GQL.ISaveExchangeMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.userService.saveExchangeMutation(args, user.userId);
  }

  @Mutation()
  public async saveApiKeyMutation(
    @Args() args: GQL.ISaveApiKeyMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.userService.saveApiKeyMutation(args, user.userId);
  }

  @Mutation()
  public async saveFavoriteCoinMutation(
    @Args() args: GQL.ISaveFavoriteCoinMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.userService.saveFavoriteCoinMutation(args, user.userId);
  }

  @Mutation()
  public async saveNoticedCoinMutation(
    @Args() args: GQL.ISaveNoticedCoinMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.userService.saveNoticedCoinMutation(args, user.userId);
  }

  @Mutation()
  public async deleteNoticedCoinMutation(
    @Args() args: GQL.IDeleteNoticedCoinMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.userService.deleteNoticedCoinMutation(args, user.userId);
  }

  @Mutation()
  public async stopActiveCallMutation(
    @Args() args: GQL.IStopActiveCallMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.userService.stopActiveCallMutation(args, user.userId);
  }

  @Mutation()
  public async addActiveCallMutation(
    @Args() args: GQL.IAddActiveCallMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.userService.addActiveCallMutation(args, user.userId);
  }
}
