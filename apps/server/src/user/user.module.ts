import { Module } from '@nestjs/common';
import { BinanceApiModule } from '@app/binance-api';
import { DatabaseModule } from '@app/database';
import { UserResolver } from './user.resolver';
import { UserService } from './user.service';
import { GateioApiModule } from '@app/gateio-api';

@Module({
  imports: [BinanceApiModule, GateioApiModule, DatabaseModule],
  providers: [UserService, UserResolver],
  exports: [UserResolver, UserService],
})
export class UserModule {}
