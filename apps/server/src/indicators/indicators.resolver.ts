import { Args, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { IndicatorsService } from './indicators.service';
import { IndicatorsQuery } from '../../../../types/schema';
import { AuthUser, CurrentUser } from '../auth/jwt.user.decorator';

@Resolver(IndicatorsQuery)
@UseGuards(JwtAuthGuard)
export class IndicatorsResolver {
  constructor(private indicatorsService: IndicatorsService) {}

  @Query()
  async indicators() {
    return {};
  }

  @ResolveField()
  public async macd(
    @Args() args: GQL.IMacdOnIndicatorsQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.indicatorsService.macd(args, user.userId);
  }

  @ResolveField()
  public async macds(
    @Args() args: GQL.IMacdsOnIndicatorsQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.indicatorsService.macds(args, user.userId);
  }
}
