import { Module } from '@nestjs/common';
import { IndicatorsResolver } from './indicators.resolver';
import { IndicatorsService } from './indicators.service';
import { BinanceApiModule } from '@app/binance-api';
import { DatabaseModule } from '@app/database';
import { WebsocketModule } from '../websocket/websocket.module';
import { GateioApiModule } from '@app/gateio-api';

@Module({
  imports: [BinanceApiModule, GateioApiModule, DatabaseModule, WebsocketModule],
  providers: [IndicatorsResolver, IndicatorsService],
  exports: [IndicatorsResolver, IndicatorsService],
})
export class IndicatorsModule {}
