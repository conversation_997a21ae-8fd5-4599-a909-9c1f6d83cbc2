import { <PERSON>, Get, Param, Req, Re<PERSON> } from '@nestjs/common';
import { AuthService } from './auth.service';
import { confirmationLinkPrefix } from '../../../../utils/constants';
import Redis from 'ioredis';
import { ConfigService } from '@nestjs/config';

@Controller()
export class AuthController {
  constructor(
    private authService: AuthService,
    private config: ConfigService,
  ) {}

  @Get('/confirm/:id')
  async confirmId(@Param() params, @Req() req, @Res() res: any) {
    const { id } = params;
    const redis = new Redis({ host: this.config.get('REDIS_HOST') });
    const userId = await redis.get(confirmationLinkPrefix + id);

    if (userId) {
      await this.authService.confirmUserRegistration(userId);
      res.redirect(
        req.protocol + '://' + process.env.FRONTEND_HOST + '/confirmed',
      );
    } else {
      res.send('invalid link');
    }
  }
}
