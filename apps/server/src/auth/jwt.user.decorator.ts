import { createParamDecorator, ExecutionContext } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';

export const CurrentUser = createParamDecorator(
  (data: unknown, context: ExecutionContext) => {
    const ctx = GqlExecutionContext.create(context);
    const session = ctx.getContext().req.session;
    return {
      userId: session.userId,
      username: session.username,
    };
  },
);

export interface AuthUser {
  userId: string;
  username: string;
}
