import { ExecutionContext, Injectable } from '@nestjs/common';
import { GqlExecutionContext } from '@nestjs/graphql';
import { AuthGuard } from '@nestjs/passport';

@Injectable()
export class LocalAuthGuard extends AuthGuard('local') {
  getRequest(context: ExecutionContext) {
    const ctx = GqlExecutionContext.create(context);
    const request = ctx.getContext();
    const args = ctx.getArgs();
    request.body = {
      username: args.username,
      password: args.password,
    };

    return request;
  }

  async canActivate(context: ExecutionContext) {
    const result = (await super.canActivate(context)) as boolean;
    const ctx = GqlExecutionContext.create(context);
    const args = ctx.getArgs();
    const req = ctx.getContext().req;
    req.user = {
      username: args.username,
      password: args.password,
    };
    await super.logIn(req);
    return result;
  }
}
