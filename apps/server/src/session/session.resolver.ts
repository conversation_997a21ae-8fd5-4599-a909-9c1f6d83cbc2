import { Context, Query, Resolver } from '@nestjs/graphql';
import { Session } from '../../../../types/schema';

@Resolver(Session)
export class SessionResolver {
  @Query()
  async session(@Context() context) {
    if (context.req.isAuthenticated()) {
      const { userId, email, username } = context.req.session;
      return {
        id: userId,
        username: username,
        email: email,
      };
    }

    return null;
  }
}
