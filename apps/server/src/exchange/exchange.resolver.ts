import { Args, Mu<PERSON>, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { ExchangeService } from './exchange.service';
import { AuthUser, CurrentUser } from '../auth/jwt.user.decorator';
import { DailyStats, ExchangeQuery } from '../../../../types/schema';

@Resolver(ExchangeQuery)
@UseGuards(JwtAuthGuard)
export class ExchangeResolver {
  constructor(private exchangeService: ExchangeService) {}

  @Query()
  async exchange() {
    return {};
  }

  @ResolveField('accountInfo')
  public async accountInfo(
    @Args() args: GQL.IAccountInfoOnExchangeQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    const result = await this.exchangeService.accountInfo(
      args.noSmallAmount,
      user,
    );

    return result;
  }

  @ResolveField('prices')
  public async prices(@CurrentUser() user: AuthUser) {
    return this.exchangeService.prices(user.userId);
  }

  @ResolveField('mytrades')
  public async mytrades(
    @Args() args: GQL.IMytradesOnExchangeQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.exchangeService.mytrades(args, user.userId);
  }

  @ResolveField('openOrders')
  public async openOrders(
    @Args() args: GQL.IOpenOrdersOnExchangeQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.exchangeService.openOrders(args, user.userId);
  }

  @ResolveField('dailyStats')
  public async dailyStats(
    @Args() args: GQL.IDailyStatsOnExchangeQueryArguments,
    @CurrentUser() user: AuthUser,
  ): Promise<DailyStats> {
    return await this.exchangeService.dailyStats(args, user.userId);
  }

  @ResolveField()
  public async lastCandle(
    @Args() args: GQL.ILastCandleOnExchangeQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.exchangeService.lastCandle(args, user.userId);
  }

  @ResolveField()
  public async candles(
    @Args() args: GQL.ICandlesOnExchangeQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.exchangeService.candles(args, user);
  }

  @ResolveField()
  public async exchangeInfo(
    @Args() args: GQL.IExchangeInfoOnExchangeQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return await this.exchangeService.exchangeInfo(args, user.userId);
  }

  @ResolveField()
  public async comment(
    @Args() args: GQL.ICommentOnExchangeQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.exchangeService.comment(args, user.userId);
  }

  @ResolveField()
  public async orderbook(
    @Args() args: GQL.IOrderbookOnExchangeQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.exchangeService.orderbook(args, user.userId);
  }

  @ResolveField()
  public async lastOrders(
    @Args() args: GQL.ILastOrdersOnExchangeQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.exchangeService.lastOrders(args, user.userId);
  }

  @ResolveField()
  public async topVolumeCoins(
    @Args() args: GQL.ITopVolumeCoinsOnExchangeQueryArguments,
  ) {
    return this.exchangeService.topVolumeCoins(args);
  }

  @ResolveField()
  public async symbolTicker(
    @Args() args: GQL.ISymbolTickerOnExchangeQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.exchangeService.symbolTicker(args, user.userId);
  }

  @ResolveField()
  public async currentPrice(
    @Args() args: GQL.ICurrentPriceOnExchangeQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.exchangeService.currentPrice(args, user.userId);
  }

  @Mutation()
  public async orderMutation(
    @Args() args: GQL.IOrderMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.exchangeService.orderMutation(args, user.userId);
  }

  @Mutation()
  public async cancelOrderMutation(
    @Args() args: GQL.ICancelOrderMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.exchangeService.cancelOrderMutation(args, user.userId);
  }

  @Mutation()
  public async commentMutation(
    @Args() args: GQL.ICommentMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.exchangeService.commentMutation(args, user.userId);
  }
}
