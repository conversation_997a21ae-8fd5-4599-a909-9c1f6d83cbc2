import { Modu<PERSON> } from '@nestjs/common';
import { BinanceApiModule } from '@app/binance-api';
import { DatabaseModule } from '@app/database';
import { WebsocketModule } from '../websocket/websocket.module';
import { BotResolver } from './bot.resolver';
import { BotService } from './bot.service';
import { AutoBotProcessorModule } from '@app/auto-bot-processor';
import { BullModule } from '@nestjs/bull';
import { GateioApiModule } from '@app/gateio-api';

@Module({
  imports: [
    BinanceApiModule,
    GateioApiModule,
    DatabaseModule,
    WebsocketModule,
    AutoBotProcessorModule,
    BullModule.forRoot({
      redis: {
        host: '127.0.0.1',
        port: 6379,
      },
    }),
    BullModule.registerQueue(
      {
        name: 'indicators',
      },
      {
        settings: {
          stalledInterval: 0,
          maxStalledCount: 0,
          lockDuration: 3600000, // 1h
        },
      },
    ),
  ],
  providers: [BotResolver, BotService],
  exports: [BotResolver, BotService],
})
export class BotModule {}
