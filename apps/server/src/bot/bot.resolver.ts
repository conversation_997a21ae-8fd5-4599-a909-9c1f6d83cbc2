import { Args, Mu<PERSON>, Query, ResolveField, Resolver } from '@nestjs/graphql';
import { BotQuery } from '../../../../types/schema';
import { UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { AuthUser, CurrentUser } from '../auth/jwt.user.decorator';
import { BotService } from './bot.service';
import { DbIndicatorDocument } from '@app/database/db-indicator/db-indicator.model';

@Resolver(BotQuery)
@UseGuards(JwtAuthGuard)
export class BotResolver {
  constructor(private botService: BotService) {}

  @Query()
  async bot() {
    return {};
  }

  @ResolveField('marketBots')
  public async marketBots(@CurrentUser() user: AuthUser) {
    return this.botService.marketBots(user.userId);
  }

  @ResolveField('formInfo')
  public async formInfo() {
    return this.botService.formInfo();
  }

  @ResolveField('signals')
  public async signals(
    @Args() args: GQL.ISignalsOnBotQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.signals(args, user.userId);
  }

  @ResolveField('signalMarkets')
  public async signalMarkets(
    @Args() args: GQL.ISignalMarketsOnBotQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.signalMarkets(args, user.userId);
  }

  @ResolveField('profit')
  public async profit(
    @Args() args: GQL.IProfitOnBotQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.profit(args, user.userId);
  }

  @ResolveField('strategyProfits')
  public async strategyProfits(
    @Args() args: GQL.IStrategyProfitsOnBotQueryArguments,
    @CurrentUser() user: AuthUser,
  ): Promise<GQL.IBotProfitPerDay[]> {
    return this.botService.strategyProfits(args, user.userId);
  }

  @ResolveField('profitPerDayForStrategy')
  public async profitPerDayForStrategy(
    @Args() args: GQL.IProfitPerDayForStrategyOnBotQueryArguments,
    @CurrentUser() user: AuthUser,
  ): Promise<GQL.IBotProfitPerDay> {
    return this.botService.profitPerDayForStrategy(args, user.userId);
  }

  @ResolveField('profitPerMonth')
  public async profitPerMonth(
    @Args() args: GQL.IProfitPerMonthOnBotQueryArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.profitPerMonth(args, user.userId);
  }

  @ResolveField('botOrders')
  public async botOrders(@CurrentUser() user: AuthUser) {
    return this.botService.botOrders(user.userId);
  }

  @ResolveField('topCoins')
  public async topCoins() {
    return this.botService.topCoins();
  }

  @ResolveField('getIndicators')
  public async getIndicators(
    @Args() args: GQL.IGetIndicatorsOnBotQueryArguments,
    @CurrentUser() user: AuthUser,
  ): Promise<DbIndicatorDocument[]> {
    return this.botService.getIndicators(args, user.userId);
  }

  @ResolveField('getAllIndicators')
  public async getAllIndicators(@CurrentUser() user: AuthUser) {
    return this.botService.getAllIndicators(user.userId);
  }

  @ResolveField('autobot')
  public async autobot(@CurrentUser() user: AuthUser) {
    return this.botService.autobot(user.userId);
  }

  @ResolveField('profitGraph')
  public async profitGraph(@CurrentUser() user: AuthUser) {
    return this.botService.profitGraph(user.userId);
  }

  @ResolveField('lastBotOrders')
  public async lastBotOrders(@CurrentUser() user: AuthUser) {
    return this.botService.lastBotOrders(user.userId);
  }

  @ResolveField('suggestList')
  public async suggestList() {
    return this.botService.suggestList();
  }

  @ResolveField('fearAltIndex')
  public async fearAltIndex() {
    return this.botService.fearAltIndex();
  }

  @ResolveField()
  public async whitelist(@CurrentUser() user: AuthUser) {
    return this.botService.whitelist(user.userId);
  }

  @Mutation()
  public async saveWhiteListCoinMutation(
    @Args() args: GQL.ISaveWhiteListCoinMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.saveWhiteListCoinMutation(args, user.userId);
  }

  @Mutation()
  public async deleteWhiteListCoinMutation(
    @Args() args: GQL.IDeleteWhiteListCoinMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.deleteWhiteListCoinMutation(args, user.userId);
  }

  @Mutation()
  public async disableWhiteListCoinMutation(
    @Args() args: GQL.IDisableWhiteListCoinMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.disableWhiteListCoinMutation(args, user.userId);
  }

  @Mutation()
  public async transformToAutoBotMutation(
    @Args() args: GQL.ITransformToAutoBotMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.transformToAutoBotMutation(args, user.userId);
  }

  @Mutation()
  public async createAutoBot(
    @Args() args: GQL.ICreateAutoBotOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.createAutoBot(args, user.userId);
  }

  @Mutation()
  public async modifyNumBotsMutation(
    @Args() args: GQL.IModifyNumBotsMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.modifyNumBotsMutation(args, user.userId);
  }

  @Mutation()
  public async autoUsdPerCoinMutation(
    @Args() args: GQL.IAutoUsdPerCoinMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.autoUsdPerCoinMutation(args, user.userId);
  }

  @Mutation()
  public async startAutoBotMutation(
    @Args() args: GQL.IStartStopBotMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.startAutoBotMutation(args, user.userId);
  }

  @Mutation()
  public async stopAutoBotMutation(
    @Args() args: GQL.IStopAutoBotMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.stopAutoBotMutation(args, user.userId);
  }

  @Mutation()
  public async removeAutoBotMutation(
    @Args() args: GQL.IRemoveAutoBotMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.removeAutoBotMutation(args, user.userId);
  }

  @Mutation()
  public async shortMarketBotMutation(
    @Args() args: GQL.IShortMarketBotMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.shortMarketBotMutation(user.userId, args);
  }

  @Mutation()
  public async sellAllMutation(
    @Args() args: GQL.ISellAllMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.sellAllMutation(user.userId);
  }

  @Mutation()
  public async buyNowMutation(
    @Args() args: GQL.IBuyNowMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.buyNowMutation(user.userId);
  }

  @Mutation()
  public async strategyTestAdd(
    @Args() args: GQL.IStrategyTestAddOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.strategyTestAdd(args, user.userId);
  }

  @Mutation()
  public async addNewBotMutation(
    @Args() args: GQL.IAddNewBotMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.addNewBotMutation(args, user.userId);
  }

  @Mutation()
  public async startStopBotMutation(
    @Args() args: GQL.IStartStopBotMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.startStopBotMutation(args, user.userId);
  }

  @Mutation()
  public async deleteBotMutation(
    @Args() args: GQL.IDeleteBotMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.deleteBotMutation(args, user.userId);
  }

  @Mutation()
  public async buyWhiteListCoinMutation(
    @Args() args: GQL.IBuyWhiteListCoinMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.buyWhiteListCoinMutation(args, user.userId);
  }

  @Mutation()
  public async sellInMinusMutation(
    @Args() args: GQL.ISellInMinusMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.sellInMinusMutation(args, user.userId);
  }

  @Mutation()
  public async selectBotModeMutation(
    @Args() args: GQL.ISelectBotModeMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.selectBotModeMutation(args, user.userId);
  }

  @Mutation()
  public async selectBotRefillWhitelist(
    @Args() args: GQL.ISelectBotRefillWhitelistOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.selectBotRefillWhitelist(args, user.userId);
  }

  @Mutation()
  public async enableBuyOnlyAOPlusMutation(
    @Args() args: GQL.IEnableBuyOnlyAOPlusMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.enableBuyOnlyAOPlusMutation(args, user.userId);
  }

  @Mutation()
  public async fillPortfolioAfterSellMutation(
    @Args() args: GQL.IFillPortfolioAfterSellMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.fillPortfolioAfterSellMutation(args, user.userId);
  }

  @Mutation()
  public async toggleBtcAoCrossMutation(
    @Args() args: GQL.IToggleBtcAoCrossMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.toggleBtcAoCrossMutation(args, user.userId);
  }

  @Mutation()
  public async toggleTakeProfitMutation(
    @Args() args: GQL.IToggleTakeProfitMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.toggleTakeProfitMutation(args, user.userId);
  }

  @Mutation()
  public async toggleStopLossMutation(
    @Args() args: GQL.IToggleStopLossMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.toggleStopLossMutation(args, user.userId);
  }

  @Mutation()
  public async setAutobotTakeProfitMutation(
    @Args() args: GQL.ISetAutobotTakeProfitMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.setAutobotTakeProfitMutation(args, user.userId);
  }

  @Mutation()
  public async setAutobotStopLossMutation(
    @Args() args: GQL.ISetAutobotStopLossMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.setAutobotStopLossMutation(args, user.userId);
  }

  @Mutation()
  public async onlyFromWhitelistMutation(
    @Args() args: GQL.IOnlyFromWhitelistMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.onlyFromWhitelistMutation(args, user.userId);
  }

  @Mutation()
  public async btcOnlyMutation(
    @Args() args: GQL.IBtcOnlyMutationOnMutationArguments,
    @CurrentUser() user: AuthUser,
  ) {
    return this.botService.btcOnlyMutation(args, user.userId);
  }
}
