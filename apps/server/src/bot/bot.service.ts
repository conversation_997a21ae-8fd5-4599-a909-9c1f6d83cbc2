import { Injectable } from '@nestjs/common';
import { DbKeyService } from '@app/database/db-key/db-key.service';
import { BinanceOpenOrdersService } from '@app/binance-api/binance-open-orders/binance-open-orders.service';
import { DbBotOrderService } from '@app/database/db-bot-order/db-bot-order.service';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbMarketBotService } from '@app/database/db-market-bot/db-market-bot.service';
import { DbAutoBotService } from '@app/database/db-auto-bot/db-auto-bot.service';
import { DbStrategyTestService } from '@app/database/db-strategy-test/db-strategy-test.service';
import { DbMarketBotTradeService } from '@app/database/db-market-bot-trade/db-market-bot-trade.service';
import { descend, last, prop, sortWith } from 'ramda';
import { Common } from '../../../../utils/Common';
import PriceCalculation from '../../../../utils/PriceCalculation';
import { DbFearAltIndexService } from '@app/database/db-fear-alt-index/db-fear-alt-index.service';
import { BuySellService } from '@app/auto-bot-processor/buy-sell/buy-sell.service';
import { DbIndicatorService } from '@app/database/db-indicator/db-indicator.service';
import { DbWhiteListService } from '@app/database/db-white-list/db-white-list.service';
import {
  DbIndicator,
  DbIndicatorDocument,
} from '@app/database/db-indicator/db-indicator.model';
import { DbBotProfitsService } from '@app/database/db-bot-profits/db-bot-profits.service';
import { addMonth, dayStart } from '@formkit/tempo';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import { DbExchangeInfoService } from '@app/database/db-exchange-info/db-exchange-info.service';
import { shuffle } from 'ioredis/built/utils';
import { DbCandlesticksService } from '@app/database/db-candlesticks/db-candlesticks.service';
import { BinanceAccountInfoService } from '@app/binance-api/binance-account-info/binance-account-info.service';
import { DbUserService } from '@app/database/db-user/db-user.service';
import { Exchange } from '../../../../types/exchanges';
import { GateioAccountInfoService } from '@app/gateio-api/gateio-account-info/gateio-account-info.service';
import { GateioOpenOrdersService } from '@app/gateio-api/gateio-open-orders/gateio-open-orders.service';
import IBotProfit = GQL.IBotProfit;
import IBotSignal = GQL.IBotSignal;
import IBotProfitPerDay = GQL.IBotProfitPerDay;
import IOpenOrder = GQL.IOpenOrder;

@Injectable()
export class BotService {
  constructor(
    private keyService: DbKeyService,
    private binanceOpenOrdersService: BinanceOpenOrdersService,
    private gateioOpenOrdersService: GateioOpenOrdersService,
    private botOrderService: DbBotOrderService,
    private logger: DbLogService,
    private marketBotService: DbMarketBotService,
    private autobotService: DbAutoBotService,
    private strategyTesterService: DbStrategyTestService,
    private marketBotTradeService: DbMarketBotTradeService,
    private fearAltIndexService: DbFearAltIndexService,
    private dbIndicatorService: DbIndicatorService,
    private coinPriceService: DbCoinPriceService,
    private buysellService: BuySellService,
    private whitelistService: DbWhiteListService,
    private dbBotProfitsService: DbBotProfitsService,
    private dbCoinPriceService: DbCoinPriceService,
    private exchangeInfo: DbExchangeInfoService,
    private dbCandleService: DbCandlesticksService,
    private binanceAccountInfoService: BinanceAccountInfoService,
    private userService: DbUserService,
    private gateioAccountInfoService: GateioAccountInfoService,
  ) {}

  public addNewBotMutation = async (
    args: GQL.IAddNewBotMutationOnMutationArguments,
    userId: string,
  ) => {
    try {
      const exchange = await this.userService.getUserExchange(userId);
      await this.marketBotService.dbAddNewMarketBot(userId, args, exchange);
      return {
        ok: true,
      };
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  };

  public startStopBotMutation = async (
    args: GQL.IStartStopBotMutationOnMutationArguments,
    userId: string,
  ) => {
    try {
      const exchange = await this.userService.getUserExchange(userId);
      await this.marketBotService.dbStartStopMarketBot(userId, args, exchange);
      return {
        ok: true,
      };
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  };

  public deleteBotMutation = async (
    args: GQL.IDeleteBotMutationOnMutationArguments,
    userId: string,
  ) => {
    try {
      await this.marketBotService.dbDeleteMarketBotByIdAndUserId(
        args.id,
        userId,
      );
      return {
        ok: true,
      };
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  };

  public sellInMinusMutation = async (
    args: GQL.ISellInMinusMutationOnMutationArguments,
    userId: string,
  ) => {
    try {
      await this.autobotService.dbAutobotSetSellInMinus(args, userId);
      return {
        ok: true,
      };
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  };

  public selectBotModeMutation = async (
    args: GQL.ISelectBotModeMutationOnMutationArguments,
    userId: string,
  ): Promise<any> => {
    try {
      await this.autobotService.dbAutobotSetMode(args, userId);
      return {
        ok: true,
      };
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  };

  public selectBotRefillWhitelist = async (
    args: GQL.ISelectBotRefillWhitelistOnMutationArguments,
    userId: string,
  ): Promise<any> => {
    try {
      await this.autobotService.dbAutobotSetRefillWhitelist(args, userId);
      return {
        ok: true,
      };
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  };

  enableBuyOnlyAOPlusMutation = async (
    args: GQL.IEnableBuyOnlyAOPlusMutationOnMutationArguments,
    userId: string,
  ): Promise<any> => {
    try {
      await this.autobotService.dbAutobotSetOnlyAOPlus(args, userId);
      return {
        ok: true,
      };
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  };

  fillPortfolioAfterSellMutation = async (
    args: GQL.IFillPortfolioAfterSellMutationOnMutationArguments,
    userId: string,
  ): Promise<any> => {
    try {
      await this.autobotService.dbAutobotSetFillPortfolioAfterSell(
        args,
        userId,
      );
      return {
        ok: true,
      };
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  };

  toggleBtcAoCrossMutation = async (
    args: GQL.IToggleBtcAoCrossMutationOnMutationArguments,
    userId: string,
  ): Promise<any> => {
    try {
      await this.autobotService.dbAutobotSetBtcAoCross(args, userId);
      return {
        ok: true,
      };
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  };

  public toggleTakeProfitMutation = async (
    args: GQL.IToggleTakeProfitMutationOnMutationArguments,
    userId: string,
  ): Promise<any> => {
    try {
      await this.autobotService.dbAutobotToggleTakeProfit(args, userId);
      return {
        ok: true,
      };
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  };

  public toggleStopLossMutation = async (
    args: GQL.IToggleStopLossMutationOnMutationArguments,
    userId: string,
  ): Promise<any> => {
    try {
      await this.autobotService.dbAutobotToggleStoploss(args, userId);
      return {
        ok: true,
      };
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  };

  public setAutobotTakeProfitMutation = async (
    args: GQL.ISetAutobotTakeProfitMutationOnMutationArguments,
    userId: string,
  ): Promise<any> => {
    try {
      await this.autobotService.dbAutobotSetTakeProfit(args, userId);
      return {
        ok: true,
      };
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  };

  public setAutobotStopLossMutation = async (
    args: GQL.ISetAutobotStopLossMutationOnMutationArguments,
    userId: string,
  ): Promise<any> => {
    try {
      await this.autobotService.dbAutobotSetStoploss(args, userId);
      return {
        ok: true,
      };
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  };

  public onlyFromWhitelistMutation = async (
    args: GQL.IOnlyFromWhitelistMutationOnMutationArguments,
    userId: string,
  ): Promise<any> => {
    try {
      await this.autobotService.dbAutobotSetOnlyFromWhitelist(args, userId);
      return {
        ok: true,
      };
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  };

  public btcOnlyMutation = async (
    args: GQL.IBtcOnlyMutationOnMutationArguments,
    userId: string,
  ): Promise<any> => {
    try {
      await this.autobotService.dbAutobotSetOnlyBtcMode(args, userId);
      return {
        ok: true,
      };
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  };

  public marketBots = async (userId: string): Promise<any> => {
    const exchange = await this.userService.getUserExchange(userId);

    const marketBots = await this.marketBotService.dbGetMarketBotsFromUser(
      userId,
      exchange,
    );

    for (const marketBot of marketBots) {
      const trades =
        (await this.marketBotTradeService.dbGetMarketBotTrades(
          marketBot._id.toString(),
        )) || [];

      const pairs = [];
      let currentLong = null;

      for (const trade of trades) {
        const { position } = trade;

        switch (position) {
          case 'short':
            if (currentLong !== null) {
              pairs.push({
                entryDate: currentLong.tradeDate,
                exitDate: trade.tradeDate,
                entryPrice: currentLong.price,
                exitPrice: trade.price,
                duration:
                  new Date(trade.tradeDate).getTime() -
                  new Date(currentLong.tradeDate).getTime(),
                profit: PriceCalculation.toPercentGain(
                  currentLong.price,
                  trade.price,
                ),
                predictionPrice: currentLong.predictionPrice,
                id: Math.random().toString(),
              });
              currentLong = null;
            }

            break;
          case 'long':
            currentLong = trade;
            break;
        }
      }

      // marketBot.trades = trades;
      // marketBot.roundTrips = pairs;
      marketBot.id = marketBot._id.toString();
    }

    return marketBots;
  };

  public formInfo = async (): Promise<any> => {
    return {
      strategies: [
        {
          name: 'nesterov',
          description: 'Neuralnet nesterov ao 15m Strategy',
          interval: '15',
          defaultParams: JSON.stringify({}),
        },
        {
          name: 'rsi',
          description: 'RSI 1h Strategy',
          interval: '60',
          defaultParams: JSON.stringify({
            RSI: {
              optInTimePeriod: 14,
            },
            thresholds: {
              RSIhigh: 70,
              RSIlow: 30,
              persistance: 1,
            },
          }),
        },
        {
          name: 'rsi_macd',
          description: 'RSI + MACD 1h Strategy',
          interval: '60',
          defaultParams: JSON.stringify({
            MACD: {
              optInFastPeriod: 5,
              optInSlowPeriod: 34,
              optInSignalPeriod: 5,
            },
            RSI: {
              optInTimePeriod: 14,
            },
            thresholds: {
              RSIhigh: 70,
              RSIlow: 30,
              MACDhigh: 0,
              MACDlow: 0,
              persistance: 1,
            },
          }),
        },
      ],
    };
  };

  public signals = async (
    args: GQL.ISignalsOnBotQueryArguments,
    userId: string,
  ): Promise<GQL.IBotSignal[]> => {
    const exchange = await this.userService.getUserExchange(userId);

    return await this.getAllSignalsFromIndicators(args, exchange);
  };

  public signalMarkets = async (
    args: GQL.ISignalMarketsOnBotQueryArguments,
    userId: string,
  ): Promise<GQL.IBotSignal[]> => {
    const exchange = await this.userService.getUserExchange(userId);

    return await this.getAllMarketSignalsFromIndicators(args, exchange);
  };

  private async getAllSignalsFromIndicators(
    args: GQL.ISignalsOnBotQueryArguments,
    exchange: string,
  ) {
    const allSignalsFromIndicators =
      await this.dbIndicatorService.getAllSignalsFromIndicators(args, exchange);

    const result = [];
    for (const signal of allSignalsFromIndicators) {
      const r = {
        id: signal._id.toString(),
        price: signal.price,
        advice: signal.action,
        strategy: signal.indicator,
        symbol: signal.symbol,
        interval: signal.interval,
        profit: signal.profit,
        timestamp: signal.timestamp,
        currentDiff: signal.profit,
        candleTime: signal.candleTime ? signal.candleTime : null,
      } as IBotSignal;

      result.push(r);
    }

    const lastResult = last(result);
    if (lastResult && lastResult.advice == 'long') {
      const currentPrice = await this.dbCoinPriceService.dbGetCoinPrice(
        lastResult.symbol,
        exchange,
      );

      lastResult.currentDiff = PriceCalculation.toPercentGain(
        lastResult.price,
        currentPrice,
      );
    }

    return result;
  }

  private async getAllMarketSignalsFromIndicators(
    args: GQL.ISignalMarketsOnBotQueryArguments,
    exchange: string,
  ): Promise<GQL.IBotSignal[]> {
    let symbols =
      await this.exchangeInfo.getUsdtSymbolsWithoutExcluded(exchange);

    if (args.symbol) {
      symbols = [args.symbol];
    }

    const allSignalsFromIndicators =
      await this.dbIndicatorService.getAllMarketSignalsFromIndicators(
        args.strategy,
        args.interval,
        symbols,
        exchange,
      );

    const result = [];
    const prices = await this.dbCoinPriceService.dbGetCoinPricesBySymbols(
      symbols,
      exchange,
    );

    for (const signal of allSignalsFromIndicators) {
      const r = {
        id: signal._id.toString(),
        price: signal.price,
        advice: signal.action,
        strategy: signal.indicator,
        symbol: signal.symbol,
        interval: signal.interval,
        profit: signal.profit,
        timestamp: signal.timestamp,
        currentDiff: signal.profit,
        candleTime: signal.candleTime ? signal.candleTime : null,
      } as IBotSignal;

      if (r && r.advice == 'long') {
        const currentPrice = prices.find(
          (x) => x.symbol == signal.symbol,
        ).price;

        r.currentDiff = PriceCalculation.toPercentGain(
          signal.price,
          currentPrice,
        );
      } else {
        r.currentDiff = null;
      }
      result.push(r);
    }

    return result;
  }

  async profit(
    args: GQL.IProfitOnBotQueryArguments,
    userId: string,
  ): Promise<GQL.IBotProfit> {
    const exchange = await this.userService.getUserExchange(userId);

    const result = await this.dbBotProfitsService.dbGetProfit(
      args.symbol,
      args.strategy,
      args.interval,
      exchange,
    );

    return {
      profit: result?.profit,
      tradesPos: result?.tradesPos,
      tradesNeg: result?.tradesNeg,
    } as IBotProfit;
  }

  public async strategyProfits(
    _: GQL.IStrategyProfitsOnBotQueryArguments,
    userId: string,
  ): Promise<GQL.IBotProfitPerDay[]> {
    const exchange = await this.userService.getUserExchange(userId);
    const strategies =
      await this.dbBotProfitsService.dbGetTotalProfitForStrategies(exchange);

    const result = [];
    for (const strategy of strategies) {
      result.push({
        strategy: strategy.strategy,
        interval: strategy.interval,
        totalProfit: strategy.profit,
        tradesPos: strategy.tradesPos,
        tradesNeg: strategy.tradesNeg,
        dailyProfits: [],
        currentDiff: 0,
      } as IBotProfitPerDay);
    }

    return result;
  }

  async profitPerDayForStrategy(
    args: GQL.IProfitPerDayForStrategyOnBotQueryArguments,
    userId: string,
  ): Promise<GQL.IBotProfitPerDay> {
    const now = new Date();
    let fromDate = dayStart(addMonth(now, -1));
    const exchange = await this.userService.getUserExchange(userId);

    const resultStrategy =
      await this.dbBotProfitsService.dbGetStrategyProfitsFromDate(
        fromDate,
        args.strategy,
        args.interval,
        exchange,
      );

    const dailyProfits = resultStrategy.map((x) => {
      return {
        day: x._id.profitForDay,
        dateStr: x._id.profitForDay.toString(),
        profit: x.total,
      };
    });

    return {
      strategy: args.strategy,
      interval: args.interval,
      dailyProfits: dailyProfits as GQL.IBotProfits[],
    } as GQL.IBotProfitPerDay;

    // const signalsFromIndicators = await this.getAllSignalsFromIndicators({
    //   symbol: '',
    //   strategy: strategy,
    //   interval: interval,
    // });
    //
    // strategyResult.currentDiff = signalsFromIndicators
    //   .filter((x) => x.advice === 'long')
    //   .reduce((acc, curr) => acc + curr.currentDiff, 0);
  }

  async profitPerMonth(
    args: GQL.IProfitPerMonthOnBotQueryArguments,
    userId: string,
  ): Promise<GQL.IBotProfitPerMonthResult> {
    const exchange = await this.userService.getUserExchange(userId);
    const now = new Date();
    let fromDate = dayStart(addMonth(now, -12));

    const result: GQL.IBotProfitPerMonthResult = {
      interval: args.interval,
      strategy: args.strategy,
      monthlyProfits: [],
      totalProfit: 0,
      tradesNeg: 0,
      tradesPos: 0,
      __typename: null,
    };

    for (let i = 0; i < 12; i++) {
      const from = addMonth(fromDate, i);
      const to = addMonth(fromDate, i + 1);

      const queryResult =
        await this.dbBotProfitsService.dbGetStrategyProfitsFromDateToDate(
          from,
          to,
          args.strategy,
          args.interval,
          exchange,
        );

      const resultForMonth = queryResult.reduce(
        (acc, result) => acc + result.total,
        0,
      );

      result.monthlyProfits.push({
        month: from.toISOString(),
        profit: resultForMonth,
        monthStr: from.toISOString(),
      } as GQL.IBotProfitsForMonth);
    }

    return result;
  }

  async botOrders(userId: string): Promise<GQL.IOpenOrder[]> {
    const exchange = await this.userService.getUserExchange(userId);
    const botOrders = await this.botOrderService.dbGetBotOrders(
      userId,
      false,
      exchange,
    );
    const apiKey = await this.keyService.getApiKey(userId, exchange);

    switch (exchange) {
      case Exchange.BINANCE:
        let resultBinance: any =
          await this.binanceOpenOrdersService.getOpenOrders(apiKey, null);

        const mappedBotOrdersBinance = botOrders
          .filter((x) => !x.response)
          .map((x) => ({
            symbol: x.symbol,
            orderId: x._id.toString(),
            price: x.price,
            status: x.status,
            type: x.type,
            origQty: x.amount,
            executedQty: x.response ? x.response.executedQty : 0,
            time: new Date(x.addTimestamp).getTime(),
          }));

        resultBinance = resultBinance.concat(mappedBotOrdersBinance);

        return resultBinance.map((x: GQL.IOpenOrder) => ({
          ...x,
          _id: x.orderId.toString(),
        }));

      case Exchange.GATEIO:
        let resultGateio: any =
          await this.gateioOpenOrdersService.getOpenOrders(apiKey, null);

        const mappedBotOrdersGateio = botOrders
          .filter((x) => !x.response)
          .map((x) => ({
            symbol: x.symbol,
            orderId: x._id.toString(),
            price: x.price,
            status: x.status,
            type: x.type,
            origQty: x.amount,
            executedQty: x.response ? x.response.executedQty : 0,
            time: new Date(x.addTimestamp).getTime(),
          }));

        resultGateio = resultGateio.concat(mappedBotOrdersGateio);

        return resultGateio.map((x: IOpenOrder) => ({
          ...x,
          _id: x.orderId.toString(),
        }));
      default:
        return [];
    }
  }

  public topCoins = async () => {
    return null;
  };

  async strategyTestAdd(
    args: GQL.IStrategyTestAddOnMutationArguments,
    userId: string,
  ) {
    try {
      const exchange = await this.userService.getUserExchange(userId);

      await this.strategyTesterService.dbCreateStrategyModel(
        args,
        userId,
        exchange,
      );

      return new Common(true);
    } catch (e) {
      throw e;
    }
  }

  public async getIndicators(
    args: GQL.IGetIndicatorsOnBotQueryArguments,
    userId: string,
  ): Promise<DbIndicatorDocument[]> {
    const exchange = await this.userService.getUserExchange(userId);

    return this.dbIndicatorService.getIndicatorsForSymbol(
      args.symbol,
      args.indicator,
      args.interval,
      exchange,
    );
  }

  public async getAllIndicators(userId: string) {
    const exchange = await this.userService.getUserExchange(userId);

    return this.dbIndicatorService.getAllIndicators(500, exchange);
  }

  public async whitelist(userId: string) {
    const exchange = await this.userService.getUserExchange(userId);
    const whitelist = await this.whitelistService.dbGetAllWhiteListCoins(
      userId,
      exchange,
    );
    const autobot = await this.autobotService.dbGetPlainAutobotByUser(
      userId,
      exchange,
    );

    const result: GQL.IWhiteListCoin[] = [];

    for (const element of whitelist) {
      const { symbol } = element;
      let lastIndicator: DbIndicator = null;

      lastIndicator = await this.dbIndicatorService.getLastIndicatorAction(
        symbol,
        autobot.interval,
        autobot.strategy,
        autobot.exchange,
      );

      const whiteListCoin: GQL.IWhiteListCoin = {
        id: element._id.toString(),
        disabled: element.disabled,
        symbol: element.symbol,
        timestamp: element.timestamp,
        autoAdded: element.autoAdded,
        indicator: null,
        __typename: null,
      };

      if (lastIndicator) {
        whiteListCoin.indicator = { ...lastIndicator, __typename: null };
      }

      result.push(whiteListCoin);
    }

    return result;
  }

  public async saveWhiteListCoinMutation(
    args: GQL.ISaveWhiteListCoinMutationOnMutationArguments,
    userId: string,
  ) {
    try {
      const exchange = await this.userService.getUserExchange(userId);
      return await this.whitelistService.dbAddWhiteListCoin(
        userId,
        args,
        exchange,
      );
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  }

  public async deleteWhiteListCoinMutation(
    args: GQL.IDeleteWhiteListCoinMutationOnMutationArguments,
    userId: string,
  ) {
    try {
      const exchange = await this.userService.getUserExchange(userId);
      return await this.whitelistService.dbDeleteWhiteListCoin(
        userId,
        args,
        exchange,
      );
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  }

  public async disableWhiteListCoinMutation(
    args: GQL.IDisableWhiteListCoinMutationOnMutationArguments,
    _: string,
  ) {
    try {
      return await this.whitelistService.dbDisableWhiteListCoin(args);
    } catch (e) {
      return {
        ok: false,
        error: e,
      };
    }
  }

  public createAutoBot = async (
    args: GQL.ICreateAutoBotOnMutationArguments,
    userId: string,
  ): Promise<Common> => {
    try {
      const exchange = await this.userService.getUserExchange(userId);
      await this.autobotService.dbCreateAutoBot(args, userId, exchange);
      return new Common(true);
    } catch (e) {
      return new Common(false, e);
    }
  };

  modifyNumBotsMutation = async (
    args: GQL.IModifyNumBotsMutationOnMutationArguments,
    userId: string,
  ) => {
    try {
      await this.autobotService.dbModifyNumBotsAutoBot(args, userId);
      return new Common(true);
    } catch (e) {
      return new Common(false, e);
    }
  };

  async autoUsdPerCoinMutation(args: any, userId: string): Promise<Common> {
    try {
      await this.autobotService.dbModifyAutoUsdPerCoinAutoBot(args, userId);
      return new Common(true);
    } catch (e) {
      return new Common(false, e);
    }
  }

  async startAutoBotMutation(
    args: GQL.IStartStopBotMutationOnMutationArguments,
    userId: string,
  ) {
    try {
      await this.autobotService.dbStartAutoBot(args, userId);
      return new Common(true);
    } catch (e) {
      return new Common(false, e);
    }
  }

  async stopAutoBotMutation(
    args: GQL.IStopAutoBotMutationOnMutationArguments,
    userId: string,
  ) {
    try {
      await this.autobotService.dbStopAutoBot(args, userId);
      return new Common(true);
    } catch (e) {
      return new Common(false, e);
    }
  }

  async removeAutoBotMutation(
    args: GQL.IRemoveAutoBotMutationOnMutationArguments,
    userId: string,
  ) {
    try {
      await this.autobotService.dbRemoveAutoBot(args, userId);
      return new Common(true);
    } catch (e) {
      return new Common(false, e);
    }
  }

  async shortMarketBotMutation(
    userId: string,
    args: GQL.IShortMarketBotMutationOnMutationArguments,
  ) {
    const exchange = await this.userService.getUserExchange(userId);
    const autobot = await this.autobotService.dbGetPlainAutobotByUser(
      userId,
      exchange,
    );

    if (autobot) {
      const marketBot =
        await this.marketBotService.dbGetMarketBotForAutobotAndSymbol(
          autobot._id.toString(),
          args.symbol,
        );

      let accountInfo = null;
      switch (autobot.exchange) {
        case Exchange.BINANCE:
          accountInfo =
            await this.binanceAccountInfoService.getAccountInfoWithRetry(
              autobot.userId,
            );
          break;
        case Exchange.GATEIO:
          accountInfo =
            await this.gateioAccountInfoService.getAccountInfoWithRetry(
              autobot.userId,
            );
          break;
      }

      await this.buysellService.processSell(marketBot, autobot, accountInfo);
      return new Common(true);
    }

    return new Common(false);
  }

  async sellAllMutation(userId: string) {
    try {
      const exchange = await this.userService.getUserExchange(userId);
      const autobot = await this.autobotService.dbGetPlainAutobotByUser(
        userId,
        exchange,
      );
      const marketBots = await this.marketBotService.dbGetMarketBotsForAutobot(
        autobot._id.toString(),
      );

      if (autobot) {
        let accountInfo = null;

        switch (autobot.exchange) {
          case Exchange.BINANCE:
            accountInfo =
              await this.binanceAccountInfoService.getAccountInfoWithRetry(
                autobot.userId,
              );
            break;
          case Exchange.GATEIO:
            accountInfo =
              await this.gateioAccountInfoService.getAccountInfoWithRetry(
                autobot.userId,
              );
            break;
        }

        for (const marketBot of marketBots) {
          this.logger.logServer(exchange, 'Selling', marketBot.symbol);
          await this.buysellService.processSell(
            marketBot,
            autobot,
            accountInfo,
          );
        }
      }

      return new Common(true);
    } catch (e) {
      return new Common(false, e);
    }
  }

  async buyNowMutation(userId: string): Promise<any> {
    try {
      const exchange = await this.userService.getUserExchange(userId);
      const autobot = await this.autobotService.dbGetAutobot(userId, exchange);
      const whitelistItems = await this.whitelistService.dbGetWhiteListCoins(
        userId,
        exchange,
      );
      const lastCandle = await this.dbCandleService.dbGetLastCandle(
        'BTCUSDT',
        '15',
        autobot.exchange,
      );

      for (const item of shuffle(whitelistItems)) {
        const symbol = item.symbol;
        const currentCoinPrice = await this.coinPriceService.dbGetCoinPrice(
          symbol,
          autobot.exchange,
        );

        await this.dbIndicatorService.addIndicator({
          symbol: symbol,
          indicator: 'manual',
          action: 'long',
          price: currentCoinPrice,
          indicatorValue: 0,
          timestamp: new Date(),
          candleTime: lastCandle.openTime,
          interval: autobot.interval,
          profit: null,
          exchange: autobot.exchange,
          newAdded: true,
        });
      }
      return new Common(true);
    } catch (e) {
      return new Common(false, e);
    }
  }

  public async buyWhiteListCoinMutation(
    args: GQL.IBuyWhiteListCoinMutationOnMutationArguments,
    userId: string,
  ) {
    try {
      const exchange = await this.userService.getUserExchange(userId);
      const autobot = await this.autobotService.dbGetAutobot(userId, exchange);

      switch (exchange) {
        case Exchange.BINANCE:
          const binanceAccountInfo =
            await this.binanceAccountInfoService.getAccountInfoWithRetry(
              autobot.userId,
            );

          await this.buysellService.processBuy(
            args.symbol,
            autobot,
            binanceAccountInfo,
          );
          break;

        case Exchange.GATEIO:
          const gateioAccountInfo =
            await this.gateioAccountInfoService.getAccountInfoWithRetry(
              autobot.userId,
            );

          await this.buysellService.processBuy(
            args.symbol,
            autobot,
            gateioAccountInfo,
          );
          break;
      }

      return new Common(true);
    } catch (e) {
      return new Common(false, e);
    }
  }

  public async autobot(userId: string): Promise<any> {
    const exchange = await this.userService.getUserExchange(userId);
    const autobotObj = await this.autobotService.dbGetAutobot(userId, exchange);

    if (autobotObj) {
      const date = new Date();
      const trades =
        await this.marketBotTradeService.dbGetMarketBotTradesAfterDate(
          autobotObj._id.toString(),
          new Date(date.getFullYear(), date.getMonth(), date.getDate()),
        );

      let positiveTrades = 0;
      let negativeTrades = 0;
      let shortTrades = 0;
      let longTrades = 0;

      // tslint:disable-next-line:prefer-for-of
      for (let i = 0; i < trades.length; i++) {
        const trade = trades[i];

        if (trade.position === 'short') {
          if (trade.profit >= 0) {
            positiveTrades++;
          } else {
            negativeTrades++;
          }

          shortTrades++;
        } else {
          longTrades++;
        }
      }

      autobotObj.tradesTodayShort = shortTrades;
      autobotObj.tradesTodayLong = longTrades;
      autobotObj.tradesToday = shortTrades + longTrades;
      autobotObj.successRateToday =
        positiveTrades + negativeTrades != 0
          ? Number((positiveTrades / (positiveTrades + negativeTrades)) * 100)
          : 0;
      autobotObj.winsToday = positiveTrades;
      autobotObj.losesToday = negativeTrades;
      autobotObj._id = autobotObj._id.toString() as any;
    }

    return autobotObj;
  }

  transformToAutoBotMutation = async (
    args: GQL.ITransformToAutoBotMutationOnMutationArguments,
    userId: string,
  ) => {
    try {
      await this.autobotService.dbTransformAutoBot(args, userId);

      return true;
    } catch (e) {
      return new Error(e);
    }
  };

  profitGraph = async (userId: string): Promise<any> => {
    const exchange = await this.userService.getUserExchange(userId);
    const autobot = await this.autobotService.dbGetPlainAutobotByUser(
      userId,
      exchange,
    );

    const dayUsd = new Map();
    const daysToFetch = 30;
    const now = new Date();

    const year = now.getFullYear();
    const month = now.getMonth();
    const date = now.getDate();

    const dateFrom = new Date(Date.UTC(year, month, date - daysToFetch));

    if (autobot) {
      const trades = await this.marketBotTradeService.dbGetMarketBotProfitFrom(
        autobot._id.toString(),
        dateFrom,
      );
      const resultHistory = [];

      // Create date map
      for (let i = daysToFetch; i >= 0; i--) {
        const dateUTC = new Date(Date.UTC(year, month, date - i));
        dayUsd.set(dateUTC.getTime(), 0);
      }

      for (const trade of trades) {
        const profitDate = new Date(trade.tradeDate);
        const year = profitDate.getFullYear();
        const month = profitDate.getMonth();
        const date = profitDate.getDate();
        const dateUTC = new Date(Date.UTC(year, month, date));

        const prev = dayUsd.get(dateUTC.getTime()) as number;

        if (prev !== undefined && trade.profitUsd != null) {
          dayUsd.set(dateUTC.getTime(), prev + trade.profitUsd);
        }
      }

      dayUsd.forEach((value, key) => {
        resultHistory.push({
          usdHistory: value,
          timestamp: new Date(key),
        });
      });

      return resultHistory;
    }

    return [];
  };

  lastBotOrders = async (userId: string): Promise<any> => {
    const exchange = await this.userService.getUserExchange(userId);
    const autobot = await this.autobotService.dbGetPlainAutobotByUser(
      userId,
      exchange,
    );

    if (autobot) {
      return await this.marketBotTradeService.dbGetMarketBotProfitFromAutobotLimit(
        autobot._id.toString(),
        100,
      );
    }

    return [];
  };

  suggestList = async () => {
    let coins: any = [];
    try {
      const result = await fetch(
        'https://www.binance.com/bapi/futures/v1/public/future/common/strategy/landing-page/queryTopCount',
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            strategyType: 2,
            rows: 150,
            page: 1,
          }),
        },
      );
      const responseJson: any = await result.json();

      if (responseJson && responseJson.data) {
        coins = responseJson.data.filter((x) => x.symbol.includes('USDT'));
        // @ts-ignore
        coins = sortWith([descend(prop('longCount'))])(coins);
      }
    } catch (e) {
      this.logger.errorServer(e);
    }

    return coins;
  };

  fearAltIndex = async () => {
    try {
      return this.fearAltIndexService.dbGetFearAltIndex();
    } catch (e) {
      this.logger.errorServer(e);
    }

    return null;
  };
}
