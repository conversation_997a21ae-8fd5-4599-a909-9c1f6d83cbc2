import { Injectable } from '@nestjs/common';
import { DbExchangeInfoService } from '@app/database/db-exchange-info/db-exchange-info.service';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbCandlesticksService } from '@app/database/db-candlesticks/db-candlesticks.service';
import { calcAORangeFromTimestampNoFilter } from '../../../../indicators/AO';
import { DbCoinIndicatorService } from '@app/database/db-coin-indicator/db-coin-indicator.service';
import { DbCoinIndicator } from '@app/database/db-coin-indicator/db-coin-indicator.model';
import { StrategyIntervals } from '../../../../utils/Strategies';
import { last } from 'ramda';
import { calcRSIFromCandles } from '../../../../indicators/RSI';
import { calcADXRangeFromTimestampNoFilter } from '../../../../indicators/ADX';
import { calcBBFromCandles } from '../../../../indicators/BB';
import { calcOBVFromCandles } from '../../../../indicators/OBV';
import { calcMFIFromCandles } from '../../../../indicators/MFI';
import { calcROCFromCandles } from '../../../../indicators/ROC';
import { calcIchimokuFromCandles } from '../../../../indicators/Inchimoku';
import { calcATRFromCandles } from '../../../../indicators/ATR';
import { calcEMARangeFromTimestampNoFilter } from '../../../../indicators/EMA';
import { calcVWAP } from '../../../../indicators/VWAP';
import { calcSOFromCandles } from '../../../../indicators/SO';
import { calcSMA } from '../../../../indicators/SMA';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import { DbIndicatorService } from '@app/database/db-indicator/db-indicator.service';

@Injectable()
export class CoinIndicatorCalculationService {
  constructor(
    private exchangeInfoService: DbExchangeInfoService,
    private coinIndicatorService: DbCoinIndicatorService,
    private candlesticksService: DbCandlesticksService,
    private dblogService: DbLogService,
    private dbCoinPriceService: DbCoinPriceService,
    private dbIndicatorService: DbIndicatorService,
  ) {}

  async calcForSymbol(symbol: string, exchange: string) {
    try {
      const intervals = [...StrategyIntervals];

      for (const interval of intervals) {
        const candleSticks = await this.candlesticksService.dbGetLastCandles(
          symbol,
          interval,
          exchange,
          500,
        );

        const result: DbCoinIndicator = {} as DbCoinIndicator;

        // AO
        const aoValues =
          calcAORangeFromTimestampNoFilter(candleSticks).slice(-2);
        result.ao = last(aoValues)?.aoValue;
        result.aoTrend =
          aoValues[1]?.aoValue >= aoValues[0]?.aoValue ? 'up' : 'down';

        // RSI
        result.rsi = last(calcRSIFromCandles(candleSticks))?.rsi;

        // ATR
        result.atr = last(calcATRFromCandles(candleSticks))?.atr;

        // ADX
        result.adx = last(
          calcADXRangeFromTimestampNoFilter(candleSticks),
        )?.adxValue;

        // BB
        const bb = last(calcBBFromCandles(candleSticks));
        result.bb_middle = bb?.middle;
        result.bb_upper = bb?.upper;
        result.bb_lower = bb?.lower;

        // OBV
        result.bb_obv = last(calcOBVFromCandles(candleSticks))?.obv;

        // MFI
        const mfi = last(calcMFIFromCandles(candleSticks))?.mfi;
        result.mfi = mfi ? mfi : 0;

        // ROC
        result.roc = last(calcROCFromCandles(candleSticks))?.roc;

        // Ichimoku
        result.ichimoku = last(calcIchimokuFromCandles(candleSticks))?.ichimocu;

        // EMA25
        result.ema25 = last(
          calcEMARangeFromTimestampNoFilter(candleSticks, 25),
        )?.emaValue;

        // EMA50
        result.ema50 = last(
          calcEMARangeFromTimestampNoFilter(candleSticks, 50),
        )?.emaValue;

        // EMA100
        result.ema100 = last(
          calcEMARangeFromTimestampNoFilter(candleSticks, 100),
        )?.emaValue;

        // SMA
        result.sma7 = last(calcSMA(candleSticks, 7))?.smaValue;
        result.sma21 = last(calcSMA(candleSticks, 21))?.smaValue;
        result.sma100 = last(calcSMA(candleSticks, 100))?.smaValue;

        // VWAP
        result.vwap = last(calcVWAP(candleSticks))?.vwap;

        // SO
        result.so_k = last(calcSOFromCandles(candleSticks))?.k;
        result.so_d = last(calcSOFromCandles(candleSticks))?.d;

        result.price = await this.dbCoinPriceService.dbGetCoinPrice(
          symbol,
          exchange,
        );

        result.nesterov = (
          await this.dbIndicatorService.getLastIndicatorAction(
            symbol,
            '15',
            'nesterov',
            exchange,
          )
        )?.action;

        await this.coinIndicatorService.update(
          symbol,
          interval,
          result,
          exchange,
        );
      }
    } catch (e) {
      this.dblogService.errorIndicators(e);
    }
  }

  async calcForAllSymbols(exchange: string) {
    try {
      // this.dblogService.logIndicators(
      //   exchange + ': Start calculating indicators for instruments',
      // );
      const usdtSymbols =
        await this.exchangeInfoService.getUsdtSymbols(exchange);
      await Promise.all(
        usdtSymbols.map((symbol) => this.calcForSymbol(symbol, exchange)),
      );
      // this.dblogService.logIndicators(
      //   exchange + ': Finished calculating indicators for instruments',
      // );
    } catch (e) {
      this.dblogService.errorIndicators(e.message, e);
    }
  }
}
