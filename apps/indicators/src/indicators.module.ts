import { Module } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { DatabaseModule } from '@app/database';
import { ScheduleModule } from '@nestjs/schedule';
import { IndicatorsService } from './indicators.service';
import { JobsService } from './jobs.service';
import { MomentumIndicatorsService } from './momentum/momentum-indicators.service';
import { ProcessIndicatorsService } from './process-indicators/process-indicators.service';
import { TrendIndicatorsService } from './trend/trend-indicators.service';
import { VolumeIndicatorsService } from './volume/volume-indicators.service';
import { VolatilityIndicatorsService } from './volatility/volatility-indicators.service';
import { PatternIndicatorsService } from './patterns/pattern-indicators.service';
import { CalculateProfitService } from './profit/calculate-profit.service';
import { CoinIndicatorCalculationService } from './coin-indicator-calculation/coin-indicator-calculation.service';

@Module({
  imports: [
    DatabaseModule,
    ScheduleModule.forRoot(),
    BullModule.forRoot({
      redis: {
        host: '127.0.0.1',
        port: 6379,
      },
    }),
  ],
  providers: [
    JobsService,
    IndicatorsService,
    MomentumIndicatorsService,
    ProcessIndicatorsService,
    TrendIndicatorsService,
    VolumeIndicatorsService,
    VolatilityIndicatorsService,
    PatternIndicatorsService,
    CalculateProfitService,
    CoinIndicatorCalculationService,
  ],
})
export class IndicatorsModule {}
