import { Injectable } from '@nestjs/common';
import { last } from 'ramda';
import { calcBBFromCandles } from '../../../../indicators/BB';

@Injectable()
export class VolatilityIndicatorsService {
  constructor() {}

  async registerIndicators(registered: Map<string, object>) {
    //
    // Volatility indicators
    //
    /**
     * Bollinger Bands
     *
     * Bollinger Bands feature three lines: a central moving average and upper/lower bands
     * indicating market volatility.
     *
     * Narrow bands signal low volatility;
     * wider bands suggest high volatility.
     *
     * Traders use them to spot overbought (price near upper band) or oversold (price near lower band)
     * conditions and potential sell-offs.
     *
     * They also identify “squeezes” when bands tighten, hinting at an upcoming significant price move.
     *
     * When price is trading near the upper band, it indicates that a stock is overbought and may be due for a price correction.
     * When the price is near the lower band, it suggests that the stock is oversold and may be ripe for a rebound.
     *
     */
    // this.onBB(registered);
  }

  private onBB(registered: Map<string, object>) {
    const indicator = 'bb';

    const buyFunction = ({ candles, price }) => {
      const result = calcBBFromCandles(candles);
      const lower = last(result)?.lower;
      const middle = last(result)?.middle;
      const upper = last(result)?.upper;

      // const buy = rsi < 30;

      // return {
      //   buy,
      //   indicatorValue: rsi,
      // };
    };

    const sellFunction = ({ candles, price }) => {
      const result = calcBBFromCandles(candles);
      const lower = last(result)?.lower;
      const middle = last(result)?.middle;
      const upper = last(result)?.upper;

      // const sell = rsi > 70;
      //
      // return {
      //   sell,
      //   indicatorValue: rsi,
      // };
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }
}
