import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { Cron } from '@nestjs/schedule';
import { ProcessIndicatorsService } from './process-indicators/process-indicators.service';
import { CalculateProfitService } from './profit/calculate-profit.service';
import { CoinIndicatorCalculationService } from './coin-indicator-calculation/coin-indicator-calculation.service';
import { Exchange } from '../../../types/exchanges';
import { DbExchangeInfoService } from '@app/database/db-exchange-info/db-exchange-info.service';
import { DbNoticedCoinService } from '@app/database/db-noticed-coin/db-noticed-coin.service';

/**
 *    *    *    *    *    *
 ┬    ┬    ┬    ┬    ┬    ┬
 │    │    │    │    │    │
 │    │    │    │    │    └ day of week (0 - 7) (0 or 7 is Sun)
 │    │    │    │    └───── month (1 - 12)
 │    │    │    └────────── day of month (1 - 31)
 │    │    └─────────────── hour (0 - 23)
 │    └──────────────────── minute (0 - 59)
 └───────────────────────── second (0 - 59, OPTIONAL)
 */

@Injectable()
export class JobsService {
  constructor(
    private logger: DbLogService,
    private calculateProfitService: CalculateProfitService,
    private processIndicatorsService: ProcessIndicatorsService,
    private coinIndicatorCalculationService: CoinIndicatorCalculationService,
    private exchangeInfoService: DbExchangeInfoService,
    private dbNoticedCoinService: DbNoticedCoinService,
  ) {}

  @Cron('15 */20 * * * *')
  async indicator15Binance() {
    // await this.executeJobs('15', Exchange.BINANCE);
  }

  @Cron('15 */20 * * * *')
  async indicator15Gateio() {
    await this.executeJobs('15', Exchange.GATEIO);
  }

  @Cron('15 16,36 * * * *')
  async indicator60Binance() {
    // await this.executeJobs('60', Exchange.BINANCE);
  }

  @Cron('15 16,36 * * * *')
  async indicator60Gateio() {
    // await this.executeJobs('60', Exchange.GATEIO);
  }

  @Cron('15 16,36 * * * *')
  async indicator120Binance() {
    // await this.executeJobs('120', Exchange.BINANCE);
  }

  @Cron('15 */20 * * * *')
  async indicator120GateioWhitelisted() {
    await this.executeJobsWhitelisted('120', Exchange.GATEIO);
  }

  @Cron('15 16,36 * * * *')
  async indicator120Gateio() {
    // await this.executeJobs('120', Exchange.GATEIO);
  }

  @Cron('15 16 * * * *')
  async indicator240Binance() {
    // await this.executeJobs('240', Exchange.BINANCE);
  }

  @Cron('15 2 * * * *')
  async indicator240GateioWhitelisted() {
    // await this.executeJobsWhitelisted('240', Exchange.GATEIO);
  }

  @Cron('15 16 */4 * * *')
  async indicator240Gateio() {
    // await this.executeJobs('240', Exchange.GATEIO);
  }

  @Cron('15 16 */6 * * *')
  async indicator1dBinance() {
    // await this.executeJobs('1d', Exchange.BINANCE);
  }

  @Cron('15 2 */6 * * *')
  async indicator1dGateioWhitelisted() {
    // await this.executeJobsWhitelisted('1d', Exchange.GATEIO);
  }

  @Cron('15 16 */6 * * *')
  async indicator1dGateio() {
    // await this.executeJobs('1d', Exchange.GATEIO);
  }

  @Cron('40 10 4 * * *')
  async updateProfitsBinance() {
    await this.calculateProfitService.updateProfits(Exchange.BINANCE);
  }

  @Cron('40 10 4 * * *')
  async updateProfitsGateio() {
    await this.calculateProfitService.updateProfits(Exchange.GATEIO);
  }

  @Cron('40 40 4 * * *')
  async updateDailyProfitsBinance() {
    await this.calculateProfitService.updateDailyProfits(Exchange.BINANCE);
  }

  @Cron('40 40 4 * * *')
  async updateDailyProfitsGateio() {
    await this.calculateProfitService.updateDailyProfits(Exchange.GATEIO);
  }

  @Cron('5 */20 * * * *')
  async calcCoinIndicatorsBinance() {
    await this.coinIndicatorCalculationService.calcForAllSymbols(
      Exchange.BINANCE,
    );
  }

  @Cron('5 */20 * * * *')
  async calcCoinIndicatorsGateio() {
    await this.coinIndicatorCalculationService.calcForAllSymbols(
      Exchange.GATEIO,
    );
  }

  private async executeJobs(interval: string, exchange: string) {
    await this.executeJobsWhitelisted('15', Exchange.GATEIO);
    await this.executeJobsWhitelisted('120', Exchange.GATEIO);

    // await this.logger.log(
    //   'mb-indicators',
    //   exchange,
    //   `Starting indicator calculation for interval ${interval}`,
    // );

    const symbols =
      await this.exchangeInfoService.getUsdtSymbolsWithoutExcluded(exchange);
    await this.processIndicatorsService.executeForSymbols(
      interval,
      symbols,
      exchange,
    );

    // await this.logger.log(
    //   'mb-indicators',
    //   exchange,
    //   `Finished indicator calculation for interval ${interval}`,
    // );
  }

  private async executeJobsWhitelisted(interval: string, exchange: string) {
    // await this.logger.log(
    //   'mb-indicators',
    //   exchange,
    //   `Starting indicator calculation for whitelisted assets. Interval ${interval}`,
    // );

    const symbols =
      await this.dbNoticedCoinService.getAllNoticedCoins(exchange);
    await this.processIndicatorsService.executeForSymbols(
      interval,
      symbols,
      exchange,
    );

    // await this.logger.log(
    //   'mb-indicators',
    //   exchange,
    //   `Finished indicator calculation for whitelisted assets. Interval ${interval}`,
    // );
  }

  async startJobs() {
    await this.executeJobsWhitelisted('120', Exchange.GATEIO);

    // await this.executeJobs('240');
    // await this.executeJobs('1d');
    // await this.coinIndicatorCalculationService.calcForAllSymbols(
    //   Exchange.BINANCE,
    // );
    // await this.coinIndicatorCalculationService.calcForAllSymbols(
    //   Exchange.GATEIO,
    // );
    // await this.calculateProfitService.calculateProfits(Exchange.BINANCE);
    // await this.calculateProfitService.updateProfits(Exchange.BINANCE);
    // await this.calculateProfitService.updateDailyProfits(Exchange.BINANCE);
    await this.calculateProfitService.calculateProfits(Exchange.GATEIO);
    await this.calculateProfitService.updateProfits(Exchange.GATEIO);
    await this.calculateProfitService.updateDailyProfits(Exchange.GATEIO);
  }
}
