import { Injectable } from '@nestjs/common';
import { predictPatternsFromCandles } from '../../../../indicators/Pattern';

@Injectable()
export class PatternIndicatorsService {
  constructor() {}

  async findBullish(registered: Map<string, object>) {
    const indicator = 'pattern';

    const buyFunction = async ({ candles, price }) => {
      const result = await predictPatternsFromCandles(candles);

      // const buy = rsi < 30;

      // return {
      //   buy,
      //   indicatorValue: rsi,
      // };
    };

    const sellFunction = async ({ candles, price }) => {
      const result = await predictPatternsFromCandles(candles);

      // const sell = rsi > 70;
      //
      // return {
      //   sell,
      //   indicatorValue: rsi,
      // };
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }
}
