import { Injectable } from '@nestjs/common';
import { calcEMARangeFromTimestampNoFilter } from '../../../../indicators/EMA';
import { calcADXRangeFromTimestampNoFilter } from '../../../../indicators/ADX';
import { last } from 'ramda';
import { DbCandleStickDocument } from '@app/database/db-candlesticks/db-candlesticks.model';
import { calcAORangeFromTimestampNoFilter } from '../../../../indicators/AO';
import { DbCandlesticksService } from '@app/database/db-candlesticks/db-candlesticks.service';
import { IndicatorValue } from '../momentum/momentum-indicators.service';

/**
 * Trend Indicators help traders identify the direction and strength of a market trend.
 * They smooth out price fluctuations to show a clearer market direction.
 */

@Injectable()
export class TrendIndicatorsService {
  constructor(private candleService: DbCandlesticksService) {}

  async registerIndicators(registered: Map<string, IndicatorValue>) {
    /**
     * Moving Average Convergence Divergence (MACD)
     *
     * The MACD is a comprehensive trend indicator in crypto trading,
     * displaying the relationship between two moving averages.
     *
     * It includes the MACD line (the difference between two EMAs),
     * the signal line (EMA of the MACD line), and the histogram (the gap between the MACD and signal lines).
     *
     * Traders use MACD crossovers to identify buy or sell signals: a MACD line crossing above the signal line
     * indicates a bullish trend and potential buy, while a cross below suggests a bearish trend and a sell signal.
     *
     * The MACD also gauges trend momentum and strength,
     * with a growing gap between the MACD and signal lines signalling stronger trends.
     */
    this.onAOCross(registered);
    this.onAOSwing(registered);
    this.onAOCrossSwing(registered);

    this.onAOCrossBtcCross(registered);
    this.onAOSwingBtcCross(registered);
    this.onAOCrossSwingBtcCross(registered);

    this.onAOCrossBtcSwing(registered);
    this.onAOSwingBtcSwing(registered);
    this.onAOCrossSwingBtcSwing(registered);

    this.onBTCCross(registered);
    this.onBTCSwing(registered);
    this.onBTCCrossSwing(registered);

    /**
     * Simple and Exponential Moving Averages (SMA and EMA)
     *
     * Moving Averages (MAs) are key trend indicators in crypto trading,
     * smoothing price data to show clear trend directions.
     *
     * The Simple Moving Average (SMA) calculates the average price over set periods, equally weighing all prices.
     *
     * In contrast, the Exponential Moving Average (EMA) prioritises recent prices,
     * reacting faster to new market changes.
     *
     * MAs help identify support and resistance levels and indicate trend directions,
     * with rising MAs suggesting uptrends and falling MAs indicating downtrends.
     */
    // this.onEmaAdx(registered);
    // this.onEma25_50(registered);
    // this.onEma50_100(registered);
    // this.onEma100_200(registered);
    // this.onEmaAdxAoCross(registered);
  }

  private onEmaAdx(registered: Map<string, IndicatorValue>) {
    const indicator = 'ema+adx';

    const buyFunction = ({ candles, price }) => {
      const result = this.getEmaAdxValues(candles);
      if (result) {
        const { ema25, ema100, ema200, adx } = result;

        const buy =
          price > ema200 && ema25 > ema100 && ema100 > ema200 && adx > 30;

        return {
          buy,
          indicatorValue: 0,
        };
      }
    };

    const sellFunction = ({ candles, price }) => {
      const result = this.getEmaAdxValues(candles);
      if (result) {
        const { ema25, ema100, ema200, adx } = result;
        const sell = price < ema200 && ema25 < ema100 && ema100 < ema200;

        return {
          sell,
          indicatorValue: 0,
        };
      }
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }

  private onEmaAdxAoCross(registered: Map<string, IndicatorValue>) {
    const indicator = 'ema+adx+ao-cross';

    const buyFunction = ({ candles, price, third }) => {
      const result = this.getEmaAdxValues(candles);
      if (result) {
        const { ema25, ema100, ema200, adx } = result;

        const aoValues = calcAORangeFromTimestampNoFilter(
          candles.slice(-64),
        ).slice(-3);

        const third = aoValues.at(2)?.aoValue;

        const buy =
          price > ema200 &&
          ema25 > ema100 &&
          ema100 > ema200 &&
          adx > 30 &&
          third > 0;

        return {
          buy,
          indicatorValue: 0,
        };
      }
    };

    const sellFunction = ({ candles, price }) => {
      const result = this.getEmaAdxValues(candles);
      if (result) {
        const { ema25, ema100, ema200, adx } = result;

        const aoValues = calcAORangeFromTimestampNoFilter(
          candles.slice(-64),
        ).slice(-3);

        const third = aoValues.at(2)?.aoValue;

        const sell =
          (price < ema200 && ema25 < ema100 && ema100 < ema200) || third < 0;

        return {
          sell,
          indicatorValue: 0,
        };
      }
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }

  private onEma25_50(registered: Map<string, IndicatorValue>) {
    const indicator = 'ema25+ema50';

    const buyFunction = ({ candles, price }) => {
      const ema25Values = calcEMARangeFromTimestampNoFilter(candles, 25);
      const ema50Values = calcEMARangeFromTimestampNoFilter(candles, 50);
      const ema25 = last(ema25Values)?.emaValue;
      const ema50 = last(ema50Values)?.emaValue;

      const buy = ema25 > ema50;

      return {
        buy,
        indicatorValue: ema50,
      };
    };

    const sellFunction = ({ candles, price }) => {
      const ema25Values = calcEMARangeFromTimestampNoFilter(candles, 25);
      const ema50Values = calcEMARangeFromTimestampNoFilter(candles, 50);
      const ema25 = last(ema25Values)?.emaValue;
      const ema50 = last(ema50Values)?.emaValue;

      const sell = ema25 < ema50;

      return {
        sell,
        indicatorValue: ema50,
      };
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }

  private onEma50_100(registered: Map<string, IndicatorValue>) {
    const indicator = 'ema50+ema100';

    const buyFunction = ({ candles }) => {
      const ema50Values = calcEMARangeFromTimestampNoFilter(candles, 50);
      const ema100Values = calcEMARangeFromTimestampNoFilter(candles, 100);
      const ema50 = last(ema50Values)?.emaValue;
      const ema100 = last(ema100Values)?.emaValue;

      const buy = ema50 > ema100;

      return {
        buy,
        indicatorValue: ema100,
      };
    };

    const sellFunction = ({ candles }) => {
      const ema50Values = calcEMARangeFromTimestampNoFilter(candles, 50);
      const ema100Values = calcEMARangeFromTimestampNoFilter(candles, 100);
      const ema50 = last(ema50Values)?.emaValue;
      const ema100 = last(ema100Values)?.emaValue;

      const sell = ema50 < ema100;

      return {
        sell,
        indicatorValue: ema100,
      };
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }

  private onEma100_200(registered: Map<string, IndicatorValue>) {
    const indicator = 'ema100+ema200';

    const buyFunction = ({ candles }) => {
      const ema100Values = calcEMARangeFromTimestampNoFilter(candles, 100);
      const ema200Values = calcEMARangeFromTimestampNoFilter(candles, 200);
      const ema100 = last(ema100Values)?.emaValue;
      const ema200 = last(ema200Values)?.emaValue;

      const buy = ema100 > ema200;

      return {
        buy,
        indicatorValue: ema200,
      };
    };

    const sellFunction = ({ candles }) => {
      const ema100Values = calcEMARangeFromTimestampNoFilter(candles, 100);
      const ema200Values = calcEMARangeFromTimestampNoFilter(candles, 200);
      const ema100 = last(ema100Values)?.emaValue;
      const ema200 = last(ema200Values)?.emaValue;

      const sell = ema100 < ema200;

      return {
        sell,
        indicatorValue: ema200,
      };
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }

  private onAOCross(registered: Map<string, IndicatorValue>) {
    const indicator = 'ao-cross';

    const buyFunction = (args: { candles: DbCandleStickDocument[] }) => {
      const { candles } = args;

      const aoValues = calcAORangeFromTimestampNoFilter(
        candles.slice(-64),
      ).slice(-3);

      const third = last(aoValues)?.aoValue;
      const buy = third != null && third > 0;

      return {
        buy,
        indicatorValue: third,
      };
    };

    const sellFunction = ({ candles }) => {
      const aoValues = calcAORangeFromTimestampNoFilter(
        candles.slice(-64),
      ).slice(-3);

      const third = last(aoValues)?.aoValue;
      const sell = third != null && third < 0;

      return {
        sell,
        indicatorValue: third,
      };
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }

  private onAOSwing(registered: Map<string, IndicatorValue>) {
    const indicator = 'ao-swing';

    const buyFunction = ({ candles }) => {
      const aoValues = calcAORangeFromTimestampNoFilter(
        candles.slice(-64),
      ).slice(-3);

      const second = aoValues.at(1)?.aoValue;
      const third = aoValues.at(2)?.aoValue;

      const buy = third > second;

      return {
        buy,
        indicatorValue: third,
      };
    };

    const sellFunction = ({ candles }) => {
      const aoValues = calcAORangeFromTimestampNoFilter(
        candles.slice(-64),
      ).slice(-3);

      const second = aoValues.at(1)?.aoValue;
      const third = aoValues.at(2)?.aoValue;

      const sell = third < second;

      return {
        sell,
        indicatorValue: third,
      };
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }

  private onAOCrossSwing(registered: Map<string, IndicatorValue>) {
    const indicator = 'ao-cross-swing';

    const buyFunction = ({ candles }) => {
      const aoValues = calcAORangeFromTimestampNoFilter(
        candles.slice(-64),
      ).slice(-3);

      const second = aoValues.at(1)?.aoValue;
      const third = aoValues.at(2)?.aoValue;

      const buy = third > second && third > 0;

      return {
        buy,
        indicatorValue: third,
      };
    };

    const sellFunction = ({ candles }) => {
      const aoValues = calcAORangeFromTimestampNoFilter(
        candles.slice(-64),
      ).slice(-3);

      const second = aoValues.at(1)?.aoValue;
      const third = aoValues.at(2)?.aoValue;

      const sell = third < second || third < 0;

      return {
        sell,
        indicatorValue: third,
      };
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }

  private onAOCrossBtcCross(registered: Map<string, IndicatorValue>) {
    const indicator = 'ao-cross-btc-cross';

    const buyFunction = ({ candles, btcAo }) => {
      const aoValue = last(
        calcAORangeFromTimestampNoFilter(candles.slice(-64)),
      )?.aoValue;

      return {
        buy: aoValue > 0 && btcAo > 0,
        indicatorValue: aoValue,
      };
    };

    const sellFunction = ({ candles, btcAo }) => {
      const aoValue = last(
        calcAORangeFromTimestampNoFilter(candles.slice(-64)),
      )?.aoValue;

      return {
        sell: aoValue < 0 || btcAo < 0,
        indicatorValue: aoValue,
      };
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }

  private onAOSwingBtcCross(registered: Map<string, IndicatorValue>) {
    const indicator = 'ao-swing-btc-cross';

    const buyFunction = ({ candles, btcAo }) => {
      const aoValues = calcAORangeFromTimestampNoFilter(
        candles.slice(-64),
      ).slice(-3);

      const second = aoValues.at(1)?.aoValue;
      const third = aoValues.at(2)?.aoValue;

      return {
        buy: third > second && btcAo > 0,
        indicatorValue: third,
      };
    };

    const sellFunction = ({ candles, btcAo }) => {
      const aoValues = calcAORangeFromTimestampNoFilter(
        candles.slice(-64),
      ).slice(-3);

      const second = aoValues.at(1)?.aoValue;
      const third = aoValues.at(2)?.aoValue;

      return {
        sell: third < second || btcAo < 0,
        indicatorValue: third,
      };
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }

  private onAOCrossSwingBtcCross(registered: Map<string, IndicatorValue>) {
    const indicator = 'ao-cross-swing-btc-cross';

    const buyFunction = ({ candles, btcAo }) => {
      const aoValues = calcAORangeFromTimestampNoFilter(
        candles.slice(-64),
      ).slice(-3);

      const second = aoValues.at(1)?.aoValue;
      const third = aoValues.at(2)?.aoValue;

      return {
        buy: third > second && third > 0 && btcAo > 0,
        indicatorValue: third,
      };
    };

    const sellFunction = ({ candles, btcAo }) => {
      const aoValues = calcAORangeFromTimestampNoFilter(
        candles.slice(-64),
      ).slice(-3);

      const second = aoValues.at(1)?.aoValue;
      const third = aoValues.at(2)?.aoValue;

      return {
        sell: third < second || third < 0 || btcAo < 0,
        indicatorValue: third,
      };
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }

  private onAOCrossBtcSwing(registered: Map<string, IndicatorValue>) {
    const indicator = 'ao-cross-btc-swing';

    const buyFunction = ({ candles, btcAoTrend }) => {
      const aoValue = last(
        calcAORangeFromTimestampNoFilter(candles.slice(-64)),
      )?.aoValue;

      return {
        buy: aoValue > 0 && btcAoTrend == 'up',
        indicatorValue: aoValue,
      };
    };

    const sellFunction = ({ candles, btcAoTrend }) => {
      const aoValue = last(
        calcAORangeFromTimestampNoFilter(candles.slice(-64)),
      )?.aoValue;

      return {
        sell: aoValue < 0 || btcAoTrend == 'down',
        indicatorValue: aoValue,
      };
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }

  private onAOSwingBtcSwing(registered: Map<string, IndicatorValue>) {
    const indicator = 'ao-swing-btc-swing';

    const buyFunction = ({ candles, btcAoTrend }) => {
      const aoValues = calcAORangeFromTimestampNoFilter(
        candles.slice(-64),
      ).slice(-3);

      const second = aoValues.at(1)?.aoValue;
      const third = aoValues.at(2)?.aoValue;

      return {
        buy: third > second && btcAoTrend == 'up',
        indicatorValue: third,
      };
    };

    const sellFunction = ({ candles, btcAoTrend }) => {
      const aoValues = calcAORangeFromTimestampNoFilter(
        candles.slice(-64),
      ).slice(-3);

      const second = aoValues.at(1)?.aoValue;
      const third = aoValues.at(2)?.aoValue;

      return {
        sell: third < second || btcAoTrend == 'down',
        indicatorValue: third,
      };
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }

  private onAOCrossSwingBtcSwing(registered: Map<string, IndicatorValue>) {
    const indicator = 'ao-cross-swing-btc-swing';

    const buyFunction = ({ candles, btcAoTrend }) => {
      const aoValues = calcAORangeFromTimestampNoFilter(
        candles.slice(-64),
      ).slice(-3);

      const second = aoValues.at(1)?.aoValue;
      const third = aoValues.at(2)?.aoValue;

      return {
        buy: third > second && third > 0 && btcAoTrend == 'up',
        indicatorValue: third,
      };
    };

    const sellFunction = ({ candles, btcAoTrend }) => {
      const aoValues = calcAORangeFromTimestampNoFilter(
        candles.slice(-64),
      ).slice(-3);

      const second = aoValues.at(1)?.aoValue;
      const third = aoValues.at(2)?.aoValue;

      return {
        sell: third < second || third < 0 || btcAoTrend == 'down',
        indicatorValue: third,
      };
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }

  private onBTCCross(registered: Map<string, IndicatorValue>) {
    const indicator = 'btc-cross';

    const buyFunction = async ({ btcAo }) => {
      return {
        buy: btcAo > 0,
        indicatorValue: btcAo,
      };
    };

    const sellFunction = async ({ btcAo }) => {
      return {
        sell: btcAo < 0,
        indicatorValue: btcAo,
      };
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }

  private onBTCSwing(registered: Map<string, IndicatorValue>) {
    const indicator = 'btc-swing';

    const buyFunction = async ({ btcAo, btcAoTrend }) => {
      const buy = btcAoTrend == 'up';

      return {
        buy,
        indicatorValue: btcAo,
      };
    };

    const sellFunction = async ({ btcAo, btcAoTrend }) => {
      const sell = btcAoTrend == 'down';

      return {
        sell,
        indicatorValue: btcAo,
      };
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }

  private onBTCCrossSwing(registered: Map<string, IndicatorValue>) {
    const indicator = 'btc-cross-swing';

    const buyFunction = async ({ btcAo, btcAoTrend }) => {
      const buy = btcAoTrend == 'up' && btcAo > 0;

      return {
        buy,
        indicatorValue: btcAo,
      };
    };

    const sellFunction = async ({ btcAo, btcAoTrend }) => {
      const sell = btcAoTrend == 'down' || btcAo < 0;

      return {
        sell,
        indicatorValue: btcAo,
      };
    };

    registered.set(indicator, {
      sellFunction,
      buyFunction,
    });
  }

  private getEmaAdxValues(candles: DbCandleStickDocument[]) {
    const ema25Values = calcEMARangeFromTimestampNoFilter(candles, 25);
    const ema100Values = calcEMARangeFromTimestampNoFilter(candles, 100);
    const ema200Values = calcEMARangeFromTimestampNoFilter(candles, 200);
    const adxValues = calcADXRangeFromTimestampNoFilter(candles, 14);

    if (
      ema25Values.length == 0 ||
      ema100Values.length == 0 ||
      ema200Values.length == 0 ||
      adxValues.length == 0
    ) {
      return;
    }

    const ema25 = last(ema25Values).emaValue;
    const ema100 = last(ema100Values).emaValue;
    const ema200 = last(ema200Values).emaValue;
    const adx = last(adxValues).adxValue;

    return {
      ema25,
      ema100,
      ema200,
      adx,
    };
  }
}
