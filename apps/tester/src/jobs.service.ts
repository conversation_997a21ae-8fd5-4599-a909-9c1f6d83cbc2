import { Injectable } from '@nestjs/common';
import { <PERSON><PERSON> } from '@nestjs/schedule';
import { TesterPickerService } from './tester-picker/tester-picker.service';
import { AllStrategyProducer } from './tester-picker/all-strategy.producer';
import { Exchange } from '../../../types/exchanges';

/**
 *    *    *    *    *    *
 ┬    ┬    ┬    ┬    ┬    ┬
 │    │    │    │    │    │
 │    │    │    │    │    └ day of week (0 - 7) (0 or 7 is Sun)
 │    │    │    │    └───── month (1 - 12)
 │    │    │    └────────── day of month (1 - 31)
 │    │    └─────────────── hour (0 - 23)
 │    └──────────────────── minute (0 - 59)
 └───────────────────────── second (0 - 59, OPTIONAL)
 */

@Injectable()
export class JobsService {
  constructor(
    private strategyPicker: TesterPickerService,
    private allStrategyService: AllStrategyProducer,
  ) {}

  @Cron('*/10 * * * * *')
  async pickJobs() {
    await this.strategyPicker.pickJobs();
  }

  @Cron('10 10 0 * * *')
  async runAllStrategyTests() {
    await this.allStrategyService.runAllStrategyTests(Exchange.BINANCE);
    await this.allStrategyService.runAllStrategyTests(Exchange.GATEIO);
  }

  async start() {
    await this.allStrategyService.runAllStrategyTests(Exchange.BINANCE);
    await this.allStrategyService.runAllStrategyTests(Exchange.GATEIO);
  }
}
