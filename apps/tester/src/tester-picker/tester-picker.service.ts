import { Injectable } from '@nestjs/common';
import { DbStrategyTestService } from '@app/database/db-strategy-test/db-strategy-test.service';
import { TestSelectionService } from './test-selection.service';
import { DbLogService } from '@app/database/db-log/db-log.service';

@Injectable()
export class TesterPickerService {
  constructor(
    private dbStrategyService: DbStrategyTestService,
    private strategyTester: TestSelectionService,
    private logger: DbLogService,
  ) {}

  public async pickJobs() {
    const newStrats = await this.dbStrategyService.dbPickNewStrategies();

    for (const strat of newStrats) {
      this.logger.logTester(
        'Starting strategy test',
        strat.strategy,
        strat.interval,
        strat.exchange,
      );
      await this.dbStrategyService.updateStrategyTestStatus(
        strat._id ? strat._id.toString() : null,
        'Train',
      );
    }

    for (const strat of newStrats) {
      await this.strategyTester.calculateStrategyTest(strat);

      await this.dbStrategyService.updateStrategyTestStatus(
        strat._id.toString(),
        'Finished',
      );
    }
  }
}
