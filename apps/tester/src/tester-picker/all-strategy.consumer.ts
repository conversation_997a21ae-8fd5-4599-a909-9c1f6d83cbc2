import PriceCalculation from '../../../../utils/PriceCalculation';
import { OnQueueActive, Process, Processor } from '@nestjs/bull';
import { DbStrategyTestService } from '@app/database/db-strategy-test/db-strategy-test.service';
import { TestSelectionService } from './test-selection.service';
import { DbStrategyAllTestService } from '@app/database/db-strategy-all-test/db-strategy-all-test.service';
import { DbStrategyAllTestDocument } from '@app/database/db-strategy-all-test/db-strategy-all-test.model';
import { Job } from 'bull';
import { DbLogService } from '@app/database/db-log/db-log.service';

@Processor('allstrategies')
export class AllStrategyConsumer {
  constructor(
    private dbStrategyService: DbStrategyTestService,
    private strategyTester: TestSelectionService,
    private allTestService: DbStrategyAllTestService,
    private logger: DbLogService,
  ) {}

  @OnQueueActive()
  onActive(job: Job) {
    const { strategy, interval } = job.data;

    console.log(`Processing test ${strategy} ${interval} ...`);
  }

  @Process()
  async handle(job: Job<unknown>) {
    const strategy: DbStrategyAllTestDocument =
      job.data as DbStrategyAllTestDocument;

    const queue = await job.queue.getWaiting();
    console.log('Jobs still in the queue:', queue.map((x) => x.id).join(','));

    await this.processStrategy(strategy);
  }

  async processStrategy(strat: DbStrategyAllTestDocument) {
    const {
      symbols,
      strategy,
      startDate,
      endDate,
      investPerCoin,
      interval,
      sl,
      exchange,
    } = strat;

    let totalStrategyProfitPercent = 0;
    let totalStrategyProfitUsd = 0;
    let bestSymbolProfit = null;
    let worstSymbolProfit = null;
    let bestPerformer = null;
    let worstPerformer = null;
    let symbolCount = 0;
    let numTrades = 0;
    let posTrades = 0;
    let negTrades = 0;

    await this.allTestService.dbUpdateStrategyTestStatus(strat._id, 'Progress');

    let i = 0;
    for (const symbol of symbols.sort()) {
      try {
        const symbolTest = await this.dbStrategyService.dbCreateStrategyModel(
          {
            strategy: strategy,
            symbol: symbol,
            interval: interval,
            invest: investPerCoin,
            startDate: startDate,
            endDate: endDate,
            isInternal: true,
            status: 'Train',
            sl: sl,
          },
          strat.userId,
          exchange,
        );

        // Run test
        await this.strategyTester.calculateStrategyTest(symbolTest);

        // Collect test result
        const testResult = await this.dbStrategyService.dbGetStrategyTest(
          symbolTest._id,
        );

        const profitInPercent = PriceCalculation.toSimplePercent(
          testResult.invest,
          testResult.endUsd,
        );

        if (testResult.endUsd != undefined && profitInPercent != undefined) {
          symbolCount++;
          totalStrategyProfitPercent += Number(profitInPercent);
          totalStrategyProfitUsd += testResult.endUsd - testResult.invest;

          // Best symbol profit
          if (
            Number(profitInPercent) > bestSymbolProfit ||
            bestSymbolProfit == null
          ) {
            bestSymbolProfit = Number(profitInPercent);
            bestPerformer = symbol;
          }

          // Worst symbol profit
          if (
            Number(profitInPercent) < worstSymbolProfit ||
            worstSymbolProfit == null
          ) {
            worstSymbolProfit = Number(profitInPercent);
            worstPerformer = symbol;
          }

          numTrades += testResult.numTrades;
          posTrades += testResult.posTrades;
          negTrades += testResult.negTrades;

          // Update total
          await this.allTestService.dbUpdateAllStrategy(strat._id, {
            profit: totalStrategyProfitPercent,
            profitUsd: totalStrategyProfitUsd / symbolCount,
            bestSymbolProfit: bestSymbolProfit,
            worstSymbolProfit: worstSymbolProfit,
            bestPerformer: bestPerformer,
            worstPerformer: worstPerformer,
            numTrades: numTrades,
            posTrades: posTrades,
            negTrades: negTrades,
            avgProfit: totalStrategyProfitPercent / symbolCount,
            currentSymbol: symbol,
          });
        }

        // Set calculation status to finished
        await this.dbStrategyService.updateStrategyTestStatus(
          symbolTest.id,
          'Finished',
        );
      } catch (e) {
        this.logger.errorTester(symbol, e);
      }
    }

    await this.allTestService.dbUpdateStrategyTestStatus(strat._id, 'Finished');
  }
}
