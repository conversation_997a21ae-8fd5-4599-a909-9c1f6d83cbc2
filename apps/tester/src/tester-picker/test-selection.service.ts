import { Injectable } from '@nestjs/common';
import { DbStrategyTestDocument } from '@app/database/db-strategy-test/db-strategy-test.model';
import { AoService } from '../strategies/ao.service';
import { NeuralNesterovService } from '../strategies/neural.nesterov.service';

@Injectable()
export class TestSelectionService {
  constructor(
    private aoTestService: AoService,
    private nesterovService: NeuralNesterovService,
  ) {}

  public async calculateStrategyTest(strat: DbStrategyTestDocument) {
    switch (strat.strategy) {
      case 'ao-cross':
        await this.aoTestService.cross(strat);
        break;
      case 'ao-swing':
        await this.aoTestService.swing(strat);
        break;
      case 'ao-cross-swing':
        await this.aoTestService.crossSwing(strat);
        break;
      case 'btc-cross':
        await this.aoTestService.btcCross(strat);
        break;
      case 'btc-swing':
        await this.aoTestService.btcSwing(strat);
        break;
      case 'btc-cross-swing':
        await this.aoTestService.btcCrossSwing(strat);
        break;
      case 'ao-swing-btc-cross':
        await this.aoTestService.swingBtcCross(strat);
        break;
      case 'ao-cross-btc-cross':
        await this.aoTestService.crossBtcCross(strat);
        break;
      case 'ao-cross-swing-btc-cross':
        await this.aoTestService.crossSwingBtcCross(strat);
        break;
      case 'ao-swing-btc-swing':
        await this.aoTestService.swingBtcSwing(strat);
        break;
      case 'ao-cross-btc-swing':
        await this.aoTestService.crossBtcSwing(strat);
        break;
      case 'ao-cross-swing-btc-swing':
        await this.aoTestService.crossSwingBtcSwing(strat);
        break;
      case 'ema+adx':
        await this.aoTestService.emaAdx(strat);
        break;
      case 'ema+adx+ao-cross':
        await this.aoTestService.emaAdxAoCross(strat);
        break;
      case 'ema25+ema50':
        await this.aoTestService.ema25_50(strat);
        break;
      case 'ema50+ema100':
        await this.aoTestService.ema50_100(strat);
        break;
      case 'ema100+ema200':
        await this.aoTestService.ema100_200(strat);
        break;
      case 'rsi':
        await this.aoTestService.rsi(strat);
        break;
      case 'nesterov':
        await this.nesterovService.testNesterov(strat, false, false, false);
        break;
      case 'nesterov-btc-swing':
        await this.nesterovService.testNesterov(strat, true, false, false);
        break;
      case 'nesterov-btc-cross':
        await this.nesterovService.testNesterov(strat, false, true, false);
        break;
    }
  }
}
