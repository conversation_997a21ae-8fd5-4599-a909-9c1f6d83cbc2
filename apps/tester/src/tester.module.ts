import { Modu<PERSON> } from '@nestjs/common';
import { BullModule } from '@nestjs/bull';
import { DatabaseModule } from '@app/database';
import { ScheduleModule } from '@nestjs/schedule';
import { TesterPickerService } from './tester-picker/tester-picker.service';
import { TestSelectionService } from './tester-picker/test-selection.service';
import { JobsService } from './jobs.service';
import { AoService } from './strategies/ao.service';
import { NeuralNesterovService } from './strategies/neural.nesterov.service';
import { NeuralAdviceService } from './strategies/neural.advice.service';
import { NeuralCalcService } from './strategies/neural.calc.service';
import { AllStrategyProducer } from './tester-picker/all-strategy.producer';
import { AllStrategyConsumer } from './tester-picker/all-strategy.consumer';
import { TestExecutorService } from './strategies/test-executor.service';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    DatabaseModule,
    BullModule.forRoot({
      redis: {
        host: '127.0.0.1',
        port: 6379,
      },
    }),
    BullModule.registerQueue(
      {
        name: 'allstrategies',
      },
      {
        settings: {
          stalledInterval: 0,
          maxStalledCount: 0,
          lockDuration: 3600000, // 1h
        },
      },
    ),
  ],
  providers: [
    JobsService,
    TesterPickerService,
    TestSelectionService,
    AoService,
    NeuralNesterovService,
    NeuralAdviceService,
    NeuralCalcService,
    AllStrategyProducer,
    AllStrategyConsumer,
    TestExecutorService,
  ],
})
export class TesterModule {}
