import * as convnetjs from 'convnetjs';
import * as math from 'mathjs';
import { isNil, last, pluck } from 'ramda';
import { VWAP } from 'technicalindicators';
import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbCandlesticksService } from '@app/database/db-candlesticks/db-candlesticks.service';
import SMMA from '../../../../indicators/SMMA';
import {
  DbCandleStick,
  DbCandleStickDocument,
} from '@app/database/db-candlesticks/db-candlesticks.model';
import {
  calcAOFromTimestamp,
  calcAORangeFromTimestampNoFilter,
} from '../../../../indicators/AO';
import { calcMAFromTimestamp } from '../../../../indicators/MA';
import { calcRSIFromTimestamp } from '../../../../indicators/RSI';
import { calcVWAP } from '../../../../indicators/VWAP';
import { Exchange } from '../../../../types/exchanges';

@Injectable()
export class NeuralCalcService {
  constructor(
    private logger: DbLogService,
    private candleService: DbCandlesticksService,
  ) {}

  public maxCandles = 1000;
  public strategy = null;
  public exchange = null;
  public botAdvice = null;

  // Internal data for convnet
  public trainingData: any = {};
  public tradeData: any = {};

  //
  public settings = {
    strategy: null,
    exchange: null,
    historySize: null,
    learning_rate: null,
    momentum: null,
    method: null,
    batch_size: null,
    decay: null,
    interval: null,
    price_buffer_len: null,
    stoploss_enabled: null,
    stoploss_threshold: null,
    threshold_sell: null,
    threshold_buy: null,
    min_predictions: null,
    startDate: null,
    endDate: null,
    testId: null,
    withAO: null,
    withMA: null,
    withRSI: null,
    withBtcSwing: false,
    withBtcCross: false,
  };

  public calculate = async (
    strategy: string,
    exchange: string,
    symbol: string,
    settings: any,
    botAdvice: any,
    withBtcSwing: boolean,
    withBtcCross: boolean,
    withRSI: boolean,
  ) => {
    // this.logger.logTester('Starting calculation for test id', settings.testId);

    this.strategy = strategy;
    this.exchange = exchange;
    this.settings = settings;
    this.botAdvice = botAdvice;
    this.settings.withBtcSwing = withBtcSwing;
    this.settings.withBtcCross = withBtcCross;
    this.settings.withRSI = withRSI;

    // this.logger.logTester('Training', symbol, 'started..');

    await this.update(symbol);
  };

  private update = async (symbol: string) => {
    await this.updateNeuralNetForSymbol(symbol);
  };

  private updateNeuralNetForSymbol = async (symbol: string) => {
    this.createInitialTrainingData(symbol);
    // this.logger.logTester('Creating init data for symbol', symbol);

    const before = new Date();
    const candles = await this.candleService.dbGetCandlesBetween(
      symbol,
      this.settings.interval,
      this.settings.startDate,
      this.settings.endDate,
      this.exchange,
    );
    console.log('Calculating', symbol, candles.length + ' candles');

    let btcCandles = [];
    if (this.settings.withBtcSwing || this.settings.withBtcCross) {
      btcCandles = await this.candleService.dbGetCandlesBetween(
        'BTCUSDT',
        this.settings.interval,
        this.settings.startDate,
        this.settings.endDate,
        this.exchange,
      );
    }

    const vwap = calcVWAP(candles);
    for (const candle of candles) {
      const candleVWAP = vwap.find((x) => x.openTime == candle.openTime);

      this.learnWithParameters(candle, symbol, candleVWAP.vwap, candles);
      await this.calculateAdvice(candle, symbol, []);
    }

    const after = new Date();

    console.log(
      'Finished strategy test for',
      symbol,
      this.strategy,
      this.exchange,
      'after',
      Math.floor((after.getTime() - before.getTime()) / 1000 / 60),
      'minutes',
    );
  };

  /**
   * Learn with parameters.
   *
   * @param candle
   * @param symbol
   * @param vwap
   * @param candles
   */
  private learnWithParameters = (
    candle: DbCandleStickDocument,
    symbol: string,
    vwap: number,
    candles: DbCandleStickDocument[],
  ) => {
    let ao = null;
    let ma = null;
    let rsi = null;

    const currentCandleIdx = candles.findIndex(
      (x) => x.openTime === candle.openTime,
    );
    const candlesForCalculation = candles.slice(0, currentCandleIdx + 1);

    if (this.settings.withAO) {
      ao = calcAOFromTimestamp(candle.openTime, candlesForCalculation);

      if (isNil(ao)) {
        return;
      }
    }

    if (this.settings.withMA) {
      ma = calcMAFromTimestamp(candle.openTime, candlesForCalculation);

      if (isNil(ma)) {
        return;
      }
    }

    if (this.settings.withRSI) {
      rsi = calcRSIFromTimestamp(candle.openTime, candlesForCalculation);

      if (isNil(rsi)) {
        return;
      }
    }

    this.learnFromPrevCandles(candle, symbol, vwap, ao, ma, rsi);
  };

  /**
   * Learn
   *
   * @param symbol
   */
  private learn = (symbol: string) => {
    for (let i = 0; i < this.trainingData[symbol].priceBuffer.length - 1; i++) {
      const prevValue = [this.trainingData[symbol].priceBuffer[i]];
      const currentValue = [this.trainingData[symbol].priceBuffer[i + 1]];

      prevValue.push(this.trainingData[symbol].volumeBuffer[i]);

      if (this.settings.withAO) {
        prevValue.push(this.trainingData[symbol].aoBuffer[i]);
      }

      if (this.settings.withMA) {
        prevValue.push(this.trainingData[symbol].maBuffer[i]);
      }

      if (this.settings.withRSI) {
        prevValue.push(this.trainingData[symbol].rsiBuffer[i]);
      }

      /**
       * Если указан индикатор АО, то предыдущую цену плюс показатель АО
       * тренируем на новую цену. Тоесть мы говорим сети на какую цену он должен делать
       * регрессию этих двух значениий.
       *
       * Если помимо АО есть другие входящие, то указываем их как элементы prevValue
       */
      const vol = new convnetjs.Vol(prevValue);
      this.trainingData[symbol].lastVol = vol;

      /**
       * Тренирум сеть [x,y] -> z
       */
      this.trainingData[symbol].trainer.train(vol, currentValue);
      this.trainingData[symbol].predictionCount += 1;
    }
  };

  /**
   * Learn from prev candles
   * @param candle
   * @param symbol
   * @param vwap
   * @param ao
   * @param ma
   * @param rsi
   */
  private learnFromPrevCandles = (
    candle: DbCandleStickDocument,
    symbol: string,
    vwap: number,
    ao: number,
    ma: number,
    rsi: number,
  ) => {
    const high = Number(candle.high);
    const low = Number(candle.low);
    const close = Number(candle.close);
    const quoteVolume = Number(candle.quoteVolume);

    // Update SMMA
    if (this.settings.withMA) {
      this.trainingData[symbol].SMMA.update(
        (high + close + low + vwap + ma) / 5,
      );
    } else {
      this.trainingData[symbol].SMMA.update((high + close + low + vwap) / 4);
    }

    // Return SMMA Fast
    const smmaFast = this.trainingData[symbol].SMMA.result;

    // setNormalizeFactor
    this.setNormalizationFactor(symbol, high);

    this.trainingData[symbol].priceBuffer.push(
      smmaFast / this.trainingData[symbol].scale,
    );
    this.trainingData[symbol].volumeBuffer.push(quoteVolume);

    if (this.settings.withAO) {
      this.trainingData[symbol].aoBuffer.push(ao);
    }
    if (this.settings.withMA) {
      this.trainingData[symbol].maBuffer.push(ma);
    }
    if (this.settings.withRSI) {
      this.trainingData[symbol].rsiBuffer.push(rsi);
    }

    if (this.trainingData[symbol].priceBuffer.length < 6) {
      return;
    }

    this.learn(symbol);

    while (
      this.settings.price_buffer_len <
      this.trainingData[symbol].priceBuffer.length
    ) {
      this.trainingData[symbol].priceBuffer.shift();
    }

    if (this.settings.withAO) {
      while (
        this.settings.price_buffer_len <
        this.trainingData[symbol].aoBuffer.length
      ) {
        this.trainingData[symbol].aoBuffer.shift();
      }
    }

    if (this.settings.withRSI) {
      while (
        this.settings.price_buffer_len <
        this.trainingData[symbol].rsiBuffer.length
      ) {
        this.trainingData[symbol].rsiBuffer.shift();
      }
    }

    if (this.settings.withMA) {
      while (
        this.settings.price_buffer_len <
        this.trainingData[symbol].maBuffer.length
      ) {
        this.trainingData[symbol].maBuffer.shift();
      }
    }

    while (
      this.settings.price_buffer_len <
      this.trainingData[symbol].volumeBuffer.length
    ) {
      this.trainingData[symbol].volumeBuffer.shift();
    }
  };

  private setNormalizationFactor = (symbol: string, high) => {
    if (
      this.trainingData[symbol].scale === 1 &&
      high > 1 &&
      this.trainingData[symbol].predictionCount === 0
    ) {
      this.trainingData[symbol].scale = Math.pow(
        10,
        Math.trunc(high).toString().length + 2,
      );
      console.info(
        'Set normalization factor to',
        this.trainingData[symbol].scale,
      );
    }
  };

  /**
   *
   */
  private calculateAdvice = async (
    candle: DbCandleStickDocument,
    symbol: string,
    btcCandles: DbCandleStickDocument[],
  ) => {
    const close = Number(candle.close);
    const currentPrice = close;

    let btcSecond;
    let btcThird;

    let btcSwingBuy;
    let btcSwingSell;

    let btcCrossBuy;
    let btcCrossSell;

    if (this.settings.withBtcSwing || this.settings.withBtcCross) {
      const btcAoValues = calcAORangeFromTimestampNoFilter(
        btcCandles.slice(-36),
      );

      btcSecond = btcAoValues.at(1)?.aoValue;
      btcThird = btcAoValues.at(2)?.aoValue;

      btcSwingBuy = btcThird > btcSecond;
      btcSwingSell = btcThird < btcSecond;

      btcCrossBuy = btcThird > 0;
      btcCrossSell = btcThird < 0;
    }

    try {
      const profit = this.tradeData[symbol].prevPrice
        ? Number(
            (
              (currentPrice / this.tradeData[symbol].prevPrice - 1) *
              100
            ).toFixed(2),
          )
        : 0;
      const currentBotData = this.trainingData[symbol];

      // Check
      if (
        this.trainingData[symbol].predictionCount >
        this.settings.min_predictions
      ) {
        if (
          this.tradeData[symbol].prevAction === 'buy' &&
          this.settings.stoploss_enabled &&
          close <=
            this.tradeData[symbol].prevPrice * this.settings.stoploss_threshold
        ) {
          this.tradeData[symbol].prevAction = 'sell';
          this.tradeData[symbol].prevPrice = close;
          await this.botAdvice(
            'short',
            close,
            0,
            this.settings,
            new Date(candle.closeTime),
          );
        }

        const predictionTemp = currentBotData.nn.forward(
          currentBotData.lastVol,
        );
        // console.log(new Date(candle.openTime).toLocaleString(), "Prediction:", predictionTemp.w[0], "Price:", currentPrice);
        // const vol = new convnetjs.Vol(this.trainingData[symbol].priceBuffer);
        // const predictionTemp = this.trainingData[symbol].nn.forward(vol);

        const prediction = predictionTemp.w[0] * currentBotData.scale;
        const meanp = math.mean(prediction, currentPrice);
        const meanAlpha = ((meanp - currentPrice) / currentPrice) * 100;

        // sell only if the price is higher than the buying price or if the price drops below the threshold
        // a hodle_threshold of 1 will always sell when the NN predicts a drop of the price. play with it!
        const signalSell =
          close > this.tradeData[symbol].prevPrice ||
          close <
            this.tradeData[symbol].prevPrice * currentBotData.hodle_threshold;

        let signal = meanp < currentPrice;

        if (this.settings.withBtcSwing) {
          if (signal === false && btcSwingBuy) {
            signal = false;
          } else if (signal === true || btcSwingSell) {
            signal = true;
          }
        }

        if (this.settings.withBtcCross) {
          if (signal === false && btcCrossBuy) {
            signal = false;
          } else if (signal === true || btcCrossSell) {
            signal = true;
          }
        }

        // BUY
        if (
          this.tradeData[symbol].prevAction !== 'buy' &&
          signal === false &&
          meanAlpha > this.settings.threshold_buy
        ) {
          this.tradeData[symbol].prevAction = 'buy';
          this.tradeData[symbol].prevPrice = currentPrice;

          // console.log(
          //   'TRAIN ' + symbol + ' buy   :',
          //   '\t\t',
          //   new Date(candle.openTime).toLocaleString(),
          // );

          await this.botAdvice(
            'long',
            currentPrice,
            meanAlpha,
            this.settings,
            new Date(candle.closeTime),
          );
        }

        // SELL
        else if (
          this.tradeData[symbol].prevAction !== 'sell' &&
          signal === true &&
          meanAlpha < this.settings.threshold_sell &&
          signalSell
        ) {
          this.tradeData[symbol].prevAction = 'sell';
          this.tradeData[symbol].prevPrice = currentPrice;

          // console.log(
          //   'TRAIN ' + symbol + ' profit:',
          //   profit,
          //   '\t',
          //   new Date(candle.openTime).toLocaleString('de-DE'),
          //   '\t',
          //   (this.tradeData[symbol].profit + profit).toFixed(2),
          // );

          await this.botAdvice(
            'short',
            currentPrice,
            meanAlpha,
            this.settings,
            new Date(candle.closeTime),
          );

          const adviceprofit =
            this.tradeData[symbol].prevPrice &&
            !isNaN(this.tradeData[symbol].prevPrice)
              ? profit
              : 0;
          this.tradeData[symbol].profit += adviceprofit;
        }
      }
    } catch (e) {
      this.logger.errorTester('calculateAdvice', e);
    }
  };

  /**
   * Initialize trainingSymbols with basic data.
   *
   * @param symbol
   */
  private createInitialTrainingData = (symbol: string) => {
    this.tradeData[symbol] = {
      // stores the last action (buy or sell)
      prevAction: 'wait',
      // stores the price of the last trade (buy/sell)
      prevPrice: 0,

      profit: 0,
    };

    this.trainingData[symbol] = {
      // timestamp of last trained candle
      lastTrainedCandle: 0,

      symbol,
      // stores the candles
      priceBuffer: [],
      aoBuffer: [],
      maBuffer: [],
      rsiBuffer: [],
      volumeBuffer: [],
      btcBuffer: [],

      predictionCount: 0,

      // no of neurons for the layer
      layer_neurons: 3,
      // activaction function for the first layer, when neurons are > 0
      layer_activation: this.settings.withAO ? 'relu' : 'tanh', // 'sigmoid', // 'tanh',
      // normalization factor
      scale: 1,

      // if you want the bot to hodl instead of selling during a small dip
      // use the hodle_threshold. e.g. 0.95 means the bot won't sell
      // unless the price drops 5% below the last buy price (this.privPrice)
      hodle_threshold: 1,

      name: 'Neural Network',
      requiredHistory: this.settings.historySize,

      // smooth the input to reduce the noise of the incoming data
      SMMA: new SMMA(5),

      nn: new convnetjs.Net(),
      lastVol: new convnetjs.Vol(0, 0, 0),
    };

    let outDepth = 2;
    if (this.settings.withAO) {
      outDepth++;
    }
    if (this.settings.withMA) {
      outDepth++;
    }
    if (this.settings.withRSI) {
      outDepth++;
    }

    const layers = [
      {
        type: 'input',
        out_sx: 1,
        out_sy: 1,

        // Указываем сколько входящих значений. При добавлении АО глубина будет не 2 (price + volume), а 3.
        out_depth: outDepth,
      },
      {
        type: 'fc',
        num_neurons: this.trainingData[symbol].layer_neurons,
        activation: this.trainingData[symbol].layer_activation,
        drop_prob: 0.1,
      },
      {
        type: 'regression',
        num_neurons: 1,
      },
    ];

    this.trainingData[symbol].nn.makeLayers(layers);

    if (this.settings.strategy === 'sgd') {
      this.trainingData[symbol].trainer = new convnetjs.SGDTrainer(
        this.trainingData[symbol].nn,
        {
          learning_rate: this.settings.learning_rate,
          momentum: this.settings.momentum,
          batch_size: this.settings.batch_size,
          l2_decay: this.settings.decay,
        },
      );
    } else if (this.settings.strategy === 'nesterov') {
      this.trainingData[symbol].trainer = new convnetjs.Trainer(
        this.trainingData[symbol].nn,
        {
          method: this.settings.strategy,
          learning_rate: this.settings.learning_rate,
          momentum: this.settings.momentum,
          batch_size: this.settings.batch_size,
          l2_decay: this.settings.decay,
        },
      );
    } else {
      this.trainingData[symbol].trainer = new convnetjs.Trainer(
        this.trainingData[symbol].nn,
        {
          method: this.settings.strategy,
          batch_size: this.settings.batch_size,
          eps: 1e-6,
          ro: 0.95,
          l2_decay: this.settings.decay,
        },
      );
    }
  };
}
