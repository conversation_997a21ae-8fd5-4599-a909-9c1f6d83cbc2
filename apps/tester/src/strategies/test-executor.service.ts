import { Injectable } from '@nestjs/common';
import { DbStrategyTestDocument } from '@app/database/db-strategy-test/db-strategy-test.model';
import { ArrayUtils } from '../../../../utils/ArrayUtils';
import { calcAORangeFromTimestampNoFilter } from '../../../../indicators/AO';
import { calcRSIFromCandles } from '../../../../indicators/RSI';
import { last } from 'ramda';
import PriceCalculation from '../../../../utils/PriceCalculation';
import { DbCandleStickDocument } from '@app/database/db-candlesticks/db-candlesticks.model';
import { calcEMARangeFromTimestampNoFilter } from '../../../../indicators/EMA';
import { calcADXRangeFromTimestampNoFilter } from '../../../../indicators/ADX';
import { Types } from 'mongoose';
import { DbCandlesticksService } from '@app/database/db-candlesticks/db-candlesticks.service';
import { DbStrategyTestService } from '@app/database/db-strategy-test/db-strategy-test.service';
import { DbIndicatorService } from '@app/database/db-indicator/db-indicator.service';
import { Exchange } from '../../../../types/exchanges';

@Injectable()
export class TestExecutorService {
  constructor(
    private candleService: DbCandlesticksService,
    private strategyService: DbStrategyTestService,
    private dbIndicatorService: DbIndicatorService,
  ) {}

  public async test(
    strat: DbStrategyTestDocument,
    buyFunction: Function,
    sellFunction: Function,
  ) {
    const initialUsd = strat.invest;
    const { _id, symbol, interval, startDate } = strat;

    // Get candles
    const candles = await this.candleService.dbGetCandlesAfter(
      symbol,
      interval,
      startDate,
      strat.exchange,
    );

    // Get btc candles
    const btcCandles = await this.candleService.dbGetCandlesAfter(
      'BTCUSDT',
      interval,
      startDate,
      strat.exchange,
    );

    let currentUsd = initialUsd;
    let prevUsd = 1000;
    let isBuy = false;
    let buyCoinPrice = 0;
    let tradeAmount: number = 0;
    let lastCandlePrice: number = 0;

    let aoMap = ArrayUtils.toMap(
      calcAORangeFromTimestampNoFilter(candles),
      'openTime',
    );

    let btcAoMap = ArrayUtils.toMap(
      calcAORangeFromTimestampNoFilter(btcCandles),
      'openTime',
    );

    let emaValues: any = {};
    if (strat.strategy.includes('ema')) {
      emaValues.ema25 = ArrayUtils.toMap(
        calcEMARangeFromTimestampNoFilter(candles, 25),
        'openTime',
      );
      emaValues.ema50 = ArrayUtils.toMap(
        calcEMARangeFromTimestampNoFilter(candles, 50),
        'openTime',
      );
      emaValues.ema100 = ArrayUtils.toMap(
        calcEMARangeFromTimestampNoFilter(candles, 100),
        'openTime',
      );
      emaValues.ema200 = ArrayUtils.toMap(
        calcEMARangeFromTimestampNoFilter(candles, 200),
        'openTime',
      );
    }

    let adxValues: any = {};
    if (strat.strategy.includes('adx')) {
      adxValues.adx = ArrayUtils.toMap(
        calcADXRangeFromTimestampNoFilter(candles, 14),
        'openTime',
      );
    }

    for (let i = 3; i < candles.length; i++) {
      // Min amount of candles needed to calculate all values
      if (i < 100) {
        continue;
      }
      const cv = this.fillCandleValues(candles, i, btcCandles, aoMap, btcAoMap);
      if (!cv.ao.first || !cv.ao.second || !cv.ao.third) {
        continue;
      }

      if (!this.fillEmaValues(cv, emaValues, adxValues)) {
        continue;
      }
      this.fillRSI(strat, cv);
      this.fillSLTP(cv, strat, buyCoinPrice);

      lastCandlePrice = cv.currentCandlePrice;

      const input = {
        first: cv.ao.first,
        second: cv.ao.second,
        third: cv.ao.third,
        btcFirst: cv.btcAo.first,
        btcSecond: cv.btcAo.second,
        btcThird: cv.btcAo.third,
        ema25: cv.ema25,
        ema50: cv.ema50,
        ema100: cv.ema100,
        ema200: cv.ema200,
        adx: cv.adx,
        price: cv.currentCandlePrice,
        rsi: cv.rsi,
      };

      const tradeInput = {
        cv,
        currentUsd,
        prevUsd,
        symbol,
        isBuy,
        _id,
        tradeAmount,
        candleCount: i,
        candleEndCount: candles.length - 1,
        interval,
        strategy: strat.strategy,
        exchange: strat.exchange,
      };

      const gain = PriceCalculation.toPercentGain(prevUsd, currentUsd);
      if (gain > 1000 || gain < 1000) {
        continue;
      }

      // BUY
      if (buyFunction(input) && !isBuy) {
        isBuy = true;
        tradeInput.isBuy = true;
        buyCoinPrice = cv.currentCandlePrice;
        const result = await this.doTrade({ ...tradeInput, isBuy: true });

        currentUsd = result.currentUsd;
        tradeAmount = result.tradeAmount;
        prevUsd = result.prevUsd;
      }

      // SELL
      else if ((sellFunction(input) || cv.isTp || cv.isSl) && isBuy) {
        isBuy = false;
        const result = await this.doTrade({ ...tradeInput, isBuy: false });

        currentUsd = result.currentUsd;
        tradeAmount = result.tradeAmount;
        prevUsd = result.prevUsd;
      }
    }

    if (tradeAmount != 0) {
      currentUsd = tradeAmount * lastCandlePrice;
      await this.strategyService.updateTrade({
        strategyId: _id,
        endUsd: currentUsd,
        posTrade: null,
        isBuy: null,
        count: candles.length,
        endCount: candles.length,
      });
    }

    console.log(
      symbol,
      `---> End USD: ${currentUsd} / ${PriceCalculation.toSimplePercent(1000, currentUsd)}%`,
    );
  }

  private fillSLTP(
    cv: any,
    strat: DbStrategyTestDocument,
    buyCoinPrice: number,
  ) {
    cv.isTp = TestExecutorService.checkTP(
      strat,
      cv.currentCandlePrice,
      buyCoinPrice,
    );
    cv.isSl = TestExecutorService.checkSL(
      strat,
      cv.currentCandlePrice,
      buyCoinPrice,
    );

    return cv;
  }

  private fillRSI(strat: DbStrategyTestDocument, cv: any) {
    if (strat.strategy.includes('rsi')) {
      const calcRSI = calcRSIFromCandles(cv.candlesToLookAt);

      cv.rsi = last(calcRSI).rsi;
    }

    return cv;
  }

  private fillCandleValues(
    candles: DbCandleStickDocument[],
    i: number,
    btcCandles: DbCandleStickDocument[],
    aoMap: Map<number, any>,
    btcAoMap: Map<number, any>,
  ) {
    const cv: any = {
      candle1: candles.at(i - 3),
      candle2: candles.at(i - 2),
      candle3: candles.at(i - 1),
      currentCandle: candles.at(i),
      candlesToLookAt: candles.slice(0, i - 1),
      btcCandlesToLookAt: btcCandles.slice(0, i - 1),
      ao: {
        first: 0,
        second: 0,
        third: 0,
      },
      btcAo: {
        first: 0,
        second: 0,
        third: 0,
      },
    };

    cv.currentCandlePrice = cv.currentCandle.open;
    cv.ao.first = aoMap.get(cv.candle1.openTime)?.aoValue;
    cv.ao.second = aoMap.get(cv.candle2.openTime)?.aoValue;
    cv.ao.third = aoMap.get(cv.candle3.openTime)?.aoValue;

    cv.btcAo.first = btcAoMap.get(cv.candle1.openTime)?.aoValue;
    cv.btcAo.second = btcAoMap.get(cv.candle2.openTime)?.aoValue;
    cv.btcAo.third = btcAoMap.get(cv.candle3.openTime)?.aoValue;

    return cv;
  }

  private fillEmaValues(cv: any, emaValues: any, adxValues: any) {
    cv.ema25 = emaValues.ema25?.get(cv.candle3.openTime)?.emaValue;
    cv.ema50 = emaValues.ema50?.get(cv.candle3.openTime)?.emaValue;
    cv.ema100 = emaValues.ema100?.get(cv.candle3.openTime)?.emaValue;
    cv.ema200 = emaValues.ema200?.get(cv.candle3.openTime)?.emaValue;
    cv.adx = adxValues.adx?.get(cv.candle3.openTime)?.adxValue;

    return 1;
  }

  private static checkSL(
    strat: DbStrategyTestDocument,
    currentCandlePrice: number,
    buyCoinPrice: number,
  ) {
    let isSl = false;
    if (strat.sl) {
      if (currentCandlePrice <= buyCoinPrice * (1 - strat.sl / 100)) {
        isSl = true;
      }
    }
    return isSl;
  }

  private static checkTP(
    strat: DbStrategyTestDocument,
    currentCandlePrice: number,
    buyCoinPrice: number,
  ) {
    let isTp = false;
    if (strat.tp) {
      if (currentCandlePrice >= buyCoinPrice * (1 + strat.tp / 100)) {
        isTp = true;
      }
    }
    return isTp;
  }

  private async doTrade(args: {
    cv: any;
    currentUsd: number;
    prevUsd: number;
    symbol: string;
    isBuy: boolean;
    _id: Types.ObjectId;
    tradeAmount: number;
    candleCount: number;
    candleEndCount: number;
    interval: string;
    strategy: string;
    exchange: string;
  }) {
    let {
      cv,
      currentUsd,
      prevUsd,
      symbol,
      isBuy,
      _id,
      tradeAmount,
      candleCount,
      candleEndCount,
      interval,
      strategy,
      exchange,
    } = args;

    let { isTp, isSl, currentCandle, currentCandlePrice } = cv;
    const aoValue = cv.ao.third;

    if (!isBuy) {
      currentUsd = tradeAmount * currentCandlePrice;
      const gain = PriceCalculation.toPercentGain(prevUsd, currentUsd);
      prevUsd = currentUsd;
      tradeAmount = 0;

      console.log(
        symbol,
        'SELL' + (isTp ? ' on TP ' : '') + (isSl ? ' on SL ' : ''),
        new Date(currentCandle.openTime).toLocaleString('de-DE'),
        '| Gain:',
        gain,
        '| Coin price:',
        currentCandlePrice,
        '| Current USD:',
        currentUsd,
        '| AO:',
        aoValue,
      );

      const updateIndicator = false;
      if (updateIndicator) {
        await this.dbIndicatorService.addIndicator({
          symbol,
          indicator: strategy,
          action: 'short',
          price: currentCandlePrice,
          timestamp: new Date(),
          candleTime: currentCandle.openTime,
          interval: interval,
          indicatorValue: aoValue,
          exchange: exchange,
        });
      }

      await this.strategyService.updateTrade({
        strategyId: _id,
        endUsd: currentUsd,
        isBuy: false,
        posTrade: gain > 0,
        count: candleCount,
        endCount: candleEndCount,
      });
    } else {
      tradeAmount = currentUsd / currentCandlePrice;
      currentUsd = 0;

      console.log(
        symbol,
        'BUY',
        new Date(currentCandle.openTime).toLocaleString('de-DE'),
        '| Coin price:',
        currentCandlePrice,
        '| Current coins:',
        tradeAmount,
        '| AO:',
        aoValue,
      );

      const updateIndicator = false;
      if (updateIndicator) {
        await this.dbIndicatorService.addIndicator({
          symbol,
          indicator: strategy,
          action: 'long',
          price: currentCandlePrice,
          timestamp: new Date(),
          candleTime: currentCandle.openTime,
          interval: interval,
          indicatorValue: aoValue,
          exchange,
        });
      }

      await this.strategyService.updateTrade({
        strategyId: _id,
        isBuy: true,
        endUsd: currentUsd,
        posTrade: null,
        count: candleCount,
        endCount: candleEndCount,
      });
    }

    return { tradeAmount, currentUsd, prevUsd };
  }
}
