import { Injectable } from '@nestjs/common';
import { DbStrategyTestDocument } from '@app/database/db-strategy-test/db-strategy-test.model';
import { NeuralAdviceService } from './neural.advice.service';
import { NeuralCalcService } from './neural.calc.service';

@Injectable()
export class NeuralNesterovService {
  constructor(
    private neuralnet: NeuralCalcService,
    private botAdviceService: NeuralAdviceService,
  ) {}

  public async testNesterov(
    strat: DbStrategyTestDocument,
    withBtcSwing: boolean,
    withBtcCross: boolean,
    withRSI: boolean,
  ) {
    const { symbol, strategy, invest, interval, startDate, endDate } = strat;

    const settings = {
      symbol,
      exchange: strat.exchange,
      interval,
      invest,
      historySize: 1000,
      threshold_buy: 1.0,
      threshold_sell: -1.0,
      method: 'nesterov',
      strategy,

      learning_rate: 0.01,
      momentum: 0.9,
      decay: 0.01,
      stoploss_enabled: false,
      stoploss_threshold: 0.95,
      hodl_threshold: 1,
      price_buffer_len: 100,
      min_predictions: 1000,
      batch_size: 1,
      startDate,
      endDate,
      testId: strat._id ? strat._id.toString() : null,

      withAO: true,
      withMA: true,
      withBTC: false,
      withRSI: false,
    };

    await this.neuralnet.calculate(
      strategy,
      strat.exchange,
      strat.symbol,
      settings,
      this.botAdviceService.botAdvice,
      withBtcSwing,
      withBtcCross,
      withRSI,
    );
  }
}
