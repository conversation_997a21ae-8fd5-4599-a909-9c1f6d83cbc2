import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { NesterovService } from '../nesterov/nesterov.service';
import { Exchange } from '../../../../types/exchanges';
import { NeuralnetService } from '../neuralnet/neuralnet.service';

@Injectable()
export class TradingBotService {
  constructor(
    private logger: DbLogService,
    private nesterov: NesterovService,
  ) {}

  initFinished = false;
  nesterov120ServiceBinance: NeuralnetService;
  nesterov15ServiceGateio: NeuralnetService;
  nesterov120ServiceGateio: NeuralnetService;

  initTradingBots = async () => {
    this.nesterov120ServiceBinance = await this.nesterov.initialize(
      Exchange.BINANCE,
      '120',
    );
    this.nesterov15ServiceGateio = await this.nesterov.initialize(
      Exchange.GATEIO,
      '15',
    );
    this.nesterov120ServiceGateio = await this.nesterov.initialize(
      Exchange.GATEIO,
      '120',
    );

    this.initFinished = true;
  };

  processTradingBots = async () => {
    if (this.initFinished) {
      this.logger.logBot('Update neural ao data...');
      await this.nesterov120ServiceBinance.update();
      await this.nesterov15ServiceGateio.update();
      await this.nesterov120ServiceGateio.update();
    }
  };
}
