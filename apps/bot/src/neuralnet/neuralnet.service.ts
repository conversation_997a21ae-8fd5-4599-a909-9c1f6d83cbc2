import { Injectable } from '@nestjs/common';
import * as convnetjs from 'convnetjs';
import * as math from 'mathjs';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbExchangeInfoService } from '@app/database/db-exchange-info/db-exchange-info.service';
import { DbCandlesticksService } from '@app/database/db-candlesticks/db-candlesticks.service';
import { DbCandleStickDocument } from '@app/database/db-candlesticks/db-candlesticks.model';
import { isNil, last } from 'ramda';
import { calcMAFromTimestamp } from '../../../../indicators/MA';
import { calcAOFromTimestamp } from '../../../../indicators/AO';
import { DbBotDataService } from '@app/database/db-bot-data/db-bot-data.service';
import SMMA from '../../../../indicators/SMMA';
import { BotAdviceService } from '../bot-advice/bot-advice.service';
import { calcRSIFromTimestamp } from '../../../../indicators/RSI';
import { calcVWAP } from '../../../../indicators/VWAP';
import { plainToInstance } from 'class-transformer';
import { Exchange } from '../../../../types/exchanges';
import { DbNoticedCoinService } from '@app/database/db-noticed-coin/db-noticed-coin.service';

@Injectable()
export class NeuralnetService {
  constructor(
    private logger: DbLogService,
    private exchangeInfoService: DbExchangeInfoService,
    private candleService: DbCandlesticksService,
    private botDataService: DbBotDataService,
    private botAdviceService: BotAdviceService,
    private dbNoticedCoinService: DbNoticedCoinService,
  ) {}

  public initCompleted = false;

  // Internal data for convnet
  public trainingData: any = {};
  public tradeData: any = {};
  public initialTradeData = {
    // stores the last action (buy or sell)
    prevAction: 'wait',
    // stores the price of the last trade (buy/sell)
    prevPrice: 0,
    profit: 0,
  };

  //
  public settings = {
    strategy: null,
    exchange: null,
    historySize: null,
    learning_rate: null,
    momentum: null,
    method: null,
    batch_size: null,
    decay: null,
    interval: null,
    price_buffer_len: null,
    stoploss_enabled: null,
    stoploss_threshold: null,
    threshold_sell: null,
    threshold_buy: null,
    min_predictions: null,
    withAO: null,
    withMA: true,
    withRSI: false,
  };

  public initialize = async (settings: any) => {
    this.settings = { ...settings };

    this.logger.logBot(
      'Training',
      this.settings.exchange,
      this.settings.strategy + this.settings.interval + ' started..',
    );
    const before = new Date();

    let symbols = [];

    if (this.settings.exchange == Exchange.GATEIO) {
      symbols = await this.dbNoticedCoinService.getAllNoticedCoins(
        this.settings.exchange,
      );
    } else {
      symbols = await this.exchangeInfoService.getUsdtSymbolsWithoutExcluded(
        this.settings.exchange,
      );
    }

    for (const symbol of symbols) {
      try {
        const pretrainNeeded = await this.loadOrCreateTrainingData(symbol);

        if (pretrainNeeded) {
          await this.preTrain(symbol);
        }
      } catch (e) {
        this.logger.errorBot(e);
      }
    }

    const after = new Date();

    this.logger.logBot(
      'Pretrain ' +
        this.settings.strategy +
        this.settings.interval +
        ' finished after ' +
        Math.floor((after.getTime() - before.getTime()) / 1000 / 60) +
        ' minutes',
    );

    // Update one time after pretraining
    await this.update();
    this.initCompleted = true;
  };

  /**
   * Called on candle update.
   *
   */
  public update = async () => {
    let symbols = [];
    if (this.settings.exchange == Exchange.GATEIO) {
      symbols = await this.dbNoticedCoinService.getAllNoticedCoins(
        this.settings.exchange,
      );
    } else {
      symbols = await this.exchangeInfoService.getUsdtSymbolsWithoutExcluded(
        this.settings.exchange,
      );
    }

    for (const symbol of symbols) {
      try {
        await this.updateNeuralNetForSymbol(symbol, this.settings.exchange);
      } catch (e) {
        this.logger.errorBot(e);
      }
    }
  };

  /**
   * Pretrain
   *
   * @param symbol
   */
  private preTrain = async (symbol: string) => {
    if (!this.trainingData[symbol]) {
      this.logger.logBot(
        this.settings.exchange,
        this.settings.strategy + this.settings.interval,
        'No need to pretrain step 1 for',
        symbol,
      );

      return;
    }

    // Count candles
    const candleCount: number = await this.candleService.dbCountCandles(
      symbol,
      this.settings.interval,
      this.settings.exchange,
    );

    // Check if we have more than 34 candles (needed for AO calculation)
    if (candleCount && candleCount < 34) {
      return;
    }

    // Get all candles
    const candles: any = await this.candleService.dbGetCandles(
      symbol,
      this.settings.interval,
      this.settings.exchange,
    );

    this.logger.logBot(
      this.settings.exchange,
      this.settings.strategy + this.settings.interval,
      'No training data for symbol.',
      'Pretrain started for',
      symbol,
      candles.length,
      'candles.',
    );

    // Learn
    const vwap = calcVWAP(candles);

    for (const candle of candles) {
      const candleVWAP = vwap.find((x) => x.openTime == candle.openTime);

      if (candleVWAP?.vwap === undefined) {
        continue;
      }

      this.learnWithParameters(candle, symbol, candleVWAP.vwap, candles);
      this.trainingData[symbol].lastTrainedCandle = candle.openTime;

      await this.calculateFakeAdvice(candle, symbol);
    }

    this.logger.logBot(
      this.settings.strategy + this.settings.interval,
      'Finished pretrain step 2 for',
      symbol,
    );

    // Update training data
    await this.botDataService.updateBotTrainingData(
      this.settings.strategy,
      this.settings.interval,
      symbol,
      this.trainingData[symbol],
      this.settings.exchange,
    );
  };

  /**
   * Update neural net.
   *
   * @param symbol
   * @param exchange
   */
  private updateNeuralNetForSymbol = async (
    symbol: string,
    exchange: string,
  ) => {
    const pretrainNeeded = await this.loadOrCreateTrainingData(symbol);

    if (pretrainNeeded) {
      await this.preTrain(symbol);
    }

    // Last candle not found -> skip?
    const lastTrainedCandle = this.trainingData[symbol].lastTrainedCandle;
    if (!lastTrainedCandle) {
      return;
    }

    const newCandles: DbCandleStickDocument[] =
      await this.candleService.dbGetCandlesAfter(
        symbol,
        this.settings.interval,
        lastTrainedCandle,
        this.settings.exchange,
      );

    // No new candles -> skip learning process
    if (newCandles.length == 0) {
      return;
    }

    const last100Candles = await this.candleService.dbGetLastCandles(
      symbol,
      this.settings.interval,
      exchange,
      100,
    );

    const vwap = calcVWAP(last100Candles);

    // Lerne nur neue candles
    for (const candle of newCandles) {
      const candleVWAP = vwap.find((x) => x.openTime == candle.openTime);

      if (candleVWAP?.vwap === undefined) {
        continue;
      }

      this.learnWithParameters(candle, symbol, candleVWAP.vwap, last100Candles);
    }

    await this.calculateAdvice(last(newCandles), symbol);

    await this.botDataService.updateBotTrainingData(
      this.settings.strategy,
      this.settings.interval,
      symbol,
      this.trainingData[symbol],
      this.settings.exchange,
    );
  };

  /**
   * Learn with parameters.
   *
   * @param candle
   * @param symbol
   * @param vwap
   * @param candles
   */
  private learnWithParameters = (
    candle: DbCandleStickDocument,
    symbol: string,
    vwap: number,
    candles: DbCandleStickDocument[],
  ) => {
    let ao = null;
    let ma = null;
    let rsi = null;

    const currentCandleIdx = candles.findIndex(
      (x) => x.openTime === candle.openTime,
    );
    const candlesForCalculation = candles.slice(0, currentCandleIdx + 1);

    if (this.settings.withAO) {
      ao = calcAOFromTimestamp(candle.openTime, candlesForCalculation);

      if (isNil(ao)) {
        return;
      }
    }

    if (this.settings.withMA) {
      ma = calcMAFromTimestamp(candle.openTime, candlesForCalculation);

      if (isNil(ma)) {
        return;
      }
    }

    if (this.settings.withRSI) {
      rsi = calcRSIFromTimestamp(candle.openTime, candlesForCalculation);

      if (isNil(rsi)) {
        return;
      }
    }

    this.learnFromPrevCandles(candle, symbol, vwap, ao, ma, rsi);
  };

  /**
   * Learn
   *
   * @param symbol
   */
  private learn = (symbol: string) => {
    const currentBotData = this.trainingData[symbol];

    for (let i = 0; i < currentBotData.priceBuffer.length - 1; i++) {
      const prevValue = [currentBotData.priceBuffer[i]];
      const currentValue = [currentBotData.priceBuffer[i + 1]];

      prevValue.push(currentBotData.volumeBuffer[i]);

      if (this.settings.withAO) {
        prevValue.push(currentBotData.aoBuffer[i]);
      }

      if (this.settings.withMA) {
        prevValue.push(currentBotData.maBuffer[i]);
      }

      if (this.settings.withRSI) {
        prevValue.push(currentBotData.rsiBuffer[i]);
      }

      /**
       * Если указан индикатор АО, то предыдущую цену плюс показатель АО
       * тренируем на новую цену. Тоесть мы говорим сети на какую цену он должен делать
       * регрессию этих двух значениий.
       *
       * Если помимо АО есть другие входящие, то указываем их как элементы prevValue
       */
      const vol = new convnetjs.Vol(prevValue);
      currentBotData.lastVol = vol;

      /**
       * Тренирум сеть [x,y] -> z
       */
      currentBotData.trainer.train(vol, currentValue);
      currentBotData.predictionCount += 1;
    }
  };

  /**
   * Learn from prev candles
   * @param candle
   * @param symbol
   * @param vwap
   * @param ao
   * @param ma
   * @param rsi
   */
  private learnFromPrevCandles = (
    candle: DbCandleStickDocument,
    symbol: string,
    vwap: number,
    ao: number,
    ma: number,
    rsi: number,
  ) => {
    const high = Number(candle.high);
    const low = Number(candle.low);
    const close = Number(candle.close);
    const quoteVolume = Number(candle.quoteVolume);

    const currentBotData = this.trainingData[symbol];

    // Update SMMA
    if (this.settings.withMA) {
      currentBotData.SMMA.update((high + close + low + vwap + ma) / 5);
    } else {
      currentBotData.SMMA.update((high + close + low + vwap) / 4);
    }

    // Return SMMA Fast
    const smmaFast = currentBotData.SMMA.result;

    // setNormalizeFactor
    this.setNormalizationFactor(symbol, high);

    currentBotData.priceBuffer.push(smmaFast / currentBotData.scale);
    currentBotData.volumeBuffer.push(quoteVolume);

    if (this.settings.withAO) {
      currentBotData.aoBuffer.push(ao);
    }
    if (this.settings.withMA) {
      currentBotData.maBuffer.push(ma);
    }
    if (this.settings.withRSI) {
      currentBotData.rsiBuffer.push(rsi);
    }

    if (currentBotData.priceBuffer.length < 6) {
      return;
    }

    this.learn(symbol);

    while (this.settings.price_buffer_len < currentBotData.priceBuffer.length) {
      currentBotData.priceBuffer.shift();
    }

    if (this.settings.withAO) {
      while (this.settings.price_buffer_len < currentBotData.aoBuffer.length) {
        currentBotData.aoBuffer.shift();
      }
    }

    if (this.settings.withRSI) {
      while (this.settings.price_buffer_len < currentBotData.rsiBuffer.length) {
        currentBotData.rsiBuffer.shift();
      }
    }

    if (this.settings.withMA) {
      while (this.settings.price_buffer_len < currentBotData.maBuffer.length) {
        currentBotData.maBuffer.shift();
      }
    }

    while (
      this.settings.price_buffer_len < currentBotData.volumeBuffer.length
    ) {
      currentBotData.volumeBuffer.shift();
    }
  };

  /**
   * Set normalization factor.
   *
   * @param symbol
   * @param high
   */
  private setNormalizationFactor = (symbol: string, high: number) => {
    if (
      this.trainingData[symbol].scale === 1 &&
      high > 1 &&
      this.trainingData[symbol].predictionCount === 0
    ) {
      this.trainingData[symbol].scale = Math.pow(
        10,
        Math.trunc(high).toString().length + 2,
      );
      this.logger.logBot(
        'Set normalization factor to',
        this.trainingData[symbol].scale,
      );
    }
  };

  /**
   * Calculate advice
   *
   * @param candle
   * @param symbol
   */
  private calculateAdvice = async (
    candle: DbCandleStickDocument,
    symbol: string,
  ) => {
    const close = Number(candle.close);
    const currentPrice = close;

    try {
      const currentBotData = this.trainingData[symbol];

      // Check
      if (currentBotData.predictionCount < this.settings.min_predictions) {
        return;
      }

      if (
        this.settings.stoploss_enabled &&
        this.tradeData[symbol].prevAction === 'buy' &&
        close <=
          this.tradeData[symbol].prevPrice * this.settings.stoploss_threshold
      ) {
        this.logger.logBot('>>>>>>>>>> STOPLOSS triggered <<<<<<<<<<');

        await this.saveTrade('stop', symbol, currentPrice);
        await this.botAdviceService.botAdvice({
          strategy: this.settings.strategy,
          symbol,
          interval: this.settings.interval,
          advice: 'short',
          price: close,
          prediction: null,
          candle,
          exchange: this.settings.exchange,
        });
      }

      const predictionTemp = currentBotData.nn.forward(currentBotData.lastVol);
      const prediction = predictionTemp.w[0] * currentBotData.scale;
      const meanp = math.mean(prediction, currentPrice);
      const meanAlpha = ((meanp - currentPrice) / currentPrice) * 100;

      // sell only if the price is higher than the buying price or if the price drops below the threshold
      // a hodle_threshold of 1 will always sell when the NN predicts a drop of the price. play with it!
      const signalSell =
        close > this.tradeData[symbol].prevPrice ||
        close <
          this.tradeData[symbol].prevPrice * currentBotData.hodle_threshold;

      const signal = meanp < currentPrice;

      // BUY
      if (
        this.tradeData[symbol].prevAction !== 'buy' &&
        this.tradeData[symbol].prevAction !== 'stop' &&
        signal === false &&
        meanAlpha > this.settings.threshold_buy
      ) {
        // Check if price > 0.00000100
        if (currentPrice > 0.000001) {
          // this.logger.logBot(
          //   this.settings.exchange,
          //   `ADVICE ${this.settings.strategy}${this.settings.interval}, Buy ${symbol}, Predicted price:`,
          //   meanp,
          //   ', PrevAction:',
          //   this.tradeData[symbol].prevAction,
          //   ', PrevPrice:',
          //   this.tradeData[symbol].prevPrice,
          // );

          await this.saveTrade('buy', symbol, currentPrice);
          await this.botAdviceService.botAdvice({
            strategy: this.settings.strategy,
            symbol,
            interval: this.settings.interval,
            advice: 'long',
            price: currentPrice,
            prediction: meanp,
            candle,
            exchange: this.settings.exchange,
          });
        }
      }

      // SELL
      else if (
        this.tradeData[symbol].prevAction !== 'sell' &&
        signal === true &&
        meanAlpha < this.settings.threshold_sell &&
        signalSell
      ) {
        if (this.tradeData[symbol].prevAction === 'stop') {
          this.tradeData[symbol].prevAction = 'sell';
          await this.botDataService.updateBotTradeData(
            this.settings.strategy,
            symbol,
            this.settings.interval,
            this.settings.exchange,
            this.tradeData[symbol],
          );
          return;
        }

        // this.logger.logBot(
        //   symbol,
        //   '->',
        //   this.settings.exchange,
        //   `ADVICE ${this.settings.strategy}${this.settings.interval}, SELL. Predicted change:`,
        //   meanp,
        //   ', PrevAction:',
        //   this.tradeData[symbol].prevAction,
        //   ', PrevPrice:',
        //   this.tradeData[symbol].prevPrice,
        //   ', PrevProfit:',
        //   this.tradeData[symbol].profit,
        // );

        await this.saveTrade('sell', symbol, currentPrice);
        await this.botAdviceService.botAdvice({
          strategy: this.settings.strategy,
          symbol,
          interval: this.settings.interval,
          advice: 'short',
          price: currentPrice,
          prediction: meanp,
          candle,
          exchange: this.settings.exchange,
        });
      }
    } catch (e) {
      this.logger.errorBot('calculateAdvice', e);
    }
  };

  /**
   * Calculate fake advice
   *
   * @param candle
   * @param symbol
   *
   */
  private calculateFakeAdvice = async (
    candle: DbCandleStickDocument,
    symbol: string,
  ) => {
    const close = Number(candle.close);
    const currentPrice = close;

    try {
      const currentBotData = this.trainingData[symbol];

      // Check
      if (currentBotData.predictionCount > this.settings.min_predictions) {
        if (
          this.settings.stoploss_enabled &&
          this.tradeData[symbol].prevAction === 'buy' &&
          close <=
            this.tradeData[symbol].prevPrice * this.settings.stoploss_threshold
        ) {
          const profit = Number(
            (
              (currentPrice / this.tradeData[symbol].prevPrice - 1) *
              100
            ).toFixed(2),
          );
          this.logger.logBot(
            this.settings.exchange,
            this.settings.strategy + this.settings.interval,
            'TRAIN ' + symbol + ' stoploss:',
            profit,
            '\t',
            new Date(candle.openTime).toLocaleString('de-DE'),
            '\t',
            (this.tradeData[symbol].profit + profit).toFixed(2),
          );
          await this.saveTrade('stop', symbol, currentPrice);
        }

        const predictionTemp = currentBotData.nn.forward(
          currentBotData.lastVol,
        );

        const prediction = predictionTemp.w[0] * currentBotData.scale;
        const meanp = math.mean(prediction, currentPrice);
        const meanAlpha = ((meanp - currentPrice) / currentPrice) * 100;

        // sell only if the price is higher than the buying price or if the price drops below the threshold
        // a hodle_threshold of 1 will always sell when the NN predicts a drop of the price. play with it!
        const signalSell =
          close > this.tradeData[symbol].prevPrice ||
          close <
            this.tradeData[symbol].prevPrice * currentBotData.hodle_threshold;

        const signal = meanp < currentPrice;

        // BUY
        if (
          this.tradeData[symbol].prevAction !== 'buy' &&
          this.tradeData[symbol].prevAction !== 'stop' &&
          signal === false &&
          meanAlpha > this.settings.threshold_buy
        ) {
          if (currentPrice > 0.000001) {
            this.logger.logBot(
              this.settings.exchange,
              this.settings.strategy + this.settings.interval,
              'TRAIN ' + symbol + ' buy   :',
              '\t\t',
              new Date(candle.openTime).toLocaleString('de-DE'),
            );
            await this.saveTrade('buy', symbol, currentPrice);
          }
        }

        // SELL
        else if (
          this.tradeData[symbol].prevAction !== 'sell' &&
          signal === true &&
          meanAlpha < this.settings.threshold_sell &&
          signalSell
        ) {
          if (this.tradeData[symbol].prevAction === 'stop') {
            this.tradeData[symbol].prevAction = 'sell';
            await this.botDataService.updateBotTradeData(
              this.settings.strategy,
              symbol,
              this.settings.interval,
              this.settings.exchange,
              this.tradeData[symbol],
            );
            return;
          }

          const profit = Number(
            (
              (currentPrice / this.tradeData[symbol].prevPrice - 1) *
              100
            ).toFixed(2),
          );
          this.logger.logBot(
            this.settings.exchange,
            this.settings.strategy + this.settings.interval,
            'TRAIN ' + symbol + ' profit:',
            profit,
            '\t',
            new Date(candle.openTime).toLocaleString('de-DE'),
            '\t',
            (this.tradeData[symbol].profit + profit).toFixed(2),
          );

          await this.saveTrade('sell', symbol, currentPrice);
        }
      }
    } catch (e) {
      this.logger.errorBot('calculateAdvice', e);
    }
  };

  /**
   * Initialize trainingSymbols with basic data.
   *
   * @param symbol
   */
  private createInitialTrainingData = async (symbol: string) => {
    this.trainingData[symbol] = {
      // timestamp of last trained candle
      lastTrainedCandle: 0,

      symbol,
      // stores the candles
      priceBuffer: [],
      aoBuffer: [],
      maBuffer: [],
      rsiBuffer: [],
      volumeBuffer: [],
      btcBuffer: [],

      predictionCount: 0,

      // no of neurons for the layer
      layer_neurons: 3,
      // activaction function for the first layer, when neurons are > 0
      layer_activation: this.settings.withAO ? 'relu' : 'tanh', // 'sigmoid', // 'tanh',
      // normalization factor
      scale: 1,

      // if you want the bot to hodl instead of selling during a small dip
      // use the hodle_threshold. e.g. 0.95 means the bot won't sell
      // unless the price drops 5% below the last buy price (this.privPrice)
      hodle_threshold: 1,

      name: 'Neural Network',
      requiredHistory: this.settings.historySize,

      // smooth the input to reduce the noise of the incoming data
      SMMA: new SMMA(5),

      nn: new convnetjs.Net(),
      lastVol: new convnetjs.Vol(0, 0, 0),
    };

    let outDepth = 2;
    if (this.settings.withAO) {
      outDepth++;
    }
    if (this.settings.withMA) {
      outDepth++;
    }
    if (this.settings.withRSI) {
      outDepth++;
    }

    const layers = [
      {
        type: 'input',
        out_sx: 1,
        out_sy: 1,

        // Указываем сколько входящих значений. При добавлении АО глубина будет не 2 (price + volume), а 3.
        out_depth: outDepth,
      },
      {
        type: 'fc',
        num_neurons: this.trainingData[symbol].layer_neurons,
        activation: this.trainingData[symbol].layer_activation,
        drop_prob: 0.1,
      },
      {
        type: 'regression',
        num_neurons: 1,
      },
    ];

    this.trainingData[symbol].nn.makeLayers(layers);

    if (this.settings.strategy === 'sgd') {
      this.trainingData[symbol].trainer = new convnetjs.SGDTrainer(
        this.trainingData[symbol].nn,
        {
          learning_rate: this.settings.learning_rate,
          momentum: this.settings.momentum,
          batch_size: this.settings.batch_size,
          l2_decay: this.settings.decay,
        },
      );
    } else if (this.settings.strategy === 'nesterov') {
      this.trainingData[symbol].trainer = new convnetjs.Trainer(
        this.trainingData[symbol].nn,
        {
          method: this.settings.strategy,
          learning_rate: this.settings.learning_rate,
          momentum: this.settings.momentum,
          batch_size: this.settings.batch_size,
          l2_decay: this.settings.decay,
        },
      );
    } else {
      this.trainingData[symbol].trainer = new convnetjs.Trainer(
        this.trainingData[symbol].nn,
        {
          method: this.settings.strategy,
          batch_size: this.settings.batch_size,
          eps: 1e-6,
          ro: 0.95,
          l2_decay: this.settings.decay,
        },
      );
    }

    await this.botDataService.updateBotTrainingData(
      this.settings.strategy,
      this.settings.interval,
      symbol,
      this.trainingData[symbol],
      this.settings.exchange,
    );
  };

  private async loadOrCreateTrainingData(symbol: string): Promise<boolean> {
    if (this.trainingData[symbol] && this.tradeData[symbol]) {
      return;
    }

    // Get trade and training data
    const botData = await this.botDataService.getBotData(
      this.settings.strategy,
      symbol,
      this.settings.interval,
      this.settings.exchange,
    );

    let pretrainNeeded = false;
    if (!botData) {
      pretrainNeeded = true;
    }

    if (!botData?.tradeData) {
      await this.createInitialTradeData(symbol);
    } else {
      this.tradeData[symbol] = botData?.tradeData;
    }

    if (!botData?.trainingData) {
      await this.createInitialTrainingData(symbol);
    } else {
      await this.createInitialTrainingData(symbol);
      this.replaceTrainingData(symbol, botData.trainingData);
    }

    return pretrainNeeded;
  }

  /**
   * Create initial trade data
   *
   * @param symbol
   * @private
   */
  private async createInitialTradeData(symbol: string) {
    if (!this.tradeData[symbol]) {
      this.logger.logBot(
        this.settings.exchange,
        this.settings.strategy + this.settings.interval,
        'Creating init data for symbol',
        symbol,
      );
      this.tradeData[symbol] = { ...this.initialTradeData };

      await this.botDataService.updateBotTradeData(
        this.settings.strategy,
        symbol,
        this.settings.interval,
        this.settings.exchange,
        this.tradeData[symbol],
      );
    }
  }

  /**
   * Replace local training data with db data.
   *
   * @param symbol
   * @param savedData
   * @private
   */
  private replaceTrainingData(symbol: string, savedData: any) {
    this.logger.logBot('Replacing training data for ' + symbol);
    const data = this.trainingData[symbol];

    data.lastTrainedCandle = savedData.lastTrainedCandle;
    data.symbol = savedData.symbol;
    data.priceBuffer = savedData.priceBuffer;
    data.aoBuffer = savedData.aoBuffer;
    data.maBuffer = savedData.maBuffer;
    data.rsiBuffer = savedData.rsiBuffer;
    data.volumeBuffer = savedData.volumeBuffer;
    data.btcBuffer = savedData.btcBuffer;
    data.predictionCount = savedData.predictionCount;
    data.layer_neurons = savedData.layer_neurons;
    data.layer_activation = savedData.layer_activation;
    data.scale = savedData.scale;
    data.hodle_threshold = savedData.hodle_threshold;
    data.name = savedData.name;
    data.requiredHistory = savedData.requiredHistory;

    data.SMMA.input = savedData.SMMA.input;
    data.SMMA.sma.weight = savedData.SMMA.sma.weight;
    data.SMMA.sma.prices = savedData.SMMA.sma.prices;
    data.SMMA.sma.result = savedData.SMMA.sma.result;
    data.SMMA.sma.age = savedData.SMMA.sma.age;
    data.SMMA.weight = savedData.SMMA.weight;
    data.SMMA.prices = savedData.SMMA.prices;
    data.SMMA.result = savedData.SMMA.result;
    data.SMMA.age = savedData.SMMA.age;

    // --
    data.nn = plainToInstance(convnetjs.Net, savedData.nn, {
      enableImplicitConversion: true,
    });

    for (let i = 0; i < savedData.nn.layers.length; i++) {
      if (i == 0) {
        data.nn.layers[i] = plainToInstance(
          convnetjs.InputLayer,
          savedData.nn.layers[i],
        );
      }

      if (i == 1) {
        data.nn.layers[i] = plainToInstance(
          convnetjs.FullyConnLayer,
          savedData.nn.layers[i],
        );
      }

      if (i == 2) {
        data.nn.layers[i] = plainToInstance(
          convnetjs.ReluLayer,
          savedData.nn.layers[i],
        );
      }

      if (i == 3) {
        data.nn.layers[i] = plainToInstance(
          convnetjs.DropoutLayer,
          savedData.nn.layers[i],
        );
      }

      if (i == 4) {
        data.nn.layers[i] = plainToInstance(
          convnetjs.FullyConnLayer,
          savedData.nn.layers[i],
        );
      }

      if (i == 5) {
        data.nn.layers[i] = plainToInstance(
          convnetjs.RegressionLayer,
          savedData.nn.layers[i],
        );
      }

      this.fillLayers(data.nn.layers[i], savedData.nn.layers[i]);
    }

    data.lastVol = plainToInstance(convnetjs.Vol, savedData.lastVol, {
      enableImplicitConversion: true,
    });

    if (data.lastVol.w) {
      data.lastVol.w = plainToInstance(Float64Array, data.lastVol.w);
    }

    if (data.lastVol.dw) {
      data.lastVol.dw = plainToInstance(Float64Array, data.lastVol.dw);
    }

    data.trainer = plainToInstance(convnetjs.Trainer, savedData.trainer);
    data.trainer.net = data.nn;

    for (let i = 0; i < savedData.trainer.gsum.length; i++) {
      data.trainer.gsum[i] = Float64Array.from(
        Object.values(savedData.trainer.gsum[i]),
      );
    }
    data.trainer.xsum = savedData.trainer.xsum;
  }

  /**
   * Fill training layers.
   *
   * @param layer
   * @param savedLayer
   * @private
   */
  private fillLayers(layer: any, savedLayer: any) {
    if (savedLayer.filters) {
      layer.filters = savedLayer.filters.map((x) => {
        const filter: convnetjs.Vol = plainToInstance(convnetjs.Vol, x);

        if (filter.w) {
          filter.w = Float64Array.from(Object.values(filter.w));
        }
        if (filter.dw) {
          filter.dw = Float64Array.from(Object.values(filter.dw));
        }

        return filter;
      });
    }

    if (savedLayer.biases) {
      layer.biases = plainToInstance(convnetjs.Vol, savedLayer.biases);

      if (layer.biases.w) {
        layer.biases.w = Float64Array.from(Object.values(savedLayer.biases.w));
      }
      if (layer.biases.dw) {
        layer.biases.dw = Float64Array.from(
          Object.values(savedLayer.biases.dw),
        );
      }
    }

    if (savedLayer.in_act) {
      layer.in_act = plainToInstance(convnetjs.Vol, savedLayer.in_act);
      layer.in_act.w = Float64Array.from(Object.values(savedLayer.in_act.w));
      layer.in_act.dw = Float64Array.from(Object.values(savedLayer.in_act.dw));
    }

    if (savedLayer.out_act) {
      layer.out_act = plainToInstance(convnetjs.Vol, savedLayer.out_act);
      layer.out_act.w = Float64Array.from(Object.values(savedLayer.out_act.w));
      layer.out_act.dw = Float64Array.from(
        Object.values(savedLayer.out_act.dw),
      );
    }

    if (layer.dropped) {
      layer.dropped = Float64Array.from(Object.values(savedLayer.dropped));
    }
  }

  /**
   * Save bot data.
   *
   * @param action
   * @param symbol
   * @param price
   */
  private saveTrade = async (action: string, symbol: string, price: number) => {
    if (!this.tradeData[symbol]) {
      this.logger.errorBot('No init data for symbol', symbol);
      return null;
    }

    if (action === 'sell' || action === 'stop') {
      const profit = this.tradeData[symbol].prevPrice
        ? Number(
            ((price / this.tradeData[symbol].prevPrice - 1) * 100).toFixed(2),
          )
        : 0;
      this.tradeData[symbol].profit += profit;
    }

    this.tradeData[symbol].prevAction = action;
    this.tradeData[symbol].prevPrice = price;
    this.tradeData[symbol].timestamp = new Date();

    this.tradeData[symbol].prevAction = action;
    this.tradeData[symbol].prevPrice = price;

    await this.botDataService.updateBotTradeData(
      this.settings.strategy,
      symbol,
      this.settings.interval,
      this.settings.exchange,
      this.tradeData[symbol],
    );
  };
}
