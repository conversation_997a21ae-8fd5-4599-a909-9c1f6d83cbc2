import { Modu<PERSON> } from '@nestjs/common';
import { DatabaseModule } from '@app/database';
import { BotService } from './bot.service';
import { JobsService } from './jobs.service';
import { TradingBotService } from './trading-bot/trading-bot.service';
import { NesterovService } from './nesterov/nesterov.service';
import { NeuralnetService } from './neuralnet/neuralnet.service';
import { BotAdviceService } from './bot-advice/bot-advice.service';
import { ScheduleModule } from '@nestjs/schedule';

@Module({
  imports: [DatabaseModule, ScheduleModule.forRoot()],
  controllers: [],
  providers: [
    BotService,
    JobsService,
    TradingBotService,
    NesterovService,
    NeuralnetService,
    BotAdviceService,
  ],
})
export class BotModule {}
