import { Injectable } from '@nestjs/common';
import { TradingBotService } from './trading-bot/trading-bot.service';
import { <PERSON>ron } from '@nestjs/schedule';
import { Exchange } from '../../../types/exchanges';

@Injectable()
export class JobsService {
  constructor(private tradingBotService: TradingBotService) {}

  @Cron('30 */10 * * * *')
  async processTradingBotsBinance() {
    await this.tradingBotService.processTradingBots();
  }

  async startJobs() {
    await this.tradingBotService.initTradingBots();
  }
}
