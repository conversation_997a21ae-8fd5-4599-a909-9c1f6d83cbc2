import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbIndicatorDocument } from '@app/database/db-indicator/db-indicator.model';
import { DbIndicatorService } from '@app/database/db-indicator/db-indicator.service';

@Injectable()
export class BotAdviceService {
  constructor(
    private logger: DbLogService,
    private indicatorService: DbIndicatorService,
  ) {}

  public botAdvice = async ({
    strategy,
    symbol,
    advice,
    interval,
    price,
    prediction,
    candle,
    exchange,
  }) => {
    try {
      // this.logger.logBot(
      //   symbol,
      //   '->',
      //   'New Advice',
      //   exchange,
      //   strategy,
      //   interval,
      //   advice,
      //   'Candle Time:',
      //   new Date(candle.openTime).toISOString(),
      // );
      let profit: number;

      const prevLong = await this.indicatorService.getLastIndicatorAction(
        symbol,
        interval,
        strategy,
        exchange,
      );

      if (prevLong && advice === 'short' && price) {
        profit = Number(
          parseFloat(String((price / prevLong.price - 1) * 100)).toFixed(2),
        );
      }

      const newData = {
        timestamp: new Date(),
        interval,
        symbol,
        candleTime: candle.openTime,
        indicator: 'nesterov',
        indicatorValue: prediction,
        action: advice,
        price,
        profit,
        newAdded: true,
        exchange,
      } as DbIndicatorDocument;

      await this.indicatorService.addIndicator(newData);
    } catch (e) {
      this.logger.errorBot('botAdvice', e);
    }
  };
}
