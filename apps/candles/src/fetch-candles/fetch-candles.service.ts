import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbExchangeInfoService } from '@app/database/db-exchange-info/db-exchange-info.service';
import { DbCandlesticksService } from '@app/database/db-candlesticks/db-candlesticks.service';
import { CandleChartInterval, CandleChartInterval_LT } from 'binance-api-node';
import { BinanceCandleSticksService } from '@app/binance-api/binance-candle-sticks/binance-candle-sticks.service';
import { Exchange } from '../../../../types/exchanges';
import { GateioCandleSticksService } from '@app/gateio-api/gateio-candle-sticks/gateio-candle-sticks.service';
import { DbFavoriteService } from '@app/database/db-favorite/db-favorite.service';
import { DbNoticedCoinService } from '@app/database/db-noticed-coin/db-noticed-coin.service';

@Injectable()
export class FetchCandlesService {
  constructor(
    private logger: DbLogService,
    private exchangeInfoService: DbExchangeInfoService,
    private candlesticksService: DbCandlesticksService,
    private binanceCandleSticksService: BinanceCandleSticksService,
    private gateioCandleSticksService: GateioCandleSticksService,
    private dbNoticedCoinService: DbNoticedCoinService,
  ) {}

  async fetchAllCandles(interval: string, size: number, exchange: string) {
    const symbols = await this.exchangeInfoService.getUsdtSymbols(exchange);
    const symbolLength = symbols.length;

    // this.logger.logCandles(
    //   exchange,
    //   `Start fetching candles for ${symbolLength} symbols with interval ${interval}`,
    // );

    const blockSize = 1;
    for (let i = 0; i < symbols.length; i += blockSize) {
      const chunk = symbols.slice(i, i + blockSize);
      const promises = chunk.map((symbol) =>
        this.fetchCandleForSymbol(symbol, interval, size, exchange),
      );
      const result = await Promise.all(promises);
    }

    // this.logger.logCandles(
    //   exchange,
    //   `End fetching candles for ${symbolLength} symbols with interval ${interval}`,
    // );
  }

  async fetchWhitelistedCandles(
    interval: string,
    size: number,
    exchange: string,
  ) {
    const symbols =
      await this.dbNoticedCoinService.getAllNoticedCoins(exchange);
    const symbolLength = symbols.length;

    this.logger.logCandles(
      exchange,
      `Start fetching candles for ${symbolLength} whitelisted symbols with interval ${interval}`,
    );

    const blockSize = 1;
    for (let i = 0; i < symbols.length; i += blockSize) {
      const chunk = symbols.slice(i, i + blockSize);
      const promises = chunk.map((symbol) =>
        this.fetchCandleForSymbol(symbol, interval, size, exchange),
      );
      await Promise.all(promises);
    }

    this.logger.logCandles(
      exchange,
      `End fetching candles for ${symbolLength} whitelisted symbols with interval ${interval}`,
    );
  }

  async fetchAllCandles1m(exchange: string) {
    const symbols = await this.exchangeInfoService.getUsdtSymbols(exchange);
    const symbolLength = symbols.length;

    // this.logger.logCandles(
    //   exchange,
    //   `Start fetching candles for ${symbolLength} symbols with interval 1m`,
    // );

    for (const symbol of symbols) {
      try {
        await this.fetchCandle1m(symbol, exchange);
      } catch (e) {
        this.logger.errorCandles(symbol, e);
      }
    }

    // this.logger.logCandles(
    //   exchange,
    //   `End fetching candles for ${symbolLength} symbols with interval 1m`,
    // );
  }

  async fetchCandleForSymbol(
    symbol: string,
    interval: string,
    size: number,
    exchange: string,
  ): Promise<string> {
    try {
      const lastCandle = await this.candlesticksService.dbGetLastCandle(
        symbol,
        interval,
        exchange,
      );

      const fetchCount = lastCandle != null ? size : 1000;
      if (lastCandle == null) {
        this.logger.logCandles(symbol, exchange, ': Initial fetching');
      } else {
      }

      let fetchedCandles = [];
      let candlesToInsert: any[];
      let updatedLastCandles: any[];

      if (lastCandle) {
        switch (exchange) {
          case Exchange.BINANCE:
            fetchedCandles =
              await this.binanceCandleSticksService.getCandleSticks({
                symbol,
                interval: this.mapInterval(interval),
                limit: fetchCount,
                startTime: lastCandle.openTime,
              });
            break;

          case Exchange.GATEIO:
            fetchedCandles =
              await this.gateioCandleSticksService.getCandleSticks({
                symbol,
                interval: this.mapInterval(interval),
                limit: fetchCount,
                startTime: lastCandle.openTime,
              });
        }
        candlesToInsert = fetchedCandles
          .filter(
            (fetchedCandle) => fetchedCandle.openTime > lastCandle.openTime,
          )
          .map((candle) => ({
            ...candle,
            timestamp: new Date(),
            symbol,
            interval,
            exchange,
          }));

        updatedLastCandles = fetchedCandles
          .filter(
            (fetchedCandle) => fetchedCandle.openTime === lastCandle.openTime,
          )
          .map((candle) => ({
            ...candle,
            timestamp: new Date(),
            symbol,
            interval,
            exchange,
          }));

        await this.candlesticksService.updateLastCandleValue(
          updatedLastCandles,
          symbol,
          interval,
          exchange,
        );

        console.log(
          `Inserted ${candlesToInsert.length} candles for ${symbol} interval ${interval} on ${exchange}`,
        );
        await this.candlesticksService.dbCandlesInsertMany(candlesToInsert);

        return symbol;
      } else {
        switch (exchange) {
          case Exchange.BINANCE:
            fetchedCandles =
              await this.binanceCandleSticksService.getCandleSticks({
                symbol,
                interval: this.mapInterval(interval),
                limit: fetchCount,
              });
            break;

          case Exchange.GATEIO:
            fetchedCandles =
              await this.gateioCandleSticksService.getCandleSticks({
                symbol,
                interval: this.mapInterval(interval),
                limit: fetchCount,
              });
        }

        candlesToInsert = fetchedCandles.map((candle) => ({
          ...candle,
          timestamp: new Date(),
          symbol,
          interval,
          exchange,
        }));
        await this.candlesticksService.dbCandlesInsertMany(candlesToInsert);
        return symbol;
      }
    } catch (e) {
      this.logger.errorServer(e);
    }
  }

  async fetchHistoricalCandlesForAllSymbols(
    interval: string,
    startTime: number,
    exchange: string,
  ) {
    const symbols =
      await this.exchangeInfoService.getUsdtSymbolsWithoutExcluded(exchange);

    for (const symbol of symbols) {
      try {
        await this.fetchHistoricalCandles(
          symbol,
          interval,
          startTime,
          exchange,
        );
      } catch (e) {
        this.logger.errorCandles(symbol, e);
      }
    }
  }

  async fetchHistoricalCandlesForWhitelistedSymbols(
    interval: string,
    startTime: number,
    exchange: string,
  ) {
    const symbols =
      await this.dbNoticedCoinService.getAllNoticedCoins(exchange);
    for (const symbol of symbols) {
      try {
        await this.fetchHistoricalCandles(
          symbol,
          interval,
          startTime,
          exchange,
        );
      } catch (e) {
        this.logger.errorCandles(symbol, e);
      }
    }
  }

  async fetchHistoricalCandles(
    symbol: string,
    interval: string,
    startTime: number,
    exchange: string,
  ) {
    const limit = 500;
    const prevCandles = await this.candlesticksService.dbGetCandles(
      symbol,
      interval,
      exchange,
    );
    const prevTimes = new Set(prevCandles.map((x) => x.openTime));

    const currentTime = new Date().getTime();

    try {
      while (startTime < currentTime) {
        let fetchedCandles = null;
        switch (exchange) {
          case Exchange.BINANCE:
            fetchedCandles =
              await this.binanceCandleSticksService.getCandleSticks({
                symbol,
                interval: this.mapInterval(interval),
                limit: limit,
                startTime: startTime,
              });
            break;
          case Exchange.GATEIO:
            fetchedCandles =
              await this.gateioCandleSticksService.getCandleSticks({
                symbol,
                interval: this.mapInterval(interval),
                limit: limit,
                startTime: startTime,
              });
            break;
        }

        // Add only new candles
        const candlesToInsert: any = fetchedCandles
          .filter((fetchedCandle) => !prevTimes.has(fetchedCandle.openTime))
          .map((candle) => ({
            ...candle,
            timestamp: new Date(),
            symbol,
            interval,
            exchange,
          }));

        this.logger.logCandles(
          symbol,
          exchange,
          `Inserting ${candlesToInsert.length} historical candles. Start Time: ${startTime}. Interval: ${interval}`,
        );

        await this.candlesticksService.dbCandlesInsertMany(candlesToInsert);

        if (
          fetchedCandles.size > 0 &&
          fetchedCandles.at(0).openTime > startTime
        ) {
          startTime = fetchedCandles.at(0).openTime;
        }

        switch (interval) {
          case '15':
            startTime += (3600000 / 4) * limit;
            break;
          case '60':
            startTime += 3600000 * limit;
            break;
          case '120':
            startTime += 3600000 * 2 * limit;
            break;
          case '240':
            startTime += 3600000 * 4 * limit;
            break;
          case '1d':
            startTime += 3600000 * 24 * limit;
            break;
        }
      }
    } catch (e) {
      console.log(e);
    }
  }

  fetchCandle1m = async (symbol: string, exchange: string) => {
    let fetchedCandles = [];

    switch (exchange) {
      case Exchange.BINANCE:
        fetchedCandles = await this.binanceCandleSticksService.getCandleSticks({
          symbol,
          interval: '1m',
          limit: 10,
        });
        break;

      case Exchange.GATEIO:
        fetchedCandles = await this.gateioCandleSticksService.getCandleSticks({
          symbol,
          interval: '1m',
          limit: 10,
        });
        break;
    }

    const candle = fetchedCandles[0];

    if (candle) {
      await this.candlesticksService.updateCandle(symbol, candle, exchange);
    }
  };

  mapInterval(interval: string): CandleChartInterval_LT {
    switch (interval) {
      case '60':
      case '1h':
        return CandleChartInterval.ONE_HOUR;
      case '120':
      case '2h':
        return CandleChartInterval.TWO_HOURS;
      case '240':
      case '4h':
        return CandleChartInterval.FOUR_HOURS;
      case '15':
      case '15m':
        return CandleChartInterval.FIFTEEN_MINUTES;
      case '5':
      case '5m':
        return CandleChartInterval.FIVE_MINUTES;
      case '1':
      case '1m':
        return CandleChartInterval.ONE_MINUTE;
      case '1d':
        return CandleChartInterval.ONE_DAY;
      default:
        return null;
    }
  }
}
