import { Module } from '@nestjs/common';
import { DatabaseModule } from '@app/database';
import { CandlesService } from './candles.service';
import { JobsService } from './jobs.service';
import { FetchExchangeInfoService } from './fetch-exchange-info/fetch-exchange-info.service';
import { FetchCandlesService } from './fetch-candles/fetch-candles.service';
import { MacdCalculationService } from './macd-calculation/macd-calculation.service';
import { BinanceApiModule } from '@app/binance-api';
import { ScheduleModule } from '@nestjs/schedule';
import { BybitApiModule } from '@app/bybit-api';
import { GateioApiModule } from '@app/gateio-api';

@Module({
  imports: [
    DatabaseModule,
    BinanceApiModule,
    BybitApiModule,
    GateioApiModule,
    ScheduleModule.forRoot(),
  ],
  controllers: [],
  providers: [
    CandlesService,
    JobsService,
    FetchExchangeInfoService,
    FetchCandlesService,
    MacdCalculationService,
  ],
})
export class CandlesModule {}
