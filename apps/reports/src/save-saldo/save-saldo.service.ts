import { Injectable } from '@nestjs/common';
import { DbUserService } from '@app/database/db-user/db-user.service';
import { DbKeyService } from '@app/database/db-key/db-key.service';
import { BinanceAccountInfoService } from '@app/binance-api/binance-account-info/binance-account-info.service';
import { DbSaldoUsdService } from '@app/database/db-saldo-usd/db-saldo-usd.service';
import { DbUserDocument } from '@app/database/db-user/db-user.model';
import { Exchange } from '../../../../types/exchanges';
import { GateioAccountInfoService } from '@app/gateio-api/gateio-account-info/gateio-account-info.service';
import { DbLogService } from '@app/database/db-log/db-log.service';

@Injectable()
export class SaveSaldoService {
  constructor(
    private userService: DbUserService,
    private keyService: DbKeyService,
    private saldoUsdService: DbSaldoUsdService,
    private binanceAccountInfoService: BinanceAccountInfoService,
    private gateioAccountInfoService: GateioAccountInfoService,
    private log: DbLogService,
  ) {}

  async saveSaldoForAllUsers(exchange: string) {
    const users = await this.userService.dbGetUsers();

    for (const user of users) {
      await this.saveSaldoForUser(user, exchange);
    }
  }

  async saveSaldoForUser(user: DbUserDocument, exchange: string) {
    try {
      const apiKey = await this.keyService.getApiKey(
        user._id.toString(),
        exchange,
      );

      if (apiKey && apiKey.key && apiKey.secret) {
        let accountInfo;

        switch (exchange) {
          case Exchange.BINANCE:
            accountInfo = await this.binanceAccountInfoService.getAccountInfo(
              apiKey,
              {
                noSmallAmount: false,
              },
            );
            break;

          case Exchange.GATEIO:
            accountInfo = await this.gateioAccountInfoService.getAccountInfo(
              apiKey,
              {
                noSmallAmount: false,
              },
            );
            break;
        }

        if (accountInfo) {
          await this.saldoUsdService.saveUserSaldoUsd(
            user._id.toString(),
            accountInfo,
            user.exchange,
          );
        }
      }
    } catch (e) {
      this.log.errorReports('Error saving saldo', e);
    }
  }
}
