import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbAutoBotService } from '@app/database/db-auto-bot/db-auto-bot.service';
import { DbKeyService } from '@app/database/db-key/db-key.service';
import { DbMarketBotTradeService } from '@app/database/db-market-bot-trade/db-market-bot-trade.service';
import { DbSaldoService } from '@app/database/db-saldo/db-saldo.service';
import { BinanceAccountInfoService } from '@app/binance-api/binance-account-info/binance-account-info.service';
import { SendMailService } from '@app/send-mail';
import { DbFearAltIndexService } from '@app/database/db-fear-alt-index/db-fear-alt-index.service';
import cheerio from 'cheerio';
import { DbUserService } from '@app/database/db-user/db-user.service';
import { Exchange } from '../../../../types/exchanges';
import { GateioAccountInfoService } from '@app/gateio-api/gateio-account-info/gateio-account-info.service';

@Injectable()
export class SendReportsService {
  constructor(
    private readonly logger: DbLogService,
    private readonly autoBotService: DbAutoBotService,
    private readonly keyService: DbKeyService,
    private readonly marketBotTradeService: DbMarketBotTradeService,
    private readonly saldoService: DbSaldoService,
    private readonly binanceAccountInfoService: BinanceAccountInfoService,
    private readonly gateioAccountInfoService: GateioAccountInfoService,
    private readonly sendMailService: SendMailService,
    private readonly fearAltIndexService: DbFearAltIndexService,
    private userService: DbUserService,
  ) {}

  public async sendReport() {
    const autobots = await this.autoBotService.dbGetAllActiveAutobots();

    for (let i = 0; i < autobots.length; i++) {
      const autobot = autobots[i];
      const date = new Date();
      const exchange = await this.userService.getUserExchange(autobot.userId);
      const binanceKeys = await this.keyService.getApiKey(
        autobot.userId,
        exchange,
      );

      if (exchange == Exchange.BINANCE) {
        let accountInfo = null;
        switch (autobot.exchange) {
          case Exchange.BINANCE:
            accountInfo = await this.binanceAccountInfoService.getAccountInfo(
              binanceKeys,
              { noSmallAmount: false },
            );
            break;
          case Exchange.GATEIO:
            accountInfo = await this.gateioAccountInfoService.getAccountInfo(
              binanceKeys,
              { noSmallAmount: false },
            );
            break;
        }

        const trades =
          await this.marketBotTradeService.dbGetMarketBotTradesAfterDate(
            autobot._id.toString(),
            new Date(date.getFullYear(), date.getMonth(), date.getDate()),
          );
        // For yesterday balance
        const userSaldo = await this.saldoService.getUserSaldo(
          autobot.userId,
          exchange,
        );

        await this.sendMailService.sendDailyReportMail(
          autobot.userId,
          `Moonbot Daily Report ${new Date().toLocaleDateString('de-DE')}`,
          {
            accountInfo,
            userSaldo,
            autobot,
            trades,
          },
        );
      }
    }
  }

  public async saveFearAltIndex() {
    const fearIndex = await this.getFearIndex();
    const altIndex = await this.getAltIndex();

    await this.fearAltIndexService.dbSaveFearIndex(fearIndex, altIndex);
  }

  private async getFearIndex() {
    try {
      const result = await fetch('https://api.alternative.me/fng/?limit=0');
      const resultJson = await result.json();

      const fearIndex: {
        value: string;
        value_classification: string;
        timestamp: string;
        time_until_updaate: string;
      } = { ...resultJson.data[0] };

      // this.logger.logReports(
      //   'Fetched Fear index:',
      //   fearIndex.value,
      //   fearIndex.value_classification,
      // );
      return fearIndex;
    } catch (e) {
      this.logger.errorReports('Error fetching fear index', e);
    }
  }

  private async getAltIndex() {
    try {
      const html = await fetch(
        'https://www.blockchaincenter.net/en/altcoin-season-index/',
      );

      const html2 = await html.text();

      const cheerioAPI = cheerio.load(html2);
      const text = cheerioAPI(
        '#season > div > div > div.text-center.m-3',
      ).text();

      const index = cheerioAPI(
        '#season > div > div > div:nth-child(3) > div:nth-child(1)',
      ).text();
      // this.logger.logReports('Fetched alt index:', index, text);
      return {
        text,
        index,
      };
    } catch (e) {
      this.logger.errorReports('Error fetching alt index', e);
    }
  }
}
