import { Injectable } from '@nestjs/common';
import { SendReportsService } from './send-reports/send-reports.service';
import { SaveSaldoService } from './save-saldo/save-saldo.service';
import { <PERSON>ron } from '@nestjs/schedule';
import { Exchange } from '../../../types/exchanges';

/**
 *    *    *    *    *    *
 ┬    ┬    ┬    ┬    ┬    ┬
 │    │    │    │    │    │
 │    │    │    │    │    └ day of week (0 - 7) (0 or 7 is Sun)
 │    │    │    │    └───── month (1 - 12)
 │    │    │    └────────── day of month (1 - 31)
 │    │    └─────────────── hour (0 - 23)
 │    └──────────────────── minute (0 - 59)
 └───────────────────────── second (0 - 59, OPTIONAL)
 */

@Injectable()
export class JobsService {
  constructor(
    private saveSaldoService: SaveSaldoService,
    private sendReportsService: SendReportsService,
  ) {}

  /**
   * Save fear and alt index every hour
   */
  @Cron('10 */10 * * * *')
  saveFearAltIndex() {
    this.sendReportsService.saveFearAltIndex();
  }

  /**
   * Send user daily report per mail
   */
  @Cron('50 55 23 * * *')
  sendReport() {
    this.sendReportsService.sendReport();
  }

  /**
   * Save user saldo every 10 minutes
   */
  @Cron('10 */30 * * * *')
  async saveSaldoForAllUsers() {
    await this.saveSaldoService.saveSaldoForAllUsers(Exchange.BINANCE);
    await this.saveSaldoService.saveSaldoForAllUsers(Exchange.GATEIO);
  }

  public async start() {
    await this.sendReportsService.sendReport();
    await this.sendReportsService.saveFearAltIndex();
  }
}
