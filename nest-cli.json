{"$schema": "https://json.schemastore.org/nest-cli", "collection": "@nestjs/schematics", "sourceRoot": "apps/moonbot-nest/src", "compilerOptions": {"deleteOutDir": true, "webpack": true, "tsConfigPath": "apps/moonbot-nest/tsconfig.app.json"}, "monorepo": true, "root": "apps/moonbot-nest", "projects": {"candles": {"type": "application", "root": "apps/candles", "entryFile": "main", "sourceRoot": "apps/candles/src", "compilerOptions": {"tsConfigPath": "apps/candles/tsconfig.app.json"}}, "server": {"type": "application", "root": "apps/server", "entryFile": "main", "sourceRoot": "apps/server/src", "compilerOptions": {"tsConfigPath": "apps/server/tsconfig.app.json"}}, "orders": {"type": "application", "root": "apps/orders", "entryFile": "main", "sourceRoot": "apps/orders/src", "compilerOptions": {"tsConfigPath": "apps/orders/tsconfig.app.json"}}, "bot": {"type": "application", "root": "apps/bot", "entryFile": "main", "sourceRoot": "apps/bot/src", "compilerOptions": {"tsConfigPath": "apps/bot/tsconfig.app.json"}}, "tester": {"type": "application", "root": "apps/tester", "entryFile": "main", "sourceRoot": "apps/tester/src", "compilerOptions": {"tsConfigPath": "apps/tester/tsconfig.app.json"}}, "database": {"type": "library", "root": "libs/database", "entryFile": "index", "sourceRoot": "libs/database/src", "compilerOptions": {"tsConfigPath": "libs/database/tsconfig.lib.json"}}, "binance-api": {"type": "library", "root": "libs/binance-api", "entryFile": "index", "sourceRoot": "libs/binance-api/src", "compilerOptions": {"tsConfigPath": "libs/binance-api/tsconfig.lib.json"}}, "bybit-api": {"type": "library", "root": "libs/bybit-api", "entryFile": "index", "sourceRoot": "libs/bybit-api/src", "compilerOptions": {"tsConfigPath": "libs/bybit-api/tsconfig.lib.json"}}, "gateio-api": {"type": "library", "root": "libs/gateio-api", "entryFile": "index", "sourceRoot": "libs/gateio-api/src", "compilerOptions": {"tsConfigPath": "libs/gateio-api/tsconfig.lib.json"}}, "reports": {"type": "application", "root": "apps/reports", "entryFile": "main", "sourceRoot": "apps/reports/src", "compilerOptions": {"tsConfigPath": "apps/reports/tsconfig.app.json"}}, "send-mail": {"type": "library", "root": "libs/send-mail", "entryFile": "index", "sourceRoot": "libs/send-mail/src", "compilerOptions": {"tsConfigPath": "libs/send-mail/tsconfig.lib.json"}}, "auto-bot-processor": {"type": "library", "root": "libs/auto-bot-processor", "entryFile": "index", "sourceRoot": "libs/auto-bot-processor/src", "compilerOptions": {"tsConfigPath": "libs/auto-bot-processor/tsconfig.lib.json"}}, "pump": {"type": "application", "root": "apps/pump", "entryFile": "main", "sourceRoot": "apps/pump/src", "compilerOptions": {"tsConfigPath": "apps/pump/tsconfig.app.json"}}, "indicators": {"type": "application", "root": "apps/indicators", "entryFile": "main", "sourceRoot": "apps/indicators/src", "compilerOptions": {"tsConfigPath": "apps/indicators/tsconfig.app.json"}}}}