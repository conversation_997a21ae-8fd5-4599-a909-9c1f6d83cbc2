#!/bin/bash

PASSWORD=""
FILENAME=""
CONFIG=""

# Argument parsing
while getopts ":p:f:c:" opt; do
  case $opt in
    p)
      PASSWORD="$OPTARG"
      ;;
    f)
      FILENAME="$OPTARG"
      ;;
    c)
      CONFIG="$OPTARG"
      ;;
    \?)
      echo "Invalid option: -$OPTARG" >&2
      exit 1
      ;;
    :)
      echo "Option -$OPTARG requires an argument." >&2
      exit 1
      ;;
  esac
done

# Check if bin/hummingbot_quickstart.py exists
if [[ ! -f bin/hummingbot_quickstart.py ]]; then
    echo "Error: bin/hummingbot_quickstart.py command not found. Make sure you are in the Hummingbot root directory"
    exit 1
fi

# Check if the hummingbot conda environment is activated
if [[ $CONDA_DEFAULT_ENV != "hummingbot" ]]; then
    echo "Error: 'hummingbot' conda environment is not activated. Please activate it and try again."
    exit 1
fi

# Build the command to run
CMD="./bin/hummingbot_quickstart.py"
if [[ ! -z "$PASSWORD" ]]; then
    CMD="$CMD -p \"$PASSWORD\""
fi

# Check for valid file extensions
if [[ ! -z "$FILENAME" ]]; then
    if [[ $FILENAME == *.yml || $FILENAME == *.py ]]; then
        CMD="$CMD -f \"$FILENAME\""
    else
        echo "Error: Invalid strategy or script file. File must be a .yml or .py file."
        exit 4
    fi
fi

if [[ ! -z "$CONFIG" ]]; then
    if [[ $CONFIG == *.yml ]]; then
        CMD="$CMD -c \"$CONFIG\""
    else
        echo "Error: Config file must be a .yml file."
        exit 3
    fi
fi

# Execute the command
eval $CMD
