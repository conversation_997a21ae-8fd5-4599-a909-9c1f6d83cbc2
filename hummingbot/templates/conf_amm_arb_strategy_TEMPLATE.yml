##########################################
###   AMM Arbitrage strategy config   ###
##########################################

template_version: 6
strategy: null

# The following configurations are only required for the AMM arbitrage trading strategy

# Connectors and markets parameters
connector_1: null
market_1: null
connector_2: null
market_2: null

order_amount: null

# Minimum profitability target required to place an order
# Expressed in percentage value, e.g. 1 = 1% target profit
min_profitability: null

# A buffer for which to adjust order price for higher chance of the order getting filled.
# This is important for AMM which transaction takes a long time where a slippage is acceptable rather having
# the transaction get rejected. The submitted order price will be adjust higher (by percentage value) for buy order
# and lower for sell order. (Enter 1 for 1%)
market_1_slippage_buffer: null

# A buffer to add to the price to account for slippage when buying/selling on second connector market
# (Enter 1 for 1%)
market_2_slippage_buffer: null

# A flag (true/false), if true the bot submits both arbitrage taker orders (buy and sell) simultaneously
# If false, the bot will wait for first exchange order filled before submitting the other order
concurrent_orders_submission: null

# What rate source should be used for quote assets pair - between fixed_rate_source and rate_oracle_source?
# When true: Uses an oracle (like CoinGecko) to get real-time conversion rates between quote assets
# When false: Uses the fixed conversion rate specified in quote_conversion_rate
rate_oracle_enabled: true

# What is the fixed_rate used to convert quote assets?
# Only used when rate_oracle_enabled is false
# Example: If trading between USDT-XXX and USDC-XXX pairs, and quote_conversion_rate is 1,
# then 1 USDT = 1 USDC for profit calculations
# Set this value based on the expected conversion rate between your quote assets
quote_conversion_rate: 1

# What is the symbol of the token used to pay gas?
# Specifies which token is used for transaction fees on the blockchain
# Default is ETH for Ethereum-based networks, but could be different for other chains
# This is used to calculate profitability by factoring in gas costs
gas_token: ETH

# What is the gas price, expressed in the quote asset?
# Sets the conversion rate between the gas token and the quote asset
# For example, if gas_price is 3500 and quote asset is USDC, then 1 ETH = 3500 USDC
# This rate is used to convert gas fees to quote asset for profit calculations
gas_price: 2000