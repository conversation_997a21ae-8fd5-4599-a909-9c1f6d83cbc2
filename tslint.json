{"defaultSeverity": "error", "enable": true, "extends": ["tslint:recommended", "tslint-config-prettier"], "jsRules": {}, "rules": {"ordered-imports": false, "object-literal-sort-keys": false, "jsx-no-lambda": false, "no-var-requires": false, "arrow-parens": [true, "ban-single-arg-parens"], "max-line-length": false, "no-console": false, "no-floating-promises": true, "variable-name": [true, "check-format", "allow-leading-underscore", "allow-pascal-case"]}, "rulesDirectory": []}