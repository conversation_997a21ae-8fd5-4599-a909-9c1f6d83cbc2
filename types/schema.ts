
/*
 * -------------------------------------------------------
 * THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
 * -------------------------------------------------------
 */

/* tslint:disable */
/* eslint-disable */

export class InstrumentFilters {
    interval?: Nullable<string>;
    ao?: Nullable<string>;
    rsi?: Nullable<string>;
    nesterov?: Nullable<string>;
}

export class SplitViewSave {
    window: number;
    market?: Nullable<string>;
    interval?: Nullable<string>;
}

export abstract class IQuery {
    abstract session(): Nullable<Session> | Promise<Nullable<Session>>;

    abstract exchange(): Nullable<ExchangeQuery> | Promise<Nullable<ExchangeQuery>>;

    abstract indicators(): Nullable<IndicatorsQuery> | Promise<Nullable<IndicatorsQuery>>;

    abstract instruments(): Nullable<InstrumentsQuery> | Promise<Nullable<InstrumentsQuery>>;

    abstract strategy(): Nullable<StrategyQuery> | Promise<Nullable<StrategyQuery>>;

    abstract user(): Nullable<UserQuery> | Promise<Nullable<UserQuery>>;

    abstract bot(): Nullable<BotQuery> | Promise<Nullable<BotQuery>>;

    abstract pump(): Nullable<PumpQuery> | Promise<Nullable<PumpQuery>>;
}

export abstract class IMutation {
    abstract login(username: string, password: string): Nullable<LoginResponse> | Promise<Nullable<LoginResponse>>;

    abstract register(username: string, password: string, email: string): Nullable<boolean> | Promise<Nullable<boolean>>;

    abstract logout(): Nullable<boolean> | Promise<Nullable<boolean>>;

    abstract accountSettingsMutation(email?: Nullable<string>, pumpbot?: Nullable<boolean>, alwaysSetStoploss?: Nullable<boolean>, alwaysSetTP?: Nullable<boolean>): Nullable<UserAccount> | Promise<Nullable<UserAccount>>;

    abstract saveExchangeMutation(exchange: string): Nullable<Common> | Promise<Nullable<Common>>;

    abstract saveApiKeyMutation(key: string, secret: string, exchange: string): Nullable<ApiKey> | Promise<Nullable<ApiKey>>;

    abstract saveSplitMutation(configs?: Nullable<Nullable<SplitViewSave>[]>): Nullable<Common> | Promise<Nullable<Common>>;

    abstract saveCurrentSaldo(): Nullable<boolean> | Promise<Nullable<boolean>>;

    abstract cancelOrderMutation(symbol: string, orderId: string, type: string, recvWindow?: Nullable<number>): Nullable<Common> | Promise<Nullable<Common>>;

    abstract orderMutation(symbol: string, side: string, type: string, quantity: number, price?: Nullable<number>, timeInForce?: Nullable<string>, stopPrice?: Nullable<number>, icebergQty?: Nullable<number>, buyStopActive?: Nullable<boolean>, buyTPActive?: Nullable<boolean>, botTPPrice?: Nullable<number>, botStopPrice?: Nullable<number>, quantityUsd?: Nullable<number>, recvWindow?: Nullable<number>): Nullable<NewOrder> | Promise<Nullable<NewOrder>>;

    abstract commentMutation(symbol: string, comment?: Nullable<string>): Nullable<Comment> | Promise<Nullable<Comment>>;

    abstract addNewBotMutation(symbol?: Nullable<string>, interval?: Nullable<string>, strategy?: Nullable<string>, strategyParams?: Nullable<string>, amountUsdtInvested?: Nullable<number>, simulation?: Nullable<boolean>, startWithPosition?: Nullable<string>, longPrice?: Nullable<number>, limitSell?: Nullable<boolean>): Nullable<Common> | Promise<Nullable<Common>>;

    abstract startStopBotMutation(id: string, action: string): Nullable<Common> | Promise<Nullable<Common>>;

    abstract transformToAutoBotMutation(id: string): Nullable<Common> | Promise<Nullable<Common>>;

    abstract deleteBotMutation(id: string): Nullable<Common> | Promise<Nullable<Common>>;

    abstract saveWhiteListCoinMutation(symbol: string): Nullable<Common> | Promise<Nullable<Common>>;

    abstract deleteWhiteListCoinMutation(symbol: string): Nullable<Common> | Promise<Nullable<Common>>;

    abstract disableWhiteListCoinMutation(id: string): Nullable<Common> | Promise<Nullable<Common>>;

    abstract buyWhiteListCoinMutation(symbol: string): Nullable<Common> | Promise<Nullable<Common>>;

    abstract saveFavoriteCoinMutation(symbol: string): Nullable<FavoriteCoin> | Promise<Nullable<FavoriteCoin>>;

    abstract addActiveCallMutation(symbol: string): Nullable<Common> | Promise<Nullable<Common>>;

    abstract stopActiveCallMutation(symbol: string): Nullable<Common> | Promise<Nullable<Common>>;

    abstract saveNoticedCoinMutation(symbol: string, buyPrice: number): Nullable<Common> | Promise<Nullable<Common>>;

    abstract deleteNoticedCoinMutation(symbol: string): Nullable<Common> | Promise<Nullable<Common>>;

    abstract strategyTestAdd(strategy: string, interval: string, startDate: string, endDate: string, symbol: string, invest?: Nullable<number>, tp?: Nullable<number>, sl?: Nullable<number>): Nullable<Common> | Promise<Nullable<Common>>;

    abstract calcAllStrategy(strategy: string): Nullable<Common> | Promise<Nullable<Common>>;

    abstract backtestRecalcMutation(): Nullable<Common> | Promise<Nullable<Common>>;

    abstract createAutoBot(strategy: string, usdPerCoin: number): Nullable<Common> | Promise<Nullable<Common>>;

    abstract modifyNumBotsMutation(id: string, numbots?: Nullable<number>): Nullable<Common> | Promise<Nullable<Common>>;

    abstract autoUsdPerCoinMutation(botId: string, value?: Nullable<boolean>): Nullable<Common> | Promise<Nullable<Common>>;

    abstract stopAutoBotMutation(botId: string): Nullable<Common> | Promise<Nullable<Common>>;

    abstract startAutoBotMutation(botId: string): Nullable<Common> | Promise<Nullable<Common>>;

    abstract removeAutoBotMutation(botId: string): Nullable<Common> | Promise<Nullable<Common>>;

    abstract sellAllMutation(botId: string): Nullable<Common> | Promise<Nullable<Common>>;

    abstract buyNowMutation(botId: string): Nullable<Common> | Promise<Nullable<Common>>;

    abstract shortMarketBotMutation(symbol?: Nullable<string>): Nullable<Common> | Promise<Nullable<Common>>;

    abstract sellInMinusMutation(botId: string, value: boolean): Nullable<Common> | Promise<Nullable<Common>>;

    abstract onlyFromWhitelistMutation(botId: string, value: boolean): Nullable<Common> | Promise<Nullable<Common>>;

    abstract btcOnlyMutation(botId: string, value: boolean): Nullable<Common> | Promise<Nullable<Common>>;

    abstract enableBuyOnlyAOPlusMutation(botId: string, value: boolean): Nullable<Common> | Promise<Nullable<Common>>;

    abstract toggleBtcAoCrossMutation(botId: string, value: boolean): Nullable<Common> | Promise<Nullable<Common>>;

    abstract selectBotModeMutation(botId: string, interval: string, value: string): Nullable<Common> | Promise<Nullable<Common>>;

    abstract selectBotRefillWhitelist(botId: string, value: boolean): Nullable<Common> | Promise<Nullable<Common>>;

    abstract fillPortfolioAfterSellMutation(botId: string, value: boolean): Nullable<Common> | Promise<Nullable<Common>>;

    abstract toggleTakeProfitMutation(botId: string, value: boolean): Nullable<Common> | Promise<Nullable<Common>>;

    abstract toggleStopLossMutation(botId: string, value: boolean): Nullable<Common> | Promise<Nullable<Common>>;

    abstract setAutobotTakeProfitMutation(botId: string, value: number): Nullable<Common> | Promise<Nullable<Common>>;

    abstract setAutobotStopLossMutation(botId: string, value: number): Nullable<Common> | Promise<Nullable<Common>>;
}

export abstract class ISubscription {
    abstract marketHistory(symbol?: Nullable<string>): Nullable<Nullable<Trade>[]> | Promise<Nullable<Nullable<Trade>[]>>;

    abstract depth(symbol: string): Nullable<OrderBook> | Promise<Nullable<OrderBook>>;

    abstract allTickers(symbol?: Nullable<string>, base?: Nullable<string>): Nullable<Nullable<WSTicker>[]> | Promise<Nullable<Nullable<WSTicker>[]>>;

    abstract coinPrices(): Nullable<Nullable<WSCoinPrices>[]> | Promise<Nullable<Nullable<WSCoinPrices>[]>>;

    abstract coinPrice(symbols?: Nullable<Nullable<string>[]>): Nullable<Nullable<WSCoinPrices>[]> | Promise<Nullable<Nullable<WSCoinPrices>[]>>;
}

export class Session {
    id?: Nullable<string>;
    username?: Nullable<string>;
    email?: Nullable<string>;
}

export class ExchangeQuery {
    accountInfo?: Nullable<AccountInfo>;
    mytrades?: Nullable<Nullable<MyTrades>[]>;
    openOrders?: Nullable<Nullable<OpenOrder>[]>;
    dailyStats?: Nullable<DailyStats>;
    lastCandle?: Nullable<Candle>;
    candles?: Nullable<Nullable<Candle>[]>;
    exchangeInfo?: Nullable<Nullable<ExchangeSymbol>[]>;
    prices?: Nullable<Nullable<Price>[]>;
    comment?: Nullable<Comment>;
    orderbook?: Nullable<OrderBook>;
    lastOrders?: Nullable<Nullable<NewOrder>[]>;
    currentPrice?: Nullable<Price>;
    symbolTicker?: Nullable<WSTicker>;
    topVolumeCoins?: Nullable<Nullable<WSTicker>[]>;
}

export class IndicatorsQuery {
    macd?: Nullable<MACD>;
    macds?: Nullable<Nullable<MACD>[]>;
}

export class InstrumentsQuery {
    fetch?: Nullable<Nullable<Instrument>[]>;
}

export class UserQuery {
    saldo?: Nullable<SaldoResponse>;
    saldoUsd?: Nullable<SaldoResponse>;
    favorites?: Nullable<Nullable<FavoriteCoin>[]>;
    noticedCoins?: Nullable<Nullable<NoticedCoin>[]>;
    account?: Nullable<UserAccount>;
    exchangeKeys?: Nullable<ApiKey>;
    splitview?: Nullable<Nullable<SplitView>[]>;
    logs?: Nullable<Nullable<Log>[]>;
}

export class BotQuery {
    formInfo?: Nullable<AddNewForm>;
    marketBots?: Nullable<Nullable<MarketBot>[]>;
    signals?: Nullable<Nullable<BotSignal>[]>;
    signalMarkets?: Nullable<Nullable<BotSignal>[]>;
    profit?: Nullable<BotProfit>;
    strategyProfits?: Nullable<Nullable<BotProfitPerDay>[]>;
    profitPerDayForStrategy?: Nullable<BotProfitPerDay>;
    profitPerMonth?: Nullable<BotProfitPerMonthResult>;
    botOrders?: Nullable<Nullable<OpenOrder>[]>;
    topCoins?: Nullable<Nullable<TopCoin>[]>;
    suggestList?: Nullable<Nullable<WhiteListSuggest>[]>;
    whitelist?: Nullable<Nullable<WhiteListCoin>[]>;
    getIndicators?: Nullable<Nullable<Indicator>[]>;
    getAllIndicators?: Nullable<Nullable<Indicator>[]>;
    autobot?: Nullable<AutoBot>;
    profitGraph?: Nullable<Nullable<AutobotProfitHistory>[]>;
    lastBotOrders?: Nullable<Nullable<MarketBotTrade>[]>;
    fearAltIndex?: Nullable<FearAltIndex>;
}

export class StrategyQuery {
    getStrategyTests?: Nullable<Nullable<StrategyTest>[]>;
    getAllStrategyTests?: Nullable<Nullable<GlobalStrategyTest>[]>;
}

export class Instrument {
    symbol?: Nullable<string>;
    isWhitelisted?: Nullable<boolean>;
    isPump?: Nullable<boolean>;
    isRecommended?: Nullable<boolean>;
    isBinanceRecommended?: Nullable<boolean>;
    isNewListed?: Nullable<boolean>;
    percent1h?: Nullable<number>;
    percent4h?: Nullable<number>;
    percent24h?: Nullable<number>;
    percentVolume1h?: Nullable<number>;
    percentVolume2h?: Nullable<number>;
    percentVolume4h?: Nullable<number>;
    percentVolume24h?: Nullable<number>;
    rsi15?: Nullable<number>;
    rsi60?: Nullable<number>;
    rsi120?: Nullable<number>;
    rsi240?: Nullable<number>;
    rsi1d?: Nullable<number>;
    listedOn?: Nullable<Date>;
    updateTimestamp?: Nullable<Date>;
    interval?: Nullable<string>;
    ao?: Nullable<number>;
    adx?: Nullable<number>;
    rsi?: Nullable<number>;
    btcAO1d?: Nullable<number>;
    atr?: Nullable<number>;
    ema25?: Nullable<number>;
    ema50?: Nullable<number>;
    ema100?: Nullable<number>;
    mfi?: Nullable<number>;
    vwap?: Nullable<number>;
    ichimoku?: Nullable<Any>;
    roc?: Nullable<number>;
    bb_upper?: Nullable<number>;
    bb_middle?: Nullable<number>;
    bb_lower?: Nullable<number>;
    bb_obv?: Nullable<number>;
    ad?: Nullable<number>;
    so_k?: Nullable<number>;
    so_d?: Nullable<number>;
    sma7?: Nullable<number>;
    sma21?: Nullable<number>;
    sma100?: Nullable<number>;
    macd?: Nullable<number>;
    price?: Nullable<number>;
}

export class Indicator {
    symbol?: Nullable<string>;
    interval?: Nullable<string>;
    indicator?: Nullable<string>;
    action?: Nullable<string>;
    price?: Nullable<number>;
    timestamp?: Nullable<Date>;
}

export class PumpQuery {
    lastPumps?: Nullable<Nullable<Pump>[]>;
}

export class Pump {
    symbol?: Nullable<string>;
    notified?: Nullable<boolean>;
    price?: Nullable<number>;
    diff?: Nullable<number>;
    timestamp?: Nullable<string>;
}

export class LoginResponse {
    access_token?: Nullable<string>;
    username?: Nullable<string>;
    userId?: Nullable<string>;
}

export class AccountInfo {
    makerCommission?: Nullable<number>;
    takerCommission?: Nullable<number>;
    buyerCommission?: Nullable<number>;
    sellerCommission?: Nullable<number>;
    canTrade?: Nullable<boolean>;
    canWithdraw?: Nullable<boolean>;
    canDeposit?: Nullable<boolean>;
    balances?: Nullable<Nullable<Balance>[]>;
    btcValue?: Nullable<number>;
    usdValue?: Nullable<number>;
    accountType?: Nullable<string>;
    permissions?: Nullable<Nullable<string>[]>;
    updateTime?: Nullable<number>;
}

export class Balance {
    idx?: Nullable<string>;
    asset?: Nullable<string>;
    free?: Nullable<number>;
    locked?: Nullable<number>;
    usdValue?: Nullable<number>;
    btcValue?: Nullable<number>;
}

export class MyTrades {
    id?: Nullable<number>;
    orderId?: Nullable<string>;
    price?: Nullable<number>;
    qty?: Nullable<number>;
    commission?: Nullable<number>;
    commissionUsd?: Nullable<number>;
    commissionAsset?: Nullable<string>;
    time?: Nullable<number>;
    isBuyer?: Nullable<boolean>;
    isMaker?: Nullable<boolean>;
    isBestMatch?: Nullable<boolean>;
    trades?: Nullable<Nullable<MyTrade>[]>;
}

export class MyTrade {
    id?: Nullable<number>;
    orderId?: Nullable<string>;
    price?: Nullable<number>;
    qty?: Nullable<number>;
    commission?: Nullable<number>;
    commissionUsd?: Nullable<number>;
    commissionAsset?: Nullable<string>;
    time?: Nullable<number>;
    isBuyer?: Nullable<boolean>;
    isMaker?: Nullable<boolean>;
    isBestMatch?: Nullable<boolean>;
}

export class OpenOrder {
    _id?: Nullable<string>;
    symbol?: Nullable<string>;
    orderId?: Nullable<string>;
    clientOrderId?: Nullable<string>;
    price?: Nullable<number>;
    origQty?: Nullable<number>;
    executedQty?: Nullable<number>;
    status?: Nullable<string>;
    timeInForce?: Nullable<string>;
    type?: Nullable<string>;
    side?: Nullable<string>;
    stopPrice?: Nullable<number>;
    icebergQty?: Nullable<number>;
    time?: Nullable<number>;
    isWorking?: Nullable<boolean>;
}

export class DailyStats {
    priceChange?: Nullable<number>;
    priceChangePercent?: Nullable<number>;
    weightedAvgPrice?: Nullable<number>;
    prevClosePrice?: Nullable<number>;
    lastPrice?: Nullable<number>;
    lastQty?: Nullable<number>;
    bidPrice?: Nullable<number>;
    bidQty?: Nullable<number>;
    askPrice?: Nullable<number>;
    askQty?: Nullable<number>;
    openPrice?: Nullable<number>;
    highPrice?: Nullable<number>;
    lowPrice?: Nullable<number>;
    volume?: Nullable<number>;
    quoteVolume?: Nullable<number>;
    openTime?: Nullable<number>;
    closeTime?: Nullable<number>;
    firstId?: Nullable<number>;
    lastId?: Nullable<number>;
    count?: Nullable<number>;
}

export class ExchangeSymbol {
    symbol?: Nullable<string>;
    baseAsset?: Nullable<string>;
    baseAssetPrecision?: Nullable<number>;
    quoteAsset?: Nullable<string>;
    quotePrecision?: Nullable<number>;
    orderTypes?: Nullable<Nullable<string>[]>;
    icebergAllowed?: Nullable<boolean>;
    filters?: Nullable<Nullable<Filter>[]>;
}

export class Filter {
    filterType?: Nullable<string>;
    minPrice?: Nullable<number>;
    maxPrice?: Nullable<number>;
    tickSize?: Nullable<number>;
    minQty?: Nullable<number>;
    maxQty?: Nullable<number>;
    stepSize?: Nullable<number>;
    minNotional?: Nullable<number>;
}

export class SaldoResponse {
    saldos?: Nullable<Nullable<Saldo>[]>;
    yesterday?: Nullable<Saldo>;
}

export class Saldo {
    timestamp?: Nullable<string>;
    usd?: Nullable<number>;
    btc?: Nullable<number>;
    usdDiff?: Nullable<number>;
}

export class NewOrder {
    symbol?: Nullable<string>;
    orderId?: Nullable<string>;
    clientOrderId?: Nullable<string>;
    transactTime?: Nullable<number>;
    price?: Nullable<number>;
    origQty?: Nullable<number>;
    executedQty?: Nullable<number>;
    status?: Nullable<string>;
    timeInForce?: Nullable<string>;
    type?: Nullable<string>;
    side?: Nullable<string>;
    ok?: Nullable<string>;
    error?: Nullable<string>;
}

export class FavoriteCoin {
    symbol?: Nullable<string>;
}

export class WhiteListCoin {
    id?: Nullable<string>;
    symbol?: Nullable<string>;
    timestamp?: Nullable<string>;
    autoAdded?: Nullable<boolean>;
    indicator?: Nullable<Indicator>;
    disabled?: Nullable<boolean>;
}

export class NoticedCoin {
    symbol?: Nullable<string>;
    buyPrice?: Nullable<number>;
    currentPrice?: Nullable<number>;
    timestamp?: Nullable<string>;
    isActiveCall?: Nullable<boolean>;
}

export class Trade {
    rowId?: Nullable<string>;
    id?: Nullable<number>;
    price?: Nullable<number>;
    qty?: Nullable<number>;
    time?: Nullable<number>;
    isBuyerMaker?: Nullable<boolean>;
    isBestMatch?: Nullable<boolean>;
    symbol?: Nullable<string>;
    maker?: Nullable<boolean>;
    tradeId?: Nullable<number>;
    eventType?: Nullable<string>;
    tradeTime?: Nullable<number>;
}

export class OrderBook {
    lastUpdateId?: Nullable<number>;
    asks?: Nullable<Nullable<DepthDetail>[]>;
    bids?: Nullable<Nullable<DepthDetail>[]>;
    error?: Nullable<number>;
}

export class DepthDetail {
    rowId?: Nullable<string>;
    price?: Nullable<number>;
    quantity?: Nullable<number>;
}

export class WSTicker {
    eventType?: Nullable<string>;
    eventTime?: Nullable<number>;
    symbol?: Nullable<string>;
    priceChange?: Nullable<number>;
    priceChangePercent?: Nullable<number>;
    weightedAvg?: Nullable<number>;
    prevDayClose?: Nullable<number>;
    curDayClose?: Nullable<number>;
    bestBid?: Nullable<number>;
    bestBidQnt?: Nullable<number>;
    bestAsk?: Nullable<number>;
    bestAskQnt?: Nullable<number>;
    open?: Nullable<number>;
    high?: Nullable<number>;
    low?: Nullable<number>;
    volume?: Nullable<number>;
    volumeQuote?: Nullable<number>;
    openTime?: Nullable<number>;
    closeTime?: Nullable<number>;
    firstTradeId?: Nullable<number>;
    lastTradeId?: Nullable<number>;
    totalTrades?: Nullable<number>;
    percent4h?: Nullable<number>;
    percentVolume1h?: Nullable<number>;
    percentVolume2h?: Nullable<number>;
    percentVolume4h?: Nullable<number>;
    percentVolume24h?: Nullable<number>;
    advice?: Nullable<string>;
    volumeQuoteBTC?: Nullable<number>;
}

export class WSCoinPrices {
    symbol?: Nullable<string>;
    price?: Nullable<number>;
}

export class UserAccount {
    username?: Nullable<string>;
    email?: Nullable<string>;
    notifications?: Nullable<UserNotifications>;
    splitview?: Nullable<Nullable<SplitView>[]>;
    trade?: Nullable<UserTrade>;
    exchange?: Nullable<string>;
}

export class UserNotifications {
    pumpbot?: Nullable<boolean>;
    signals?: Nullable<Any>;
    marketBots?: Nullable<boolean>;
}

export class UserTrade {
    alwaysSetStoploss?: Nullable<boolean>;
    alwaysSetTP?: Nullable<boolean>;
}

export class ApiKey {
    key?: Nullable<string>;
    secret?: Nullable<string>;
    exchange?: Nullable<string>;
    error?: Nullable<string>;
    activated?: Nullable<boolean>;
}

export class Common {
    ok?: Nullable<boolean>;
    message?: Nullable<string>;
    error?: Nullable<string>;
}

export class Comment {
    symbol?: Nullable<string>;
    userId?: Nullable<string>;
    comment?: Nullable<string>;
    ok?: Nullable<string>;
    error?: Nullable<string>;
}

export class NewBot {
    symbol?: Nullable<string>;
    interval?: Nullable<string>;
    strategy?: Nullable<string>;
    strategyParams?: Nullable<string>;
    amountUsdtInvested?: Nullable<number>;
    simulation?: Nullable<boolean>;
    startWithPosition?: Nullable<string>;
    longPrice?: Nullable<number>;
    limitSell?: Nullable<boolean>;
}

export class Price {
    symbol?: Nullable<string>;
    price?: Nullable<number>;
}

export class BotProfit {
    profit?: Nullable<number>;
    tradesPos?: Nullable<number>;
    tradesNeg?: Nullable<number>;
}

export class BotProfitPerDay {
    strategy?: Nullable<string>;
    interval?: Nullable<string>;
    totalProfit?: Nullable<number>;
    currentDiff?: Nullable<number>;
    tradesPos?: Nullable<number>;
    tradesNeg?: Nullable<number>;
    dailyProfits?: Nullable<Nullable<BotProfits>[]>;
}

export class BotProfitPerMonthResult {
    strategy?: Nullable<string>;
    interval?: Nullable<string>;
    totalProfit?: Nullable<number>;
    tradesPos?: Nullable<number>;
    tradesNeg?: Nullable<number>;
    monthlyProfits?: Nullable<Nullable<BotProfitsForMonth>[]>;
}

export class BotProfitsForMonth {
    month?: Nullable<string>;
    monthStr?: Nullable<string>;
    profit?: Nullable<number>;
}

export class BotProfits {
    day?: Nullable<string>;
    dateStr?: Nullable<string>;
    profit?: Nullable<number>;
}

export class BotSignal {
    id?: Nullable<string>;
    symbol?: Nullable<string>;
    price?: Nullable<number>;
    advice?: Nullable<string>;
    strategy?: Nullable<string>;
    interval?: Nullable<string>;
    profit?: Nullable<number>;
    timestamp?: Nullable<Date>;
    currentDiff?: Nullable<number>;
    candleTime?: Nullable<Date>;
}

export class MarketBot {
    id?: Nullable<string>;
    symbol?: Nullable<string>;
    exchange?: Nullable<string>;
    strategy?: Nullable<string>;
    amountUsdtInvested?: Nullable<number>;
    startBalanceUSD?: Nullable<number>;
    finalBalanceUSD?: Nullable<number>;
    startBalanceBTC?: Nullable<number>;
    finalBalanceBTC?: Nullable<number>;
    startPrice?: Nullable<number>;
    endPrice?: Nullable<number>;
    marketPercent?: Nullable<number>;
    startDate?: Nullable<Date>;
    stopDate?: Nullable<Date>;
    updatedOn?: Nullable<Date>;
    strategyParams?: Nullable<string>;
    interval?: Nullable<string>;
    profit?: Nullable<number>;
    trades?: Nullable<Nullable<BotTrade>[]>;
    lastTrade?: Nullable<string>;
    active?: Nullable<boolean>;
    position?: Nullable<string>;
    sellInMinus?: Nullable<boolean>;
    simulation?: Nullable<boolean>;
    roundTrips?: Nullable<Nullable<BotRoundTrip>[]>;
    timestamp?: Nullable<string>;
    fromAutoBot?: Nullable<boolean>;
    currentProfit?: Nullable<number>;
    currentValueUsd?: Nullable<number>;
    investedUsd?: Nullable<number>;
}

export class BotTrade {
    tradeDate?: Nullable<string>;
    position?: Nullable<string>;
    price?: Nullable<number>;
    profit?: Nullable<number>;
    profitWithoutFees?: Nullable<number>;
    tradeResult?: Nullable<string>;
}

export class BotRoundTrip {
    entryDate?: Nullable<Date>;
    exitDate?: Nullable<Date>;
    entryPrice?: Nullable<number>;
    exitPrice?: Nullable<number>;
    duration?: Nullable<number>;
    profit?: Nullable<number>;
    profitWithoutFees?: Nullable<number>;
}

export class AddNewForm {
    strategies?: Nullable<Nullable<BotStrategy>[]>;
}

export class BotStrategy {
    name?: Nullable<string>;
    description?: Nullable<string>;
    interval?: Nullable<string>;
    defaultParams?: Nullable<string>;
}

export class MACD {
    signal?: Nullable<number>;
    openTime?: Nullable<number>;
    MACD?: Nullable<number>;
    histogram?: Nullable<number>;
    coinPriceUsd?: Nullable<number>;
}

export class BotOrder {
    _id?: Nullable<string>;
    symbol?: Nullable<string>;
    type?: Nullable<string>;
    amount?: Nullable<string>;
    price?: Nullable<number>;
    markForTP?: Nullable<boolean>;
    tpPrice?: Nullable<number>;
    addTimestamp?: Nullable<string>;
    executionTimestamp?: Nullable<string>;
    status?: Nullable<string>;
}

export class TopCoin {
    symbol?: Nullable<string>;
    profit?: Nullable<number>;
    strategy?: Nullable<string>;
}

export class StrategyTest {
    symbol?: Nullable<string>;
    interval?: Nullable<string>;
    startDate?: Nullable<string>;
    endDate?: Nullable<string>;
    strategy?: Nullable<string>;
    endUsd?: Nullable<number>;
    invest?: Nullable<number>;
    status?: Nullable<string>;
    profit?: Nullable<number>;
    roundTrips?: Nullable<Nullable<BotRoundTrip>[]>;
    botTrades?: Nullable<Nullable<BotTrade>[]>;
    createTimestamp?: Nullable<string>;
}

export class GlobalStrategyTest {
    strategy?: Nullable<string>;
    interval?: Nullable<string>;
    profit?: Nullable<number>;
    profitUsd?: Nullable<number>;
    numShorts?: Nullable<number>;
    startDate?: Nullable<string>;
    endDate?: Nullable<string>;
    status?: Nullable<string>;
    endUsd?: Nullable<number>;
    invest?: Nullable<number>;
    currentSymbol?: Nullable<string>;
    progressStrategy?: Nullable<number>;
    progressInterval?: Nullable<number>;
    progressSymbol?: Nullable<number>;
    investPerCoin?: Nullable<number>;
    bestSymbolProfit?: Nullable<number>;
    worstSymbolProfit?: Nullable<number>;
    numTrades?: Nullable<number>;
    posTrades?: Nullable<number>;
    negTrades?: Nullable<number>;
    bestPerformer?: Nullable<string>;
    worstPerformer?: Nullable<string>;
    avgProfit?: Nullable<number>;
    avgHoldMinutes?: Nullable<number>;
    avgTradesDay?: Nullable<number>;
    bestTrade?: Nullable<number>;
    avgTrade?: Nullable<number>;
    worstTrade?: Nullable<number>;
    createTimestamp?: Nullable<string>;
    updateTimestamp?: Nullable<string>;
    sl?: Nullable<number>;
}

export class AutoBot {
    id?: Nullable<string>;
    _id?: Nullable<string>;
    strategy?: Nullable<string>;
    interval?: Nullable<string>;
    usdPerCoin?: Nullable<string>;
    numbots?: Nullable<number>;
    autoUsdPerCoin?: Nullable<boolean>;
    marketBotsBig?: Nullable<Nullable<MarketBot>[]>;
    profitToday?: Nullable<number>;
    profitTodayRelative?: Nullable<number>;
    profitYesterday?: Nullable<number>;
    profitThisWeek?: Nullable<number>;
    profitThisMonth?: Nullable<number>;
    profitLastMonth?: Nullable<number>;
    profitOverall?: Nullable<number>;
    active?: Nullable<boolean>;
    archived?: Nullable<boolean>;
    createTimestamp?: Nullable<Date>;
    lastTradeTimestamp?: Nullable<Date>;
    startTime?: Nullable<Date>;
    profitTodayUsd?: Nullable<number>;
    profitYesterdayUsd?: Nullable<number>;
    profitThisWeekUsd?: Nullable<number>;
    profitThisMonthUsd?: Nullable<number>;
    profitLastMonthUsd?: Nullable<number>;
    profitOverallUsd?: Nullable<number>;
    accumulatedInvestUsd?: Nullable<number>;
    accumulatedValueUsd?: Nullable<number>;
    accumulatedProfit?: Nullable<number>;
    accumulatedProfitRelative?: Nullable<number>;
    activeCount?: Nullable<number>;
    tradesToday?: Nullable<number>;
    tradesTodayShort?: Nullable<number>;
    tradesTodayLong?: Nullable<number>;
    successRateToday?: Nullable<number>;
    winsToday?: Nullable<number>;
    losesToday?: Nullable<number>;
    autoRefillWhitelist?: Nullable<boolean>;
    stoplossPercent?: Nullable<number>;
    takeProfitPercent?: Nullable<number>;
    enableTakeProfit?: Nullable<boolean>;
    enableStoploss?: Nullable<boolean>;
    enableSellInMinus?: Nullable<boolean>;
    buyOnlyFromWhitelist?: Nullable<boolean>;
    fillPortfolioAfterSell?: Nullable<boolean>;
    buyOnlyOnAOPlus?: Nullable<boolean>;
    useBtcAOCross?: Nullable<boolean>;
    btcOnly?: Nullable<boolean>;
}

export class Candle {
    symbol?: Nullable<string>;
    interval?: Nullable<string>;
    timestamp?: Nullable<Date>;
    openTime?: Nullable<number>;
    open?: Nullable<number>;
    high?: Nullable<number>;
    low?: Nullable<number>;
    close?: Nullable<number>;
    volume?: Nullable<number>;
    closeTime?: Nullable<number>;
    quoteVolume?: Nullable<number>;
    trades?: Nullable<number>;
    baseAssetVolume?: Nullable<number>;
    quoteAssetVolume?: Nullable<number>;
}

export class SplitView {
    window: number;
    market?: Nullable<string>;
    interval?: Nullable<string>;
}

export class AutobotProfitHistory {
    percentHistory?: Nullable<number>;
    usdHistory?: Nullable<number>;
    timestamp?: Nullable<Date>;
}

export class MarketBotTrade {
    id?: Nullable<string>;
    autobotId?: Nullable<string>;
    marketBotId?: Nullable<string>;
    asset?: Nullable<string>;
    position?: Nullable<string>;
    price?: Nullable<number>;
    valueInUsd?: Nullable<number>;
    profit?: Nullable<number>;
    amount?: Nullable<number>;
    tradeDate?: Nullable<Date>;
    btcPrice?: Nullable<number>;
    profitUsd?: Nullable<number>;
}

export class WhiteListSuggest {
    symbol?: Nullable<string>;
    totalCount?: Nullable<number>;
    neuralCount?: Nullable<number>;
    longCount?: Nullable<number>;
    shortCount?: Nullable<number>;
}

export class Log {
    msg?: Nullable<string>;
    service?: Nullable<string>;
    level?: Nullable<string>;
    timestamp?: Nullable<Date>;
}

export class FearAltIndex {
    fearIndex?: Nullable<number>;
    altIndex?: Nullable<number>;
    fearText?: Nullable<string>;
    altText?: Nullable<string>;
}

export type Any = any;
type Nullable<T> = T | null;
