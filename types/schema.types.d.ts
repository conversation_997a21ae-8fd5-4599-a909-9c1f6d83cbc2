// tslint:disable
// graphql typescript definitions

declare namespace GQL {
interface IGraphQLResponseRoot {
data?: IQuery | IMutation;
errors?: Array<IGraphQLResponseError>;
}

interface IGraphQLResponseError {
/** Required for all errors */
message: string;
locations?: Array<IGraphQLResponseErrorLocation>;
/** 7.2.2 says 'GraphQL servers may provide additional entries to error' */
[propName: string]: any;
}

interface IGraphQLResponseErrorLocation {
line: number;
column: number;
}

interface IQuery {
__typename: "Query";
session: ISession | null;
binance: IBinanceQuery | null;
indicators: IIndicatorsQuery | null;
instruments: IInstrumentsQuery | null;
strategy: IStrategyQuery | null;
user: IUserQuery | null;
bot: IBotQuery | null;
pump: IPumpQuery | null;
}

interface ISession {
__typename: "Session";
id: string | null;
username: string | null;
email: string | null;
}

interface IBinanceQuery {
__typename: "BinanceQuery";
accountInfo: IAccountInfo | null;
mytrades: Array<IMyTrades | null> | null;
openOrders: Array<IOpenOrder | null> | null;
dailyStats: IDailyStats | null;
lastCandle: ICandle | null;
candles: Array<ICandle | null> | null;
exchangeInfo: Array<IExchangeSymbol | null> | null;
prices: Array<IPrice | null> | null;
comment: IComment | null;
orderbook: IOrderBook | null;
lastOrders: Array<INewOrder | null> | null;
currentPrice: IPrice | null;
symbolTicker: IWSTicker | null;
topVolumeCoins: Array<IWSTicker | null> | null;
}

interface IAccountInfoOnBinanceQueryArguments {
noSmallAmount?: boolean | null;
}

interface IMytradesOnBinanceQueryArguments {
symbol: string;
limit?: number | null;
fromId?: number | null;
recvWindow?: number | null;
}

interface IOpenOrdersOnBinanceQueryArguments {
symbol: string;
withBinance?: boolean | null;
}

interface IDailyStatsOnBinanceQueryArguments {
symbol: string;
}

interface ILastCandleOnBinanceQueryArguments {
symbol: string;
interval: string;
}

interface ICandlesOnBinanceQueryArguments {
symbol: string;
strategy: string;
limit: number;
interval?: string | null;
}

interface IExchangeInfoOnBinanceQueryArguments {
base?: string | null;
symbol?: string | null;
}

interface ICommentOnBinanceQueryArguments {
symbol: string;
}

interface IOrderbookOnBinanceQueryArguments {
symbol: string;
limit?: number | null;
}

interface ILastOrdersOnBinanceQueryArguments {
symbol?: string | null;
limit?: number | null;
status?: string | null;
}

interface ICurrentPriceOnBinanceQueryArguments {
symbol: string;
}

interface ISymbolTickerOnBinanceQueryArguments {
symbol: string;
}

interface ITopVolumeCoinsOnBinanceQueryArguments {
number: number;
}

interface IAccountInfo {
__typename: "AccountInfo";
makerCommission: number | null;
takerCommission: number | null;
buyerCommission: number | null;
sellerCommission: number | null;
canTrade: boolean | null;
canWithdraw: boolean | null;
canDeposit: boolean | null;
balances: Array<IBalance | null> | null;
btcValue: number | null;
usdValue: number | null;
accountType: string | null;
permissions: Array<string | null> | null;
updateTime: number | null;
}

interface IBalancesOnAccountInfoArguments {
symbols?: Array<string | null> | null;
}

interface IBalance {
__typename: "Balance";
idx: string | null;
asset: string | null;
free: number | null;
locked: number | null;
usdValue: number | null;
btcValue: number | null;
}

interface IMyTrades {
__typename: "MyTrades";
id: number | null;
orderId: string | null;
price: number | null;
qty: number | null;
commission: number | null;
commissionUsd: number | null;
commissionAsset: string | null;
time: number | null;
isBuyer: boolean | null;
isMaker: boolean | null;
isBestMatch: boolean | null;
trades: Array<IMyTrade | null> | null;
}

interface IMyTrade {
__typename: "MyTrade";
id: number | null;
orderId: string | null;
price: number | null;
qty: number | null;
commission: number | null;
commissionUsd: number | null;
commissionAsset: string | null;
time: number | null;
isBuyer: boolean | null;
isMaker: boolean | null;
isBestMatch: boolean | null;
}

interface IOpenOrder {
__typename: "OpenOrder";
_id: string | null;
symbol: string | null;
orderId: string | null;
clientOrderId: string | null;
price: number | null;
origQty: number | null;
executedQty: number | null;
status: string | null;
timeInForce: string | null;
type: string | null;
side: string | null;
stopPrice: number | null;
icebergQty: number | null;
time: number | null;
isWorking: boolean | null;
}

interface IDailyStats {
__typename: "DailyStats";
priceChange: number | null;
priceChangePercent: number | null;
weightedAvgPrice: number | null;
prevClosePrice: number | null;
lastPrice: number | null;
lastQty: number | null;
bidPrice: number | null;
bidQty: number | null;
askPrice: number | null;
askQty: number | null;
openPrice: number | null;
highPrice: number | null;
lowPrice: number | null;
volume: number | null;
quoteVolume: number | null;
openTime: number | null;
closeTime: number | null;
firstId: number | null;
lastId: number | null;
count: number | null;
}

interface ICandle {
__typename: "Candle";
symbol: string | null;
interval: string | null;
timestamp: any | null;
openTime: number | null;
open: number | null;
high: number | null;
low: number | null;
close: number | null;
volume: number | null;
closeTime: number | null;
quoteVolume: number | null;
trades: number | null;
baseAssetVolume: number | null;
quoteAssetVolume: number | null;
}

interface IExchangeSymbol {
__typename: "ExchangeSymbol";
symbol: string | null;
baseAsset: string | null;
baseAssetPrecision: number | null;
quoteAsset: string | null;
quotePrecision: number | null;
orderTypes: Array<string | null> | null;
icebergAllowed: boolean | null;
filters: Array<IFilter | null> | null;
}

interface IFilter {
__typename: "Filter";
filterType: string | null;
minPrice: number | null;
maxPrice: number | null;
tickSize: number | null;
minQty: number | null;
maxQty: number | null;
stepSize: number | null;
minNotional: number | null;
}

interface IPrice {
__typename: "Price";
symbol: string | null;
price: number | null;
}

interface IComment {
__typename: "Comment";
symbol: string | null;
userId: string | null;
comment: string | null;
ok: string | null;
error: string | null;
}

interface IOrderBook {
__typename: "OrderBook";
lastUpdateId: number | null;
asks: Array<IDepthDetail | null> | null;
bids: Array<IDepthDetail | null> | null;
error: number | null;
}

interface IDepthDetail {
__typename: "DepthDetail";
rowId: string | null;
price: number | null;
quantity: number | null;
}

interface INewOrder {
__typename: "NewOrder";
symbol: string | null;
orderId: string | null;
clientOrderId: string | null;
transactTime: number | null;
price: number | null;
origQty: number | null;
executedQty: number | null;
status: string | null;
timeInForce: string | null;
type: string | null;
side: string | null;
ok: string | null;
error: string | null;
}

interface IWSTicker {
__typename: "WSTicker";
eventType: string | null;
eventTime: number | null;
symbol: string | null;
priceChange: number | null;
priceChangePercent: number | null;
weightedAvg: number | null;
prevDayClose: number | null;
curDayClose: number | null;
bestBid: number | null;
bestBidQnt: number | null;
bestAsk: number | null;
bestAskQnt: number | null;
open: number | null;
high: number | null;
low: number | null;
volume: number | null;
volumeQuote: number | null;
openTime: number | null;
closeTime: number | null;
firstTradeId: number | null;
lastTradeId: number | null;
totalTrades: number | null;
percent4h: number | null;
percentVolume1h: number | null;
percentVolume2h: number | null;
percentVolume4h: number | null;
percentVolume24h: number | null;
advice: string | null;
volumeQuoteBTC: number | null;
}

interface IIndicatorsQuery {
__typename: "IndicatorsQuery";
macd: IMACD | null;
macds: Array<IMACD | null> | null;
}

interface IMacdOnIndicatorsQueryArguments {
symbol: string;
interval?: string | null;
}

interface IMacdsOnIndicatorsQueryArguments {
symbol: string;
interval?: string | null;
}

interface IMACD {
__typename: "MACD";
signal: number | null;
openTime: number | null;
MACD: number | null;
histogram: number | null;
coinPriceUsd: number | null;
}

interface IInstrumentsQuery {
__typename: "InstrumentsQuery";
fetch: Array<IInstrument | null> | null;
}

interface IFetchOnInstrumentsQueryArguments {
filters?: IInstrumentFilters | null;
}

interface IInstrumentFilters {
interval?: string | null;
ao?: string | null;
rsi?: string | null;
}

interface IInstrument {
__typename: "Instrument";
symbol: string | null;
isWhitelisted: boolean | null;
isPump: boolean | null;
isRecommended: boolean | null;
isBinanceRecommended: boolean | null;
isNewListed: boolean | null;
percent1h: number | null;
percent4h: number | null;
percent24h: number | null;
percentVolume1h: number | null;
percentVolume2h: number | null;
percentVolume4h: number | null;
percentVolume24h: number | null;
rsi15: number | null;
rsi60: number | null;
rsi120: number | null;
rsi240: number | null;
rsi1d: number | null;
listedOn: any | null;
updateTimestamp: any | null;
interval: string | null;
ao: number | null;
adx: number | null;
rsi: number | null;
btcAO1d: number | null;
atr: number | null;
ema25: number | null;
ema50: number | null;
ema100: number | null;
mfi: number | null;
vwap: number | null;
ichimoku: any | null;
roc: number | null;
bb_upper: number | null;
bb_middle: number | null;
bb_lower: number | null;
bb_obv: number | null;
ad: number | null;
so_k: number | null;
so_d: number | null;
sma7: number | null;
sma21: number | null;
sma100: number | null;
macd: number | null;
price: number | null;
}

interface IStrategyQuery {
__typename: "StrategyQuery";
getStrategyTests: Array<IStrategyTest | null> | null;
getAllStrategyTests: Array<IGlobalStrategyTest | null> | null;
}

interface IStrategyTest {
__typename: "StrategyTest";
symbol: string | null;
interval: string | null;
startDate: string | null;
endDate: string | null;
strategy: string | null;
endUsd: number | null;
invest: number | null;
status: string | null;
profit: number | null;
roundTrips: Array<IBotRoundTrip | null> | null;
botTrades: Array<IBotTrade | null> | null;
createTimestamp: string | null;
}

interface IBotRoundTrip {
__typename: "BotRoundTrip";
entryDate: any | null;
exitDate: any | null;
entryPrice: number | null;
exitPrice: number | null;
duration: number | null;
profit: number | null;
profitWithoutFees: number | null;
}

interface IBotTrade {
__typename: "BotTrade";
tradeDate: string | null;
position: string | null;
price: number | null;
profit: number | null;
profitWithoutFees: number | null;
tradeResult: string | null;
}

interface IGlobalStrategyTest {
__typename: "GlobalStrategyTest";
strategy: string | null;
interval: string | null;
profit: number | null;
profitUsd: number | null;
numShorts: number | null;
startDate: string | null;
endDate: string | null;
status: string | null;
endUsd: number | null;
invest: number | null;
currentSymbol: string | null;
progressStrategy: number | null;
progressInterval: number | null;
progressSymbol: number | null;
investPerCoin: number | null;
bestSymbolProfit: number | null;
worstSymbolProfit: number | null;
numTrades: number | null;
posTrades: number | null;
negTrades: number | null;
bestPerformer: string | null;
worstPerformer: string | null;
avgProfit: number | null;
avgHoldMinutes: number | null;
avgTradesDay: number | null;
bestTrade: number | null;
avgTrade: number | null;
worstTrade: number | null;
createTimestamp: string | null;
updateTimestamp: string | null;
sl: number | null;
}

interface IUserQuery {
__typename: "UserQuery";
saldo: ISaldoResponse | null;
saldoUsd: ISaldoResponse | null;
favorites: Array<IFavoriteCoin | null> | null;
noticedCoins: Array<INoticedCoin | null> | null;
account: IUserAccount | null;
exchangeKeys: IBinanceApiKey | null;
splitview: Array<ISplitView | null> | null;
logs: Array<ILog | null> | null;
}

interface ISaldoOnUserQueryArguments {
limit?: number | null;
}

interface ISaldoUsdOnUserQueryArguments {
days?: number | null;
}

interface ILogsOnUserQueryArguments {
limit?: number | null;
service?: string | null;
}

interface ISaldoResponse {
__typename: "SaldoResponse";
saldos: Array<ISaldo | null> | null;
yesterday: ISaldo | null;
}

interface ISaldo {
__typename: "Saldo";
timestamp: string | null;
usd: number | null;
btc: number | null;
usdDiff: number | null;
}

interface IFavoriteCoin {
__typename: "FavoriteCoin";
symbol: string | null;
}

interface INoticedCoin {
__typename: "NoticedCoin";
symbol: string | null;
buyPrice: number | null;
currentPrice: number | null;
timestamp: string | null;
isActiveCall: boolean | null;
}

interface IUserAccount {
__typename: "UserAccount";
username: string | null;
email: string | null;
notifications: IUserNotifications | null;
splitview: Array<ISplitView | null> | null;
trade: IUserTrade | null;
}

interface IUserNotifications {
__typename: "UserNotifications";
pumpbot: boolean | null;
signals: any | null;
marketBots: boolean | null;
}

interface ISplitView {
__typename: "SplitView";
window: number;
market: string | null;
interval: string | null;
}

interface IUserTrade {
__typename: "UserTrade";
alwaysSetStoploss: boolean | null;
alwaysSetTP: boolean | null;
}

interface IBinanceApiKey {
__typename: "BinanceApiKey";
key: string | null;
secret: string | null;
error: string | null;
}

interface ILog {
__typename: "Log";
msg: string | null;
service: string | null;
level: string | null;
timestamp: any | null;
}

interface IBotQuery {
__typename: "BotQuery";
formInfo: IAddNewForm | null;
marketBots: Array<IMarketBot | null> | null;
signals: Array<IBotSignal | null> | null;
signalMarkets: Array<IBotSignal | null> | null;
profit: IBotProfit | null;
profitPerDay: Array<IBotProfitPerDay | null> | null;
profitPerDayForStrategy: IBotProfitPerDay | null;
profitPerMonth: Array<IBotProfitPerDay | null> | null;
botOrders: Array<IOpenOrder | null> | null;
topCoins: Array<ITopCoin | null> | null;
suggestList: Array<IWhiteListSuggest | null> | null;
whitelist: Array<IWhiteListCoin | null> | null;
getIndicators: Array<IIndicator | null> | null;
getAllIndicators: Array<IIndicator | null> | null;
autobot: IAutoBot | null;
profitGraph: Array<IAutobotProfitHistory | null> | null;
lastBotOrders: Array<IMarketBotTrade | null> | null;
fearAltIndex: IFearAltIndex | null;
}

interface ISignalsOnBotQueryArguments {
symbol?: string | null;
strategy?: string | null;
days?: number | null;
interval?: string | null;
}

interface ISignalMarketsOnBotQueryArguments {
symbol?: string | null;
strategy?: string | null;
interval?: string | null;
}

interface IProfitOnBotQueryArguments {
strategy?: string | null;
symbol?: string | null;
interval?: string | null;
}

interface IProfitPerDayOnBotQueryArguments {
strategy?: string | null;
symbol?: string | null;
days?: number | null;
profitProgress?: boolean | null;
}

interface IProfitPerDayForStrategyOnBotQueryArguments {
strategy?: string | null;
interval?: string | null;
days?: number | null;
}

interface IProfitPerMonthOnBotQueryArguments {
strategy?: string | null;
symbol?: string | null;
months?: number | null;
}

interface IGetIndicatorsOnBotQueryArguments {
symbol?: string | null;
}

interface IAddNewForm {
__typename: "AddNewForm";
strategies: Array<IBotStrategy | null> | null;
}

interface IBotStrategy {
__typename: "BotStrategy";
name: string | null;
description: string | null;
interval: string | null;
defaultParams: string | null;
}

interface IMarketBot {
__typename: "MarketBot";
id: string | null;
symbol: string | null;
exchange: string | null;
strategy: string | null;
amountUsdtInvested: number | null;
startBalanceUSD: number | null;
finalBalanceUSD: number | null;
startBalanceBTC: number | null;
finalBalanceBTC: number | null;
startPrice: number | null;
endPrice: number | null;
marketPercent: number | null;
startDate: any | null;
stopDate: any | null;
updatedOn: any | null;
strategyParams: string | null;
interval: string | null;
profit: number | null;
trades: Array<IBotTrade | null> | null;
lastTrade: string | null;
active: boolean | null;
position: string | null;
sellInMinus: boolean | null;
simulation: boolean | null;
roundTrips: Array<IBotRoundTrip | null> | null;
timestamp: string | null;
fromAutoBot: boolean | null;
currentProfit: number | null;
currentValueUsd: number | null;
investedUsd: number | null;
}

interface IBotSignal {
__typename: "BotSignal";
id: string | null;
symbol: string | null;
price: number | null;
advice: string | null;
strategy: string | null;
interval: string | null;
profit: number | null;
timestamp: any | null;
currentDiff: number | null;
candleTime: any | null;
}

interface IBotProfit {
__typename: "BotProfit";
profit: number | null;
tradesPos: number | null;
tradesNeg: number | null;
}

interface IBotProfitPerDay {
__typename: "BotProfitPerDay";
strategy: string | null;
interval: string | null;
totalProfit: number | null;
currentDiff: number | null;
tradesPos: number | null;
tradesNeg: number | null;
dailyProfits: Array<IBotProfits | null> | null;
}

interface IBotProfits {
__typename: "BotProfits";
day: string | null;
dateStr: string | null;
profit: number | null;
}

interface ITopCoin {
__typename: "TopCoin";
symbol: string | null;
profit: number | null;
strategy: string | null;
}

interface IWhiteListSuggest {
__typename: "WhiteListSuggest";
symbol: string | null;
totalCount: number | null;
neuralCount: number | null;
longCount: number | null;
shortCount: number | null;
}

interface IWhiteListCoin {
__typename: "WhiteListCoin";
symbol: string | null;
timestamp: string | null;
autoAdded: boolean | null;
indicator: IIndicator | null;
}

interface IIndicator {
__typename: "Indicator";
symbol: string | null;
interval: string | null;
indicator: string | null;
action: string | null;
price: number | null;
timestamp: any | null;
}

interface IAutoBot {
__typename: "AutoBot";
id: string | null;
_id: string | null;
strategy: string | null;
interval: string | null;
usdPerCoin: string | null;
numbots: number | null;
autoUsdPerCoin: boolean | null;
marketBotsBig: Array<IMarketBot | null> | null;
profitToday: number | null;
profitTodayRelative: number | null;
profitYesterday: number | null;
profitThisWeek: number | null;
profitThisMonth: number | null;
profitLastMonth: number | null;
profitOverall: number | null;
active: boolean | null;
archived: boolean | null;
createTimestamp: any | null;
lastTradeTimestamp: any | null;
startTime: any | null;
profitTodayUsd: number | null;
profitYesterdayUsd: number | null;
profitThisWeekUsd: number | null;
profitThisMonthUsd: number | null;
profitLastMonthUsd: number | null;
profitOverallUsd: number | null;
accumulatedInvestUsd: number | null;
accumulatedValueUsd: number | null;
accumulatedProfit: number | null;
accumulatedProfitRelative: number | null;
activeCount: number | null;
tradesToday: number | null;
tradesTodayShort: number | null;
tradesTodayLong: number | null;
successRateToday: number | null;
winsToday: number | null;
losesToday: number | null;
autoRefillWhitelist: boolean | null;
stoplossPercent: number | null;
takeProfitPercent: number | null;
enableTakeProfit: boolean | null;
enableStoploss: boolean | null;
enableSellInMinus: boolean | null;
buyOnlyFromWhitelist: boolean | null;
fillPortfolioAfterSell: boolean | null;
buyOnlyOnAOPlus: boolean | null;
useBtcAOCross: boolean | null;
btcOnly: boolean | null;
}

interface IAutobotProfitHistory {
__typename: "AutobotProfitHistory";
percentHistory: number | null;
usdHistory: number | null;
timestamp: any | null;
}

interface IMarketBotTrade {
__typename: "MarketBotTrade";
id: string | null;
autobotId: string | null;
marketBotId: string | null;
asset: string | null;
position: string | null;
price: number | null;
valueInUsd: number | null;
profit: number | null;
amount: number | null;
tradeDate: any | null;
btcPrice: number | null;
profitUsd: number | null;
}

interface IFearAltIndex {
__typename: "FearAltIndex";
fearIndex: number | null;
altIndex: number | null;
fearText: string | null;
altText: string | null;
}

interface IPumpQuery {
__typename: "PumpQuery";
lastPumps: Array<IPump | null> | null;
}

interface IPump {
__typename: "Pump";
symbol: string | null;
notified: boolean | null;
price: number | null;
diff: number | null;
timestamp: string | null;
}

interface IMutation {
__typename: "Mutation";
login: ILoginResponse | null;
register: boolean | null;
logout: boolean | null;
accountSettingsMutation: IUserAccount | null;
saveApiKeyMutation: IBinanceApiKey | null;
saveSplitMutation: ICommon | null;
saveCurrentSaldo: boolean | null;
cancelOrderMutation: ICommon | null;
orderMutation: INewOrder | null;
commentMutation: IComment | null;
addNewBotMutation: ICommon | null;
startStopBotMutation: ICommon | null;
transformToAutoBotMutation: ICommon | null;
deleteBotMutation: ICommon | null;
saveWhiteListCoinMutation: ICommon | null;
deleteWhiteListCoinMutation: ICommon | null;
buyWhiteListCoinMutation: ICommon | null;
saveFavoriteCoinMutation: IFavoriteCoin | null;
addActiveCallMutation: ICommon | null;
stopActiveCallMutation: ICommon | null;
saveNoticedCoinMutation: ICommon | null;
deleteNoticedCoinMutation: ICommon | null;
strategyTestAdd: ICommon | null;
calcAllStrategy: ICommon | null;
backtestRecalcMutation: ICommon | null;
createAutoBot: ICommon | null;
modifyNumBotsMutation: ICommon | null;
autoUsdPerCoinMutation: ICommon | null;
stopAutoBotMutation: ICommon | null;
startAutoBotMutation: ICommon | null;
removeAutoBotMutation: ICommon | null;
sellAllMutation: ICommon | null;
buyNowMutation: ICommon | null;
shortMarketBotMutation: ICommon | null;
sellInMinusMutation: ICommon | null;
onlyFromWhitelistMutation: ICommon | null;
btcOnlyMutation: ICommon | null;
enableBuyOnlyAOPlusMutation: ICommon | null;
toggleBtcAoCrossMutation: ICommon | null;
selectBotModeMutation: ICommon | null;
selectBotRefillWhitelist: ICommon | null;
fillPortfolioAfterSellMutation: ICommon | null;
toggleTakeProfitMutation: ICommon | null;
toggleStopLossMutation: ICommon | null;
setAutobotTakeProfitMutation: ICommon | null;
setAutobotStopLossMutation: ICommon | null;
}

interface ILoginOnMutationArguments {
username: string;
password: string;
}

interface IRegisterOnMutationArguments {
username: string;
password: string;
email: string;
}

interface IAccountSettingsMutationOnMutationArguments {
email?: string | null;
pumpbot?: boolean | null;
alwaysSetStoploss?: boolean | null;
alwaysSetTP?: boolean | null;
}

interface ISaveApiKeyMutationOnMutationArguments {
key: string;
secret: string;
}

interface ISaveSplitMutationOnMutationArguments {
configs?: Array<ISplitViewSave | null> | null;
}

interface ICancelOrderMutationOnMutationArguments {
symbol: string;
orderId: string;
type: string;
recvWindow?: number | null;
}

interface IOrderMutationOnMutationArguments {
symbol: string;
side: string;
type: string;
quantity: number;
price?: number | null;
timeInForce?: string | null;
stopPrice?: number | null;
icebergQty?: number | null;
buyStopActive?: boolean | null;
buyTPActive?: boolean | null;
botTPPrice?: number | null;
botStopPrice?: number | null;
recvWindow?: number | null;
}

interface ICommentMutationOnMutationArguments {
symbol: string;
comment?: string | null;
}

interface IAddNewBotMutationOnMutationArguments {
symbol?: string | null;
interval?: string | null;
strategy?: string | null;
strategyParams?: string | null;
amountUsdtInvested?: number | null;
simulation?: boolean | null;
startWithPosition?: string | null;
longPrice?: number | null;
limitSell?: boolean | null;
}

interface IStartStopBotMutationOnMutationArguments {
id: string;
action: string;
}

interface ITransformToAutoBotMutationOnMutationArguments {
id: string;
}

interface IDeleteBotMutationOnMutationArguments {
id: string;
}

interface ISaveWhiteListCoinMutationOnMutationArguments {
symbol: string;
}

interface IDeleteWhiteListCoinMutationOnMutationArguments {
symbol: string;
}

interface IBuyWhiteListCoinMutationOnMutationArguments {
symbol: string;
}

interface ISaveFavoriteCoinMutationOnMutationArguments {
symbol: string;
}

interface IAddActiveCallMutationOnMutationArguments {
symbol: string;
}

interface IStopActiveCallMutationOnMutationArguments {
symbol: string;
}

interface ISaveNoticedCoinMutationOnMutationArguments {
symbol: string;
buyPrice: number;
}

interface IDeleteNoticedCoinMutationOnMutationArguments {
symbol: string;
}

interface IStrategyTestAddOnMutationArguments {
strategy: string;
interval: string;
startDate: string;
endDate: string;
symbol: string;
invest?: number | null;
tp?: number | null;
sl?: number | null;
}

interface ICalcAllStrategyOnMutationArguments {
strategy: string;
}

interface ICreateAutoBotOnMutationArguments {
strategy: string;
usdPerCoin: number;
}

interface IModifyNumBotsMutationOnMutationArguments {
id: string;
numbots?: number | null;
}

interface IAutoUsdPerCoinMutationOnMutationArguments {
botId: string;
value?: boolean | null;
}

interface IStopAutoBotMutationOnMutationArguments {
botId: string;
}

interface IStartAutoBotMutationOnMutationArguments {
botId: string;
}

interface IRemoveAutoBotMutationOnMutationArguments {
botId: string;
}

interface ISellAllMutationOnMutationArguments {
botId: string;
}

interface IBuyNowMutationOnMutationArguments {
botId: string;
}

interface IShortMarketBotMutationOnMutationArguments {
symbol?: string | null;
}

interface ISellInMinusMutationOnMutationArguments {
botId: string;
value: boolean;
}

interface IOnlyFromWhitelistMutationOnMutationArguments {
botId: string;
value: boolean;
}

interface IBtcOnlyMutationOnMutationArguments {
botId: string;
value: boolean;
}

interface IEnableBuyOnlyAOPlusMutationOnMutationArguments {
botId: string;
value: boolean;
}

interface IToggleBtcAoCrossMutationOnMutationArguments {
botId: string;
value: boolean;
}

interface ISelectBotModeMutationOnMutationArguments {
botId: string;
interval: string;
value: string;
}

interface ISelectBotRefillWhitelistOnMutationArguments {
botId: string;
value: boolean;
}

interface IFillPortfolioAfterSellMutationOnMutationArguments {
botId: string;
value: boolean;
}

interface IToggleTakeProfitMutationOnMutationArguments {
botId: string;
value: boolean;
}

interface IToggleStopLossMutationOnMutationArguments {
botId: string;
value: boolean;
}

interface ISetAutobotTakeProfitMutationOnMutationArguments {
botId: string;
value: number;
}

interface ISetAutobotStopLossMutationOnMutationArguments {
botId: string;
value: number;
}

interface ILoginResponse {
__typename: "LoginResponse";
access_token: string | null;
username: string | null;
userId: string | null;
}

interface ISplitViewSave {
window: number;
market?: string | null;
interval?: string | null;
}

interface ICommon {
__typename: "Common";
ok: boolean | null;
message: string | null;
error: string | null;
}

interface ISubscription {
__typename: "Subscription";
marketHistory: Array<ITrade | null> | null;
depth: IOrderBook | null;
allTickers: Array<IWSTicker | null> | null;
coinPrices: Array<IWSCoinPrices | null> | null;
coinPrice: Array<IWSCoinPrices | null> | null;
}

interface IMarketHistoryOnSubscriptionArguments {
symbol?: string | null;
}

interface IDepthOnSubscriptionArguments {
symbol: string;
}

interface IAllTickersOnSubscriptionArguments {
symbol?: string | null;
base?: string | null;
}

interface ICoinPriceOnSubscriptionArguments {
symbols?: Array<string | null> | null;
}

interface ITrade {
__typename: "Trade";
rowId: string | null;
id: number | null;
price: number | null;
qty: number | null;
time: number | null;
isBuyerMaker: boolean | null;
isBestMatch: boolean | null;
symbol: string | null;
maker: boolean | null;
tradeId: number | null;
eventType: string | null;
tradeTime: number | null;
}

interface IWSCoinPrices {
__typename: "WSCoinPrices";
symbol: string | null;
price: number | null;
}

interface INewBot {
__typename: "NewBot";
symbol: string | null;
interval: string | null;
strategy: string | null;
strategyParams: string | null;
amountUsdtInvested: number | null;
simulation: boolean | null;
startWithPosition: string | null;
longPrice: number | null;
limitSell: boolean | null;
}

interface IBotOrder {
__typename: "BotOrder";
_id: string | null;
symbol: string | null;
type: string | null;
amount: string | null;
price: number | null;
markForTP: boolean | null;
tpPrice: number | null;
addTimestamp: string | null;
executionTimestamp: string | null;
status: string | null;
}
}

// tslint:enable
