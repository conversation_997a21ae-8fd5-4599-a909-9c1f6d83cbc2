services:
  mb-server:
    container_name: mb-server
    build:
      context: .
      dockerfile: apps/server/Dockerfile
    image: mb-server
    pull_policy: build
    environment:
      NODE_ENV: "production"
    restart: always
    network_mode: host

  mb-pump:
    container_name: mb-pump
    build:
      context: .
      dockerfile: apps/pump/Dockerfile
    image: mb-pump
    pull_policy: build
    environment:
      NODE_ENV: "production"
    restart: always
    network_mode: host

  mb-advices:
    container_name: mb-advices
    build:
      context: .
      dockerfile: apps/advices/Dockerfile
    image: mb-advices
    pull_policy: build
    environment:
      NODE_ENV: "production"
    restart: always
    network_mode: host

  mb-reports:
    container_name: mb-reports
    build:
      context: .
      dockerfile: apps/reports/Dockerfile
    image: mb-reports
    pull_policy: build
    restart: always
    environment:
      NODE_ENV: "production"
    network_mode: host

  mb-orders:
    container_name: mb-orders
    build:
      context: .
      dockerfile: apps/orders/Dockerfile
    image: mb-orders
    pull_policy: build
    restart: always
    environment:
      NODE_ENV: "production"
    network_mode: host

  mb-candles:
    container_name: mb-candles
    build:
      context: .
      dockerfile: apps/candles/Dockerfile
    image: mb-candles
    pull_policy: build
    restart: always
    environment:
      NODE_ENV: "production"
    network_mode: host

  mb-bot:
    container_name: mb-bot
    build:
      context: .
      dockerfile: apps/bot/Dockerfile
    image: mb-bot
    pull_policy: build
    restart: always
    environment:
      NODE_ENV: "production"
    network_mode: host

  mb-tester:
    container_name: mb-tester
    build:
      context: .
      dockerfile: apps/tester/Dockerfile
    image: mb-tester
    pull_policy: build
    restart: always
    environment:
      NODE_ENV: "production"
    network_mode: host

  mb-indicators:
    container_name: mb-indicators
    build:
      context: .
      dockerfile: apps/indicators/Dockerfile
    image: mb-indicators
    pull_policy: build
    restart: always
    environment:
      NODE_ENV: "production"
    network_mode: host

  mb-client:
    container_name: mb-client
    build:
      context: client
      dockerfile: Dockerfile
    image: mb-client
    pull_policy: build
    restart: always
    ports:
      - 3000:3000
    environment:
      NODE_ENV: "production"
    network_mode: host