{"compilerOptions": {"target": "ES2022", "module": "commonjs", "lib": ["ES2022"], "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/index.ts", "src/simple-bot.ts", "src/config/**/*", "src/utils/**/*", "src/types/**/*"], "exclude": ["node_modules", "dist", "src/core", "src/mempool", "src/dex", "src/strategies", "src/simulation", "src/gas", "src/calldata"]}