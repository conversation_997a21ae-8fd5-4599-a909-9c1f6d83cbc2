export const createOrderText = (head, input) => {
  const rows = Object.keys(input).map(
    (k) => `<tr>
        <td>
            <b>${k}</b>
        </td>
        <td>${input[k]}</td>
    </tr>`,
  );

  return `<!DOCTYPE html>
<html lang="en">
<head>
    <style>
        html {
            font-family: Arial, serif;
            font-size: 13px;
        }

        .top {
            height: 30px;
            text-align: center;
            color: #cacaca;
            font-weight: bold;
            font-size: 14px;
            padding-top: 10px;
        }

        .panel {
            width: 99%;
            border: 1px solid #525252;
            background-color: hsla(0, 0%, 53.3%, .07);
            -webkit-box-shadow: none;
            -moz-box-shadow: none;
            box-shadow: none;
            color: #949ba2;
            border-radius: 3px;
            margin-bottom: 20px;

            top: 50px;
        }

        .panel-head {
            color: #cacaca;
            padding: 5px 10px;
            border-bottom: 1px solid hsla(0, 0%, 45.9%, .29);
            border-top-left-radius: 3px;
            border-top-right-radius: 3px;
            background-color: #363841;
            font-weight: 700;
            text-transform: uppercase;
            text-decoration: none;
        }

        .panel-body {
            padding: 5px 15px 15px;
            text-align: center;
        }

        table tr td:nth-child(2) {
            font-weight: bold;
        }
        
        .accent {
            color: #f6a821;
        }

    </style>
</head>
<body>
<div>
    <div class="panel">
        <div class="panel-head">${head}</div>
        <div class="panel-body">
            <table width="100%">
                <tbody>
                ${rows.join(' ')}
                </tbody>
            </table>
        </div>
    </div>
</div>
</body>
</html>`;
};
