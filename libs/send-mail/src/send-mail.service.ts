import { Injectable } from '@nestjs/common';
import * as nodemailer from 'nodemailer';
import { DbUserService } from '@app/database/db-user/db-user.service';
import { createDailyReportText } from '@app/send-mail/mail-templates/dailyReport';
import { createOrderText } from '@app/send-mail/mail-templates/order';
import { DbLogService } from '@app/database/db-log/db-log.service';

@Injectable()
export class SendMailService {
  constructor(
    private userService: DbUserService,
    private logger: DbLogService,
  ) {}

  smtpConfig = {
    host: 'mx2f11.netcup.net',
    auth: {
      user: '<EMAIL>',
      pass: '3Jto3e5%',
    },
    tls: {
      rejectUnauthorized: false,
    },
  };

  async sendConfirmRegistrationLink(email, url) {
    const subject = 'Moonbot registration';
    const html = `<html>
                    <body>
                    <p>To confirm registration, click on the link below:</p> <a href="${url}">${url}</a>
                    </body></html>`;
    const transporter = nodemailer.createTransport(this.smtpConfig);

    const message = {
      from: '<EMAIL>',
      to: email,
      subject,
      html,
    };

    await transporter.sendMail(message, (error, _info) => {
      if (error) {
        this.logger.errorServer(error.message);
      }
    });
  }

  async sendMailNotification(userId, subject, text) {
    const userObj = await this.userService.findUserById(userId);
    const { email } = userObj;
    const transporter = nodemailer.createTransport(this.smtpConfig);

    const message = {
      from: '<EMAIL>',
      to: email,
      subject,
      text,
    };

    await transporter.sendMail(message, (error, _info) => {
      if (error) {
        this.logger.errorServer(error.message);
      }
    });
  }

  async sendOrderMail(
    userId: string,
    subject: string,
    head: string,
    content: object,
    force: boolean,
  ) {
    const userObj = await this.userService.findUserById(userId);
    const { email, notifications } = userObj;

    if (!force && notifications && notifications.signals) {
      const transporter = nodemailer.createTransport(this.smtpConfig);
      const html = createOrderText(head, content);

      const message = {
        from: '<EMAIL>',
        to: email,
        subject,
        html,
      };

      transporter.sendMail(message, (error, _info) => {
        if (error) {
          this.logger.errorServer(error.message);
        }
      });
    }
  }

  async sendErrorMail(userId, subject, head, content) {
    const userObj = await this.userService.findUserById(userId);
    const { email } = userObj;

    const transporter = nodemailer.createTransport(this.smtpConfig);
    const html = createOrderText(head, content);

    const message = {
      from: '<EMAIL>',
      to: email,
      subject,
      html,
    };

    await transporter.sendMail(message, (error, _info) => {
      if (error) {
        this.logger.errorServer(error.message);
      }
    });
  }

  async sendDailyReportMail(userId: string, subject: string, content: any) {
    const userObj = await this.userService.findUserById(userId);
    const { email } = userObj;
    const transporter = nodemailer.createTransport(this.smtpConfig);
    const html = await createDailyReportText(content);

    const message = {
      from: '<EMAIL>',
      to: email,
      subject,
      html,
    };

    await transporter.sendMail(message, (error, _) => {
      if (error) {
        this.logger.errorServer(error.message);
      }
    });
  }
}
