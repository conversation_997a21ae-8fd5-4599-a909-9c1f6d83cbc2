import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { CandleChartResult, CandlesOptions } from 'binance-api-node';
import { HyperliquidConnectService } from '@app/hyperliquid-api/hyperliquid-connect/hyperliquid-connect.service';
import * as hl from '@nktkas/hyperliquid';

@Injectable()
export class HyperliquidCandleSticksService {
  constructor(
    private logger: DbLogService,
    private connectService: HyperliquidConnectService,
  ) {}

  /**
   * Get candlestick data from Hyperliquid
   * Returns data in Binance-compatible format
   */
  async getCandleSticks(args: CandlesOptions): Promise<CandleChartResult[]> {
    const infoClient = this.connectService.initInfoClient();
    
    try {
      // Convert symbol format (BTCUSDT -> BTC)
      const coin = args.symbol.replace('USDT', '');
      
      // Convert interval format
      const interval = this.convertInterval(args.interval);
      
      // Calculate time range
      const endTime = args.endTime || Date.now();
      const startTime = args.startTime || (endTime - (args.limit || 500) * this.getIntervalMs(interval));

      const result = await infoClient.candleSnapshot({
        coin,
        interval,
        startTime: Math.floor(startTime / 1000), // Hyperliquid expects seconds
        endTime: Math.floor(endTime / 1000),
      });

      if (!result || !Array.isArray(result)) {
        this.logger.errorServer('Invalid candlestick data received from Hyperliquid');
        return [];
      }

      // Convert to Binance format
      return result.map((candle) => {
        return {
          openTime: candle.t * 1000, // Convert to milliseconds
          open: candle.o.toString(),
          high: candle.h.toString(),
          low: candle.l.toString(),
          close: candle.c.toString(),
          volume: candle.v.toString(),
          closeTime: (candle.t * 1000) + this.getIntervalMs(interval) - 1,
          quoteAssetVolume: (parseFloat(candle.v) * parseFloat(candle.c)).toString(),
          trades: 0, // Not provided by Hyperliquid
          baseAssetVolume: candle.v.toString(),
        } as CandleChartResult;
      });
    } catch (e) {
      this.logger.errorServer('Error fetching Hyperliquid candlesticks', e);
    }

    return [];
  }

  /**
   * Convert Binance interval format to Hyperliquid format
   */
  private convertInterval(binanceInterval: string): string {
    const intervalMap: { [key: string]: string } = {
      '1m': '1m',
      '3m': '3m',
      '5m': '5m',
      '15m': '15m',
      '30m': '30m',
      '1h': '1h',
      '2h': '2h',
      '4h': '4h',
      '6h': '6h',
      '8h': '8h',
      '12h': '12h',
      '1d': '1d',
      '3d': '3d',
      '1w': '1w',
      '1M': '1M',
    };

    return intervalMap[binanceInterval] || '1h';
  }

  /**
   * Get interval duration in milliseconds
   */
  private getIntervalMs(interval: string): number {
    const intervalMs: { [key: string]: number } = {
      '1m': 60 * 1000,
      '3m': 3 * 60 * 1000,
      '5m': 5 * 60 * 1000,
      '15m': 15 * 60 * 1000,
      '30m': 30 * 60 * 1000,
      '1h': 60 * 60 * 1000,
      '2h': 2 * 60 * 60 * 1000,
      '4h': 4 * 60 * 60 * 1000,
      '6h': 6 * 60 * 60 * 1000,
      '8h': 8 * 60 * 60 * 1000,
      '12h': 12 * 60 * 60 * 1000,
      '1d': 24 * 60 * 60 * 1000,
      '3d': 3 * 24 * 60 * 60 * 1000,
      '1w': 7 * 24 * 60 * 60 * 1000,
      '1M': 30 * 24 * 60 * 60 * 1000,
    };

    return intervalMs[interval] || 60 * 60 * 1000; // Default to 1 hour
  }
}
