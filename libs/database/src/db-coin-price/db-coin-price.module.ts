import { Module } from '@nestjs/common';
import { DbCoinPriceService } from './db-coin-price.service';
import { MongooseModule } from '@nestjs/mongoose';
import { DbLogModule } from '@app/database/db-log/db-log.module';
import {
  DbCoinPrice,
  DbCoinPriceSchema,
} from '@app/database/db-coin-price/db-coin-price.model';

@Module({
  providers: [DbCoinPriceService],
  exports: [
    DbCoinPriceService,
    MongooseModule.forFeature([
      {
        name: DbCoinPrice.name,
        schema: DbCoinPriceSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    MongooseModule.forFeature([
      {
        name: DbCoinPrice.name,
        schema: DbCoinPriceSchema,
      },
    ]),
  ],
})
export class DbCoinPriceModule {}
