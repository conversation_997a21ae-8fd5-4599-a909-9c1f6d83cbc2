import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import { Model } from 'mongoose';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { COIN_PRICES_SUBSCRIPTION, pubsub } from '../../../../utils/pubSub';
import {
  DbCoinPrice,
  DbCoinPriceDocument,
} from '@app/database/db-coin-price/db-coin-price.model';

@Injectable()
export class DbCoinPriceService {
  constructor(
    @InjectModel(DbCoinPrice.name)
    private coinPriceModel: Model<DbCoinPriceDocument>,
    private logger: DbLogService,
  ) {}

  public async dbSetCurrentCoinPrice(
    symbol: string,
    price: number,
    exchange: string,
  ) {
    try {
      await this.coinPriceModel
        .findOneAndUpdate(
          {
            symbol,
            exchange,
          },
          {
            symbol,
            price,
            timestamp: new Date(),
            exchange,
          },
          { upsert: true, new: true },
        )
        .lean()
        .exec();
    } catch (e) {
      this.logger.errorServer(e);
    }
  }

  public async dbPublishCurrentCoinPrices() {
    const result = await this.coinPriceModel.find().lean().exec();

    pubsub.publish(COIN_PRICES_SUBSCRIPTION, result);
  }

  public async dbGetCoinPrices(exchange: string) {
    return await this.coinPriceModel
      .find({
        exchange,
      })
      .lean()
      .exec();
  }

  public async dbGetCoinPricesIn(exchange: string, symbols: string[]) {
    return await this.coinPriceModel
      .find({
        exchange,
        symbol: { $in: symbols },
      })
      .lean()
      .exec();
  }

  public async dbGetCoinPrice(symbol: string, exchange: string) {
    const result = await this.coinPriceModel
      .findOne({
        symbol,
        exchange,
      })
      .lean()
      .exec();

    return result?.price ? Number(result?.price) : 0;
  }

  public async dbGetCoinPricesBySymbols(symbols: string[], exchange: string) {
    const result = await this.coinPriceModel
      .find({
        symbol: { $in: symbols },
        exchange,
      })
      .sort({ timestamp: -1 })
      .lean()
      .exec();

    return result.map((x) => {
      return {
        symbol: x.symbol,
        price: Number(x.price),
      };
    });
  }
}
