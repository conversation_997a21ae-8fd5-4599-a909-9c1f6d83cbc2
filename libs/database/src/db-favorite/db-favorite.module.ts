import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';

import { DbLogModule } from '@app/database/db-log/db-log.module';
import { DbFavoriteService } from '@app/database/db-favorite/db-favorite.service';
import {
  DbFavorite,
  DbFavoriteSchema,
} from '@app/database/db-favorite/db-favorite.model';

@Module({
  providers: [DbFavoriteService],
  exports: [
    DbFavoriteService,
    MongooseModule.forFeature([
      {
        name: DbFavorite.name,
        schema: DbFavoriteSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    MongooseModule.forFeature([
      {
        name: DbFavorite.name,
        schema: DbFavoriteSchema,
      },
    ]),
  ],
})
export class DbFavoriteModule {}
