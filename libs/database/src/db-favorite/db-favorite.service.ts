import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  DbFavorite,
  DbFavoriteDocument,
} from '@app/database/db-favorite/db-favorite.model';
import { Model } from 'mongoose';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { uniq } from 'ramda';

@Injectable()
export class DbFavoriteService {
  constructor(
    @InjectModel(DbFavorite.name)
    private favoriteModel: Model<DbFavoriteDocument>,
    private logger: DbLogService,
  ) {}

  async dbGetFavoiteCoins(userId: string, exchange: string) {
    try {
      return this.favoriteModel
        .find({
          userId: userId,
          exchange,
        })
        .lean()
        .exec();
    } catch (e) {
      this.logger.errorServer(e.message);
    }

    return null;
  }

  async dbGetAllFavoiteCoins(exchange: string) {
    try {
      const result = await this.favoriteModel
        .find({
          exchange,
        })
        .lean()
        .exec();

      return uniq(result.map((x) => x.symbol));
    } catch (e) {
      this.logger.errorServer(e.message);
    }

    return null;
  }

  public async dbSaveFavoriteCoin(
    userId: string,
    args: GQL.ISaveFavoriteCoinMutationOnMutationArguments,
    exchange: string,
  ): Promise<any> {
    const result = await this.favoriteModel
      .findOne({
        userId: userId,
        symbol: args.symbol,
        exchange,
      })
      .lean()
      .exec();
    if (result) {
      return this.favoriteModel
        .deleteOne({
          userId: userId,
          symbol: args.symbol,
        })
        .exec();
    }

    return this.favoriteModel
      .findOneAndUpdate(
        {
          userId: userId,
          symbol: args.symbol,
        },
        {
          userId: userId,
          symbol: args.symbol,
        },
        { upsert: true },
      )
      .lean()
      .exec();
  }
}
