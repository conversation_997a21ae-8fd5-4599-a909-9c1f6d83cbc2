import { Module } from '@nestjs/common';
import { DbTradeService } from './db-trade.service';
import { MongooseModule } from '@nestjs/mongoose';

import { DbLogModule } from '@app/database/db-log/db-log.module';
import { DbTrade, DbTradeSchema } from '@app/database/db-trade/db-trade.model';

@Module({
  providers: [DbTradeService],
  exports: [
    DbTradeService,
    MongooseModule.forFeature([
      {
        name: DbTrade.name,
        schema: DbTradeSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    MongooseModule.forFeature([
      {
        name: DbTrade.name,
        schema: DbTradeSchema,
      },
    ]),
  ],
})
export class DbTradeModule {}
