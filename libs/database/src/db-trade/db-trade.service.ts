import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { DbLogService } from '@app/database/db-log/db-log.service';
import {
  DbTrade,
  DbTradeDocument,
} from '@app/database/db-trade/db-trade.model';
import { Order } from 'binance-api-node';

@Injectable()
export class DbTradeService {
  constructor(
    @InjectModel(DbTrade.name) private tradeModel: Model<DbTradeDocument>,
    private readonly logger: DbLogService,
  ) {}

  async saveUserTrade(
    userId: string,
    data: any,
    result: Order,
    exchange: string,
  ) {
    try {
      const { symbol, side, type, price, quantity } = data;

      return await new this.tradeModel({
        user: userId,
        symbol,
        price,
        side,
        type,
        quantity,
        result,
        timestamp: new Date(),
        exchange,
      }).save();
    } catch (e) {
      this.logger.errorServer(e);
    }

    return null;
  }

  async getLastTrades(userId: string, args: any, exchange: string) {
    try {
      const { limit, symbol } = args;
      const query: any = {
        user: userId,
        exchange,
      };

      if (symbol) {
        query.symbol = symbol;
      }

      return this.tradeModel
        .find(query)
        .sort({
          timestamp: -1,
        })
        .lean()
        .limit(limit || 999)
        .exec();
    } catch (e) {
      this.logger.errorServer(e);
    }

    return null;
  }
}
