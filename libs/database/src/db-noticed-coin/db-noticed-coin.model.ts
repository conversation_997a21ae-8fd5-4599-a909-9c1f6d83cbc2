import { HydratedDocument } from 'mongoose';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbNoticedCoinDocument = HydratedDocument<DbNoticedCoin>;

@Schema({ collection: 'noticedcoins' })
export class DbNoticedCoin {
  @Prop()
  userId: string;

  @Prop()
  symbol: string;

  @Prop()
  buyPrice: number;

  @Prop({ type: Date })
  timestamp: any;

  @Prop()
  isActiveCall: boolean;

  @Prop()
  exchange: string;
}

export const DbNoticedCoinSchema = SchemaFactory.createForClass(DbNoticedCoin);
DbNoticedCoinSchema.index({ userId: 1 });
