import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { DbLogService } from '@app/database/db-log/db-log.service';
import {
  DbNoticedCoin,
  DbNoticedCoinDocument,
} from '@app/database/db-noticed-coin/db-noticed-coin.model';
import { uniq } from 'ramda';

@Injectable()
export class DbNoticedCoinService {
  constructor(
    @InjectModel(DbNoticedCoin.name)
    private noticedCoinDocumentModel: Model<DbNoticedCoinDocument>,
    private logger: DbLogService,
  ) {}

  getNoticedCoins = async (userId: string, exchange: string) => {
    try {
      return this.noticedCoinDocumentModel
        .find({
          userId: userId,
          exchange,
        })
        .sort({
          symbol: 1,
        })
        .lean()
        .exec();
    } catch (e) {
      this.logger.errorServer(e.message);
    }

    return null;
  };

  getAllNoticedCoins = async (exchange: string) => {
    try {
      const result = await this.noticedCoinDocumentModel
        .find({
          exchange,
        })
        .sort({
          symbol: 1,
        })
        .lean()
        .exec();

      return uniq(result.map((x) => x.symbol));
    } catch (e) {
      this.logger.errorServer(e.message);
    }

    return null;
  };

  saveNoticedCoin = async (
    userId: string,
    args: GQL.ISaveNoticedCoinMutationOnMutationArguments,
    exchange: string,
  ) => {
    try {
      await new this.noticedCoinDocumentModel({
        userId: userId,
        symbol: args.symbol,
        buyPrice: args.buyPrice,
        timestamp: new Date(),
        exchange: exchange,
      }).save();

      return {
        ok: true,
        error: null,
      };
    } catch (e) {
      throw Error('Error on saving favorite coin');
    }
  };

  deleteNoticedCoin = async (
    userId: string,
    args: GQL.IDeleteNoticedCoinMutationOnMutationArguments,
    exchange: string,
  ) => {
    await this.noticedCoinDocumentModel
      .deleteOne({
        userId: userId,
        symbol: args.symbol,
        exchange,
      })
      .exec();

    return {
      ok: true,
      error: null,
    };
  };

  stopActiveCall = async (
    userId: string,
    args: GQL.IStopActiveCallMutationOnMutationArguments,
    exchange: string,
  ) => {
    await this.noticedCoinDocumentModel
      .findOneAndUpdate(
        {
          userId: userId,
          symbol: args.symbol,
          exchange,
        },
        {
          $set: {
            isActiveCall: false,
          },
        },
      )
      .exec();

    return {
      ok: true,
      error: null,
    };
  };

  addActiveCall = async (
    userId: string,
    args: GQL.IAddActiveCallMutationOnMutationArguments,
    exchange: string,
  ) => {
    await this.noticedCoinDocumentModel
      .findOneAndUpdate(
        {
          userId: userId,
          symbol: args.symbol,
          exchange,
        },
        {
          $set: {
            isActiveCall: true,
          },
        },
      )
      .exec();

    return {
      ok: true,
      error: null,
    };
  };
}
