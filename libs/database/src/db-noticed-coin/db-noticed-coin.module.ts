import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import {
  DbFavorite,
  DbFavoriteSchema,
} from '@app/database/db-favorite/db-favorite.model';
import { DbLogModule } from '@app/database/db-log/db-log.module';
import { DbNoticedCoinService } from '@app/database/db-noticed-coin/db-noticed-coin.service';
import {
  DbNoticedCoin,
  DbNoticedCoinSchema,
} from '@app/database/db-noticed-coin/db-noticed-coin.model';

@Module({
  providers: [DbNoticedCoinService],
  exports: [
    DbNoticedCoinService,
    MongooseModule.forFeature([
      {
        name: DbNoticedCoin.name,
        schema: DbNoticedCoinSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    MongooseModule.forFeature([
      {
        name: DbNoticedCoin.name,
        schema: DbNoticedCoinSchema,
      },
    ]),
  ],
})
export class DbNoticedCoinModule {}
