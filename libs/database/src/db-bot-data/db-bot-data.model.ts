import { HydratedDocument } from 'mongoose';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbBotDataDocument = HydratedDocument<DbBotData>;

@Schema({ collection: 'botdatas' })
export class DbBotData {
  @Prop()
  strategy: string;

  @Prop()
  symbol: string;

  @Prop()
  interval: string;

  @Prop({ type: Object })
  tradeData: any;

  @Prop({ type: Object })
  trainingData: any;

  @Prop({ type: Date })
  timestamp: any;

  @Prop({ type: Date })
  trainingTimestamp: any;

  @Prop()
  exchange: string;
}

export const DbBotDataSchema = SchemaFactory.createForClass(DbBotData);
DbBotDataSchema.index({ symbol: 1, interval: 1, strategy: 1, exchange: 1 });
