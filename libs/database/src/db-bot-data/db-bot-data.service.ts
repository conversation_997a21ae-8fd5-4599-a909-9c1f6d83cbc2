import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  DbBotData,
  DbBotDataDocument,
} from '@app/database/db-bot-data/db-bot-data.model';

@Injectable()
export class DbBotDataService {
  constructor(
    @InjectModel(DbBotData.name)
    private botDataModel: Model<DbBotDataDocument>,
  ) {}

  updateBotTradeData = async (
    strategy: string,
    symbol: string,
    interval: string,
    exchange: string,
    tradeData: any,
  ) => {
    return this.botDataModel
      .findOneAndUpdate(
        {
          strategy,
          symbol,
          interval,
          exchange,
        },
        {
          $set: {
            tradeData: tradeData,
            timestamp: new Date(),
            exchange,
          },
        },
        { upsert: true, new: true },
      )
      .lean()
      .exec();
  };

  public updateBotTrainingData = async (
    strategy: string,
    interval: string,
    symbol: string,
    trainingData: any,
    exchange: string,
  ) => {
    return this.botDataModel
      .findOneAndUpdate(
        {
          strategy,
          interval,
          symbol,
          exchange,
        },
        {
          $set: {
            trainingData: trainingData,
            trainingTimestamp: new Date(),
            exchange,
          },
        },
        { upsert: true, new: true },
      )
      .lean()
      .exec();
  };

  /**
   * Fetch bot data.
   *
   * @param strategy
   * @param symbol
   * @param interval
   * @param exchange
   */
  getBotData = async (
    strategy: string,
    symbol: string,
    interval: string,
    exchange: string,
  ): Promise<DbBotDataDocument> => {
    try {
      const result = await this.botDataModel
        .findOne({
          strategy,
          interval,
          symbol,
          exchange,
        })
        .lean()
        .exec();

      return result ? result : null;
    } catch (e) {
      throw e;
    }
  };
}
