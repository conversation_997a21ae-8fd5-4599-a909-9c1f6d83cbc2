import { HydratedDocument } from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbAutoBotDocument = HydratedDocument<DbAutoBot>;

@Schema({ collection: 'autobots' })
export class DbAutoBot {
  @Prop()
  userId: string;

  @Prop()
  strategy: string;

  // Minimale Summe, welche man haben muss um ein Coin vom Bot kaufen zu können
  @Prop()
  usdPerCoin: string;

  // Maximale Anzahl an activebots. Wird nicht verwendet
  @Prop()
  numbots: number;

  @Prop()
  profitToday: number;

  @Prop()
  profitTodayRelative: number;

  @Prop()
  profitTodayUsd: number;

  @Prop()
  profitYesterday: number;

  @Prop()
  profitYesterdayUsd: number;

  @Prop()
  profitThisWeek: number;

  @Prop()
  profitThisWeekUsd: number;

  @Prop()
  profitThisMonth: number;

  @Prop()
  profitThisMonthUsd: number;

  @Prop()
  profitLastMonth: number;

  @Prop()
  profitLastMonthUsd: number;

  @Prop()
  profitOverall: number;

  @Prop()
  profitOverallUsd: number;

  @Prop()
  active: boolean;

  // Bot stopped (wird nicht verwendet)
  @Prop()
  archived: boolean;

  @Prop({ type: Date })
  createTimestamp: any;

  @Prop({ type: Date })
  lastTradeTimestamp: any;

  @Prop({ type: Date })
  startTime: any;

  @Prop()
  interval: string;

  @Prop()
  autoUsdPerCoin: boolean;

  @Prop()
  autoRefillWhitelist: boolean;

  @Prop()
  stoplossPercent: number;

  @Prop()
  takeProfitPercent: number;

  @Prop()
  enableTakeProfit: boolean;

  @Prop()
  enableStoploss: boolean;

  @Prop()
  enableSellInMinus: boolean;

  @Prop()
  buyOnlyFromWhitelist: boolean;

  @Prop()
  buyOnlyOnAOPlus: boolean;

  @Prop()
  useBtcAOCross: boolean;

  @Prop()
  fillPortfolioAfterSell: boolean;

  @Prop()
  btcOnly: boolean;

  @Prop()
  exchange: string;

  activeCount: number; // Number active bots
  accumulatedProfit: number; // Accumulated profit in % of all active bots
  accumulatedProfitRelative: number; // Relative to current account value
  accumulatedValueUsd: number; // Accumulated current USD value of all active bots
  accumulatedInvestUsd: number; // Accumulated USD invested in bots initially

  successRateToday: number;
  winsToday: number;
  losesToday: number;

  tradesToday: number;
  tradesTodayShort: number;
  tradesTodayLong: number;
  marketBotsBig: any[];
}

export const DbAutoBotSchema = SchemaFactory.createForClass(DbAutoBot);
DbAutoBotSchema.index({ userId: 1 });
DbAutoBotSchema.index({ userId: 1, exchange: 1 });
DbAutoBotSchema.index({ exchange: 1 });
DbAutoBotSchema.index({ exchange: 1, strategy: 1 });
