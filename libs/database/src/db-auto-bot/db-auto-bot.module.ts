import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { DbLogModule } from '@app/database/db-log/db-log.module';
import {
  DbAutoBot,
  DbAutoBotSchema,
} from '@app/database/db-auto-bot/db-auto-bot.model';
import { DbAutoBotService } from '@app/database/db-auto-bot/db-auto-bot.service';
import { DbMarketBotTradeModule } from '@app/database/db-market-bot-trade/db-market-bot-trade.module';
import { DbCoinPriceModule } from '@app/database/db-coin-price/db-coin-price.module';
import { DbMarketBotModule } from '@app/database/db-market-bot/db-market-bot.module';

@Module({
  providers: [DbAutoBotService],
  exports: [
    DbAutoBotService,
    MongooseModule.forFeature([
      {
        name: DbAutoBot.name,
        schema: DbAutoBotSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    DbMarketBotTradeModule,
    DbMarketBotModule,
    DbCoinPriceModule,
    MongooseModule.forFeature([
      {
        name: DbAutoBot.name,
        schema: DbAutoBotSchema,
      },
    ]),
  ],
})
export class DbAutoBotModule {}
