import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import { Model, Types } from 'mongoose';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { descend, prop, sortWith } from 'ramda';
import { DbMarketBotTradeService } from '@app/database/db-market-bot-trade/db-market-bot-trade.service';
import PriceCalculation from '../../../../utils/PriceCalculation';
import { DbMarketBotService } from '@app/database/db-market-bot/db-market-bot.service';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import {
  DbAutoBot,
  DbAutoBotDocument,
} from '@app/database/db-auto-bot/db-auto-bot.model';

@Injectable()
export class DbAutoBotService {
  constructor(
    @InjectModel(DbAutoBot.name)
    private autoBotModel: Model<DbAutoBotDocument>,
    private logger: DbLogService,
    private marketBotTradeService: DbMarketBotTradeService,
    private marketBotService: DbMarketBotService,
    private coinPriceService: DbCoinPriceService,
  ) {}

  async dbGetAutoBot(id: Types.ObjectId | string) {
    return this.autoBotModel
      .findOne({
        _id: id,
      })
      .lean()
      .exec();
  }

  async dbCreateAutoBot(args: any, userId: string, exchange: string) {
    const { strategy, usdPerCoin } = args;

    await new this.autoBotModel({
      userId: userId,
      strategy,
      usdPerCoin,
      numbots: 100,

      profitToday: 0,
      profitLastMonth: 0,
      profitOverall: 0,

      active: true,
      archived: false,
      createTimestamp: new Date(),
      startTime: new Date(),
      interval: '15',
      autoRefillWhitelist: false,

      stoplossPercent: 10,
      takeProfitPercent: 20,
      enableTakeProfit: true,
      enableStoploss: true,

      enableSellInMinus: true,
      buyOnlyFromWhitelist: true,
      buyOnlyOnAOPlus: true,
      useBtcAOCross: false,

      exchange: exchange,
    }).save();
  }

  async dbModifyNumBotsAutoBot(args: any, userId: string) {
    const { numbots } = args;
    try {
      await this.autoBotModel
        .findOneAndUpdate(
          {
            userId: userId,
            _id: args.id,
          },
          {
            $set: {
              numbots,
            },
          },
        )
        .lean()
        .exec();
    } catch (e) {
      this.logger.errorServer(e);

      throw e;
    }
  }

  async dbModifyAutoUsdPerCoinAutoBot(args: any, userId: string) {
    const { value, botId } = args;
    try {
      await this.autoBotModel
        .findOneAndUpdate(
          {
            userId: userId,
            _id: botId,
          },
          {
            $set: {
              autoUsdPerCoin: value,
            },
          },
        )
        .lean()
        .exec();
    } catch (e) {
      this.logger.errorServer(e);
      throw e;
    }
  }

  async dbAutobotSetMode(
    args: GQL.ISelectBotModeMutationOnMutationArguments,
    userId: string,
  ) {
    const { value, interval, botId } = args;

    await this.autoBotModel
      .findOneAndUpdate(
        {
          userId: userId,
          _id: botId,
        },
        {
          $set: {
            strategy: value,
            interval: interval,
          },
        },
      )
      .lean()
      .exec();
  }

  async dbRemoveAutoBot(args: any, userId: string) {
    const autobot = await this.dbGetAutoBot(args.botId);
    await this.marketBotService.deleteAllForAutobot(autobot._id.toString());

    await this.autoBotModel
      .deleteOne({
        userId: userId,
        _id: args.botId,
      })
      .exec();
  }

  async dbStartAutoBot(args: any, userId: string) {
    await this.autoBotModel
      .findOneAndUpdate(
        {
          userId: userId,
          _id: args.botId,
        },
        {
          $set: {
            active: true,
          },
        },
      )
      .exec();
  }

  async dbStopAutoBot(args: any, userId: string) {
    await this.autoBotModel
      .findOneAndUpdate(
        {
          userId: userId,
          _id: args.botId,
        },
        {
          $set: {
            active: false,
          },
        },
      )
      .exec();
  }

  async dbGetPlainAutobots() {
    return await this.autoBotModel
      .find({
        archived: false,
      })
      .lean()
      .exec();
  }

  async dbGetPlainAutobotByUser(userId: string, exchange: string) {
    return this.autoBotModel
      .findOne({
        userId: userId,
        exchange,
        archived: false,
      })
      .lean()
      .exec();
  }

  async dbGetAutobot(userId: string, exchange: string) {
    const autobot = await this.autoBotModel
      .findOne({
        userId: userId,
        archived: false,
        exchange,
      })
      .lean()
      .exec();

    if (autobot) {
      const marketBots = await this.marketBotService.dbGetMarketBotsForAutobot(
        autobot._id.toString(),
      );

      let accumulatedInvestUsd = 0;
      let accumulatedProfit = 0;
      let accumulatedValueUsd = 0;

      for (const marketBot of marketBots) {
        marketBot.id = marketBot._id.toString();
        const currentPrice = await this.coinPriceService.dbGetCoinPrice(
          marketBot.symbol,
          autobot.exchange,
        );
        const lastTrade =
          (await this.marketBotTradeService.dbGetLastMarketBotTrade(
            marketBot._id.toString(),
          )) || null;

        marketBot.currentProfit = PriceCalculation.toPercentGain(
          marketBot.startPrice,
          currentPrice,
        );
        marketBot.currentValueUsd = lastTrade?.amount
          ? Number(currentPrice) * lastTrade.amount
          : 0;
        marketBot.amount = lastTrade?.amount ? lastTrade.amount : 0;
        marketBot.investedUsd = lastTrade ? Number(lastTrade?.valueInUsd) : 0;

        accumulatedValueUsd += Number(marketBot.currentValueUsd);
        accumulatedInvestUsd += lastTrade?.valueInUsd
          ? Number(lastTrade?.valueInUsd)
          : 0;
        accumulatedProfit += marketBot?.currentProfit
          ? Number(marketBot.currentProfit)
          : 0;
      }

      autobot.id = autobot._id.toString();
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      autobot.marketBotsBig = sortWith([descend(prop('startDate'))])(
        marketBots,
      );

      autobot.activeCount = marketBots.length;
      autobot.accumulatedProfit = accumulatedProfit;
      autobot.accumulatedInvestUsd = accumulatedInvestUsd;
      autobot.accumulatedValueUsd = accumulatedValueUsd;
    }

    return autobot;
  }

  async dbGetMatchedAutobots(
    exchange: string,
    strategy: string,
    interval: string,
  ) {
    const query: any = {
      active: true,
      exchange,
    };

    if (strategy) {
      query.strategy = strategy;
      query.interval = interval;
    }

    return this.autoBotModel.find(query).lean().exec();
  }

  async dbGetAllAutobots() {
    return this.autoBotModel.find().lean().exec();
  }

  async dbGetAllActiveAutobots() {
    return this.autoBotModel
      .find({
        active: true,
      })
      .lean()
      .exec();
  }

  async dbGetAllAutobotsByModeAndInterval(indicator: string, interval: string) {
    return this.autoBotModel
      .find({
        active: true,
        strategy: indicator,
        interval: interval,
      })
      .lean()
      .exec();
  }

  async dbTransformAutoBot(
    args: GQL.ITransformToAutoBotMutationOnMutationArguments,
    userId: string,
  ) {
    const autobot = await this.autoBotModel
      .findOne({
        active: true,
        userId: userId,
      })
      .lean()
      .exec();

    await this.marketBotService.dbUpdateMarketBot(args, autobot);
  }

  async dbUpdateAutoBotProfit(autobot: DbAutoBotDocument, profit: any) {
    const {
      profitToday,
      profitTodayRelative,
      profitYesterday,
      profitThisWeek,
      profitThisMonth,
      profitLastMonth,
      profitOverall,
      profitTodayUsd,
      profitYesterdayUsd,
      profitThisWeekUsd,
      profitLastMonthUsd,
      profitThisMonthUsd,
      profitOverallUsd,
    } = profit;

    await this.autoBotModel
      .findOneAndUpdate(
        { _id: autobot._id.toString() },
        {
          $set: {
            profitToday,
            profitTodayRelative,
            profitYesterday,
            profitThisWeek,
            profitThisMonth,
            profitLastMonth,
            profitOverall,
            profitTodayUsd,
            profitYesterdayUsd,
            profitThisWeekUsd,
            profitLastMonthUsd,
            profitThisMonthUsd,
            profitOverallUsd,
          },
        },
      )
      .lean()
      .exec();
  }

  public dbAutobotSetSellInMinus = async (
    args: GQL.ISellInMinusMutationOnMutationArguments,
    userId: string,
  ) => {
    const { value, botId } = args;

    await this.autoBotModel
      .findOneAndUpdate(
        {
          userId: userId,
          _id: botId,
        },
        {
          $set: {
            enableSellInMinus: value,
          },
        },
      )
      .lean()
      .exec();
  };

  dbAutobotSetRefillWhitelist = async (
    args: GQL.ISelectBotRefillWhitelistOnMutationArguments,
    userId: string,
  ) => {
    const { value, botId } = args;

    await this.autoBotModel
      .findOneAndUpdate(
        {
          userId: userId,
          _id: botId,
        },
        {
          $set: {
            autoRefillWhitelist: value,
          },
        },
      )
      .lean()
      .exec();
  };

  dbAutobotSetOnlyAOPlus = async (
    args: GQL.IEnableBuyOnlyAOPlusMutationOnMutationArguments,
    userId: string,
  ) => {
    const { value, botId } = args;

    await this.autoBotModel
      .findOneAndUpdate(
        {
          userId: userId,
          _id: botId,
        },
        {
          $set: {
            buyOnlyOnAOPlus: value,
          },
        },
      )
      .lean()
      .exec();
  };

  dbAutobotSetFillPortfolioAfterSell = async (
    args: GQL.IFillPortfolioAfterSellMutationOnMutationArguments,
    userId: string,
  ) => {
    const { value, botId } = args;

    await this.autoBotModel
      .findOneAndUpdate(
        {
          userId: userId,
          _id: botId,
        },
        {
          $set: {
            fillPortfolioAfterSell: value,
          },
        },
      )
      .lean()
      .exec();
  };

  dbAutobotSetBtcAoCross = async (
    args: GQL.IToggleBtcAoCrossMutationOnMutationArguments,
    userId: string,
  ) => {
    const { value, botId } = args;

    await this.autoBotModel
      .findOneAndUpdate(
        {
          userId: userId,
          _id: botId,
        },
        {
          $set: {
            useBtcAOCross: value,
          },
        },
      )
      .lean()
      .exec();
  };

  dbAutobotToggleTakeProfit = async (
    args: GQL.IToggleTakeProfitMutationOnMutationArguments,
    userId: string,
  ) => {
    const { value, botId } = args;

    await this.autoBotModel
      .findOneAndUpdate(
        {
          userId: userId,
          _id: botId,
        },
        {
          $set: {
            enableTakeProfit: value,
          },
        },
      )
      .lean()
      .exec();
  };

  dbAutobotToggleStoploss = async (
    args: GQL.IToggleStopLossMutationOnMutationArguments,
    userId: string,
  ) => {
    const { value, botId } = args;

    await this.autoBotModel
      .findOneAndUpdate(
        {
          userId: userId,
          _id: botId,
        },
        {
          $set: {
            enableStoploss: value,
          },
        },
      )
      .lean()
      .exec();
  };

  dbAutobotSetTakeProfit = async (
    args: GQL.ISetAutobotTakeProfitMutationOnMutationArguments,
    userId: string,
  ) => {
    const { value, botId } = args;

    await this.autoBotModel
      .findOneAndUpdate(
        {
          userId: userId,
          _id: botId,
        },
        {
          $set: {
            takeProfitPercent: value,
          },
        },
      )
      .lean()
      .exec();
  };

  dbAutobotSetStoploss = async (
    args: GQL.ISetAutobotStopLossMutationOnMutationArguments,
    userId: string,
  ) => {
    const { value, botId } = args;

    await this.autoBotModel
      .findOneAndUpdate(
        {
          userId: userId,
          _id: botId,
        },
        {
          $set: {
            stoplossPercent: value,
          },
        },
      )
      .lean()
      .exec();
  };

  dbAutobotSetOnlyFromWhitelist = async (
    args: GQL.IOnlyFromWhitelistMutationOnMutationArguments,
    userId: string,
  ) => {
    const { value, botId } = args;

    await this.autoBotModel
      .findOneAndUpdate(
        {
          userId: userId,
          _id: botId,
        },
        {
          $set: {
            buyOnlyFromWhitelist: value,
          },
        },
      )
      .lean()
      .exec();
  };

  dbAutobotSetOnlyBtcMode = async (
    args: GQL.IBtcOnlyMutationOnMutationArguments,
    userId: string,
  ) => {
    const { value, botId } = args;

    await this.autoBotModel
      .findOneAndUpdate(
        {
          userId: userId,
          _id: botId,
        },
        {
          $set: {
            btcOnly: value,
          },
        },
      )
      .lean()
      .exec();
  };
}
