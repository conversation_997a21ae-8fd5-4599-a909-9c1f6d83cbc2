import { HydratedDocument } from 'mongoose';
import { <PERSON>p, <PERSON>hem<PERSON>, SchemaFactory } from '@nestjs/mongoose';

export type DbStrategyAllTestDocument = HydratedDocument<DbStrategyAllTest>;

@Schema({ collection: 'strategyalltesters' })
export class DbStrategyAllTest {
  @Prop()
  strategy: string;
  @Prop()
  interval: string;
  @Prop()
  userId: string;
  @Prop()
  profit: number;
  @Prop()
  profitUsd: number;
  @Prop({ type: Date })
  startDate: any;
  @Prop({ type: Date })
  endDate: any;
  @Prop()
  status: string;
  @Prop()
  progressStrategy: number;
  @Prop()
  progressInterval: number;
  @Prop()
  progressSymbol: number;
  @Prop()
  totalProgressSymbol: number;
  @Prop()
  currentSymbol: string;

  @Prop()
  numTrades: number;
  @Prop()
  posTrades: number;
  @Prop()
  negTrades: number;
  @Prop()
  investPerCoin: number;

  @Prop()
  bestPerformer: string;
  @Prop()
  worstPerformer: string;

  @Prop()
  bestSymbolProfit: number;
  @Prop()
  worstSymbolProfit: number;
  @Prop()
  avgProfit: number;
  @Prop()
  avgHoldMinutes: number;
  @Prop()
  avgTradesDay: number;
  @Prop()
  bestTrade: number;
  @Prop()
  avgTrade: number;
  @Prop()
  worstTrade: number;
  @Prop({ type: Date })
  createTimestamp: any;
  @Prop({ type: Date })
  updateTimestamp: any;
  @Prop({ type: [String] })
  symbols: any;
  @Prop()
  sl: number;

  @Prop()
  exchange: string;
}

export const DbStrategyAllTestSchema =
  SchemaFactory.createForClass(DbStrategyAllTest);
DbStrategyAllTestSchema.index({ userId: 1 });
DbStrategyAllTestSchema.index({ createTimestamp: 1 });
