import { Modu<PERSON> } from '@nestjs/common';
import { DbExcludedSymbolsService } from './db-excluded-symbols.service';
import { MongooseModule } from '@nestjs/mongoose';

import { DbLogModule } from '@app/database/db-log/db-log.module';
import {
  DbExcludedSymbols,
  DbExcludedSymbolsSchema,
} from '@app/database/db-excluded-symbols/db-excluded-symbols.model';

@Module({
  providers: [DbExcludedSymbolsService],
  exports: [
    DbExcludedSymbolsService,
    MongooseModule.forFeature([
      {
        name: DbExcludedSymbols.name,
        schema: DbExcludedSymbolsSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    MongooseModule.forFeature([
      {
        name: DbExcludedSymbols.name,
        schema: DbExcludedSymbolsSchema,
      },
    ]),
  ],
})
export class DbExcludedSymbolsModule {}
