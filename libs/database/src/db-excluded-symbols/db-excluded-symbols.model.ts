import { HydratedDocument } from 'mongoose';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbExcludedSymbolsDocument = HydratedDocument<DbExcludedSymbols>;

@Schema({ collection: 'excludedsymbols' })
export class DbExcludedSymbols {
  @Prop()
  symbol: string;

  @Prop()
  exchange: string;
}

export const DbExcludedSymbolsSchema =
  SchemaFactory.createForClass(DbExcludedSymbols);
DbExcludedSymbolsSchema.index({ symbol: 1 }, { unique: true });
