import { HydratedDocument } from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbCandleStickDocument = HydratedDocument<DbCandleStick>;

@Schema({ collection: 'candlesticks' })
export class DbCandleStick {
  @Prop()
  symbol: string;

  @Prop()
  interval: string;

  @Prop({ type: Date })
  timestamp: any;

  @Prop()
  openTime: number;

  @Prop()
  open: number;

  @Prop()
  high: number;

  @Prop()
  low: number;

  @Prop()
  close: number;

  @Prop()
  volume: number;

  @Prop()
  closeTime: number;

  @Prop()
  quoteVolume: number;

  @Prop()
  trades: number;

  @Prop()
  baseAssetVolume: number;

  @Prop()
  quoteAssetVolume: number;

  @Prop()
  exchange: string;
}

export const DbCandleStickSchema = SchemaFactory.createForClass(DbCandleStick);
DbCandleStickSchema.index({ symbol: 1, interval: 1, exchange: 1 });
DbCandleStickSchema.index(
  { symbol: 1, interval: 1, openTime: 1, exchange: 1 },
  { unique: true },
);
