import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { prop, sortBy } from 'ramda';
import {
  DbCandleStick,
  DbCandleStickDocument,
} from '@app/database/db-candlesticks/db-candlesticks.model';

@Injectable()
export class DbCandlesticksService {
  constructor(
    @InjectModel(DbCandleStick.name)
    private candlesModel: Model<DbCandleStickDocument>,
  ) {}

  async updateLastCandleValue(
    updatedLastCandles: any[],
    symbol: string,
    interval: string,
    exchange: string,
  ) {
    if (updatedLastCandles && updatedLastCandles.length > 0) {
      const updatedCandle = updatedLastCandles[0];
      await this.candlesModel
        .findOneAndUpdate(
          {
            symbol,
            interval,
            exchange,
            openTime: updatedCandle.openTime,
          },
          {
            $set: {
              ...updatedCandle,
              exchange,
            },
          },
          {
            upsert: false,
            new: false,
          },
        )
        .lean()
        .exec();
    }
  }

  async updateCandle(symbol: string, candle: any, exchange: string) {
    return this.candlesModel
      .findOneAndUpdate(
        {
          symbol,
          interval: '1',
          exchange,
        },
        {
          $set: {
            ...candle,
            timestamp: new Date(),
            symbol,
            interval: '1',
            exchange,
          },
        },
        {
          upsert: true,
          new: true,
        },
      )
      .lean()
      .exec();
  }

  async dbGetLastCandle(symbol: string, interval: string, exchange: string) {
    return await this.candlesModel
      .findOne({
        symbol,
        interval,
        exchange,
      })
      .sort({
        openTime: -1,
      })
      .lean()
      .exec();
  }

  async dbGetLastCandles(
    symbol: string,
    interval: string,
    exchange: string,
    size: number,
  ) {
    const result = await this.candlesModel
      .find({
        symbol,
        interval,
        exchange,
      })
      .sort({
        openTime: -1,
      })
      .limit(size)
      .lean()
      .exec();

    return sortBy(prop('openTime'))(result);
  }

  async dbCountCandles(
    symbol: string,
    interval: string,
    exchange: string,
  ): Promise<number> {
    return this.candlesModel
      .countDocuments({
        symbol,
        interval,
        exchange,
      })
      .lean()
      .exec();
  }

  async dbGetCandles(
    symbol: string,
    interval: string,
    exchange: string,
  ): Promise<DbCandleStickDocument[]> {
    return this.candlesModel
      .find({
        symbol,
        interval,
        exchange,
      })
      .sort({
        openTime: 1,
      })
      .lean()
      .exec();
  }

  async dbGetSingleCandleBefore(
    symbol: string,
    interval: string,
    openTime: number,
    exchange: string,
  ) {
    const result = await this.candlesModel
      .find({
        symbol,
        interval,
        openTime: { $lt: openTime },
        exchange,
      })
      .sort({
        openTime: -1,
      })
      .limit(1)
      .lean()
      .exec();

    if (result) {
      return result[0];
    }

    return null;
  }

  async dbGetCandlesAfter(
    symbol: string,
    interval: string,
    openTime: number | Date,
    exchange: string,
  ) {
    return this.candlesModel
      .find({
        symbol,
        interval,
        openTime: { $gt: openTime },
        exchange,
      })
      .sort({ openTime: 1 })
      .lean()
      .exec();
  }

  async dbCandlesInsertMany(candlesToInsert: DbCandleStick[]) {
    return this.candlesModel.insertMany(candlesToInsert);
  }

  async dbGetCandlesBetween(
    symbol: string,
    interval: string,
    startDate: Date,
    endDate: Date,
    exchange: string,
    limit?,
  ) {
    const search = {
      symbol,
      interval,
      openTime: { $gte: startDate, $lt: endDate },
      exchange,
    };

    if (limit) {
      return await this.candlesModel
        .find(search)
        .sort({
          openTime: 1,
        })
        .limit(limit)
        .lean()
        .exec();
    } else {
      return await this.candlesModel
        .find(search)
        .sort({
          openTime: 1,
        })
        .lean()
        .exec();
    }
  }
}
