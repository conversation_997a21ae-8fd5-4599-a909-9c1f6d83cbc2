import { Module } from '@nestjs/common';
import { DbCandlesticksService } from './db-candlesticks.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  DbCandleStick,
  DbCandleStickSchema,
} from '@app/database/db-candlesticks/db-candlesticks.model';

@Module({
  providers: [DbCandlesticksService],
  exports: [
    DbCandlesticksService,
    MongooseModule.forFeature([
      {
        name: DbCandleStick.name,
        schema: DbCandleStickSchema,
      },
    ]),
  ],
  imports: [
    MongooseModule.forFeature([
      {
        name: DbCandleStick.name,
        schema: DbCandleStickSchema,
      },
    ]),
  ],
})
export class DbCandlesticksModule {}
