import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { last } from 'ramda';
import { DbMACD, DbMACDDocument } from '@app/database/db-macd/db-macd.model';

@Injectable()
export class DbMacdService {
  constructor(
    @InjectModel(DbMACD.name) private macdModel: Model<DbMACDDocument>,
    private loggerService: DbLogService,
  ) {}

  public async getMACD(
    args: GQL.IMacdsOnIndicatorsQueryArguments,
    exchange: string,
  ) {
    const { interval } = args;

    try {
      return this.macdModel
        .findOne({
          symbol: args.symbol,
          interval: interval || '1d',
          exchange,
        })
        .lean()
        .exec();
    } catch (e) {
      this.loggerService.errorServer(e);
    }

    return null;
  }

  public async findOne(symbol: string, interval: string, exchange: string) {
    return this.macdModel.findOne({
      symbol,
      interval,
      exchange,
    });
  }

  async updateMACD(
    symbol: string,
    exchange: string,
    interval: string,
    aoValues: {
      aoValue: number;
      openTime: number;
      coinPriceUsd: number;
    }[],
    prevEntry: any,
  ) {
    const values = aoValues.map((x) => {
      return {
        signal: x.aoValue,
        openTime: x.openTime,
        coinPriceUsd: x.coinPriceUsd,
      };
    });

    const signal = last(aoValues)?.aoValue;
    const openTime = last(aoValues)?.openTime;
    const coinPriceUsd = last(aoValues)?.coinPriceUsd;

    const prev = prevEntry
      ? {
          symbol,
          interval,
          values: prevEntry.values,
          timestamp: prevEntry.timestamp,
        }
      : null;

    return this.macdModel
      .findOneAndUpdate(
        {
          symbol,
          interval,
          exchange,
        },
        {
          symbol,
          interval,
          values,
          signal,
          openTime,
          coinPriceUsd,
          timestamp: new Date(),
          prev,
        },
        { upsert: true, new: true },
      )
      .lean()
      .exec();
  }
}
