import { HydratedDocument } from 'mongoose';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbMACDDocument = HydratedDocument<DbMACD>;

@Schema({ collection: 'macds' })
export class DbMACD {
  @Prop()
  symbol: string;

  @Prop()
  interval: string;

  @Prop()
  values: [
    {
      MACD: number;
      signal: number;
      histogram: number;
      openTime: number;
      coinPriceUsd: number;
    },
  ];

  @Prop({ type: Date })
  timestamp: any;

  @Prop()
  signal: number;

  @Prop()
  openTime: number;

  @Prop()
  coinPriceUsd: number;

  @Prop()
  exchange: string;
}

export const DbMACDSchema = SchemaFactory.createForClass(DbMACD);
DbMACDSchema.index({ symbol: 1, interval: 1, exchange: 1 });
