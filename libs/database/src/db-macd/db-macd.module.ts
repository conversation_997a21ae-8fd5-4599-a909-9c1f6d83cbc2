import { Module } from '@nestjs/common';
import { DbMacdService } from './db-macd.service';
import { MongooseModule } from '@nestjs/mongoose';
import { DbLogModule } from '@app/database/db-log/db-log.module';
import { DbMACD, DbMACDSchema } from '@app/database/db-macd/db-macd.model';

@Module({
  providers: [DbMacdService],
  exports: [
    DbMacdService,
    MongooseModule.forFeature([
      {
        name: DbMACD.name,
        schema: DbMACDSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    MongooseModule.forFeature([
      {
        name: DbMACD.name,
        schema: DbMACDSchema,
      },
    ]),
  ],
})
export class DbMacdModule {}
