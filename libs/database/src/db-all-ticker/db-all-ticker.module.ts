import { Module } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { DbLogModule } from '@app/database/db-log/db-log.module';
import {
  DbAllTicker,
  DbAllTickerSchema,
} from '@app/database/db-all-ticker/db-all-ticker.model';
import { DbAllTickerService } from '@app/database/db-all-ticker/db-all-ticker.service';
import { DbCoinPriceModule } from '@app/database/db-coin-price/db-coin-price.module';
import { DbCandlesticksModule } from '@app/database/db-candlesticks/db-candlesticks.module';

@Module({
  providers: [DbAllTickerService],
  exports: [
    DbAllTickerService,
    MongooseModule.forFeature([
      {
        name: DbAllTicker.name,
        schema: DbAllTickerSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    DbCoinPriceModule,
    DbCandlesticksModule,
    MongooseModule.forFeature([
      {
        name: DbAllTicker.name,
        schema: DbAllTickerSchema,
      },
    ]),
  ],
})
export class DbAllTickerModule {}
