import { Injectable } from '@nestjs/common';
import { Ticker } from 'binance-api-node';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import {
  DbAllTicker,
  DbAllTickerDocument,
} from '@app/database/db-all-ticker/db-all-ticker.model';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import { DbCandlesticksService } from '@app/database/db-candlesticks/db-candlesticks.service';
import PriceCalculation from '../../../../utils/PriceCalculation';
import { path } from 'ramda';
import { addHour } from '@formkit/tempo';

@Injectable()
export class DbAllTickerService {
  constructor(
    @InjectModel(DbAllTicker.name)
    private allTickerModel: Model<DbAllTickerDocument>,
    private logger: DbLogService,
    private coinPriceService: DbCoinPriceService,
    private candlesService: DbCandlesticksService,
  ) {}

  public async calcPricePercents(tickerData: Ticker, exchange: string) {
    const min240 = addHour(new Date(), -4).getTime();
    const currentCoinPrice = await this.coinPriceService.dbGetCoinPrice(
      tickerData.symbol,
      exchange,
    );
    const result240Candle = await this.candlesService.dbGetSingleCandleBefore(
      tickerData.symbol,
      '15',
      min240,
      exchange,
    );

    return result240Candle
      ? PriceCalculation.toPercentGain(result240Candle.close, currentCoinPrice)
      : 0;
  }

  private calcDate = (min) => {
    return new Date().getTime() - min * 60 * 1000;
  };

  private getVol = (dbResult) => {
    return dbResult ? path(['data', 'volumeQuote'])(dbResult) : null;
  };

  private findTimeTicker = async (
    symbol: string,
    time: number,
    exchange: string,
  ) => {
    return await this.allTickerModel
      .findOne({
        symbol,
        timestamp: { $lt: new Date(time) },
        exchange,
      })
      .sort({ timestamp: -1 })
      .lean()
      .exec();
  };

  toPercent = (now, prev) => {
    return ((now / prev - 1) * 100).toFixed(2);
  };

  public async calcVolumeChange(ticker: Ticker & any, exchange: string) {
    const symbol = ticker.symbol;

    const result4h = await this.findTimeTicker(
      symbol,
      this.calcDate(60 * 4),
      exchange,
    );
    const result24h = await this.findTimeTicker(
      symbol,
      this.calcDate(60 * 24),
      exchange,
    );

    const prev3: any = this.getVol(result4h);
    const prev4: any = this.getVol(result24h);

    const percentVolume4h = prev3
      ? Number(
          Number(
            PriceCalculation.toPercentGain(ticker.volumeQuote, prev3),
          ).toFixed(1),
        )
      : 0;
    const percentVolume24h = prev4
      ? Number(
          Number(
            PriceCalculation.toPercentGain(ticker.volumeQuote, prev4),
          ).toFixed(1),
        )
      : 0;

    return {
      ...ticker,
      percentVolume4h,
      percentVolume24h,
    };
  }

  public async saveTickerData(
    symbol: string,
    tickerData: Ticker,
    exchange: string,
  ) {
    try {
      return new this.allTickerModel({
        symbol,
        data: tickerData,
        timestamp: new Date(),
        exchange,
      }).save();
    } catch (e) {
      this.logger.errorServer(e);
    }

    return null;
  }

  public async getTickerData(symbol: string, exchange: string) {
    return this.allTickerModel
      .findOne({
        symbol,
        exchange,
      })
      .sort({
        timestamp: -1,
      })
      .lean()
      .exec();
  }
}
