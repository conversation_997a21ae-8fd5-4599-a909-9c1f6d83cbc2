import { Module } from '@nestjs/common';
import { DbBotProfitsService } from './db-bot-profits.service';
import { MongooseModule } from '@nestjs/mongoose';
import { DbLogModule } from '@app/database/db-log/db-log.module';
import {
  DbBotProfits,
  DbBotProfitsSchema,
} from '@app/database/db-bot-profits/db-bot-profits.model';

@Module({
  providers: [DbBotProfitsService],
  exports: [
    DbBotProfitsService,
    MongooseModule.forFeature([
      {
        name: DbBotProfits.name,
        schema: DbBotProfitsSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    MongooseModule.forFeature([
      {
        name: DbBotProfits.name,
        schema: DbBotProfitsSchema,
      },
    ]),
  ],
})
export class DbBotProfitsModule {}
