import { Module } from '@nestjs/common';
import { DbCommentService } from '@app/database/db-comment/db-comment.service';
import { MongooseModule } from '@nestjs/mongoose';
import { DbLogModule } from '@app/database/db-log/db-log.module';
import {
  DbComment,
  DbCommentSchema,
} from '@app/database/db-comment/db-comment.model';

@Module({
  providers: [DbCommentService],
  exports: [
    DbCommentService,
    MongooseModule.forFeature([
      {
        name: DbComment.name,
        schema: DbCommentSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    MongooseModule.forFeature([
      {
        name: DbComment.name,
        schema: DbCommentSchema,
      },
    ]),
  ],
})
export class DbCommentModule {}
