import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import { Model } from 'mongoose';
import { DbLogService } from '@app/database/db-log/db-log.service';
import {
  DbComment,
  DbCommentDocument,
} from '@app/database/db-comment/db-comment.model';

@Injectable()
export class DbCommentService {
  constructor(
    @InjectModel(DbComment.name) private commentModel: Model<DbCommentDocument>,
    private loggerService: DbLogService,
  ) {}

  saveComment(data: any, userId: string) {
    const { symbol, comment } = data;
    try {
      return this.commentModel
        .findOneAndUpdate(
          {
            symbol,
            user: userId,
          },
          {
            symbol,
            user: userId,
            comment,
          },
          { upsert: true, new: true },
        )
        .lean()
        .exec();
    } catch (e) {
      this.loggerService.errorServer(e);
    }

    return null;
  }

  getComment(args: { symbol: string }, userId: string) {
    const { symbol } = args;

    return this.commentModel
      .findOne({
        symbol,
        user: userId,
      })
      .lean()
      .exec();
  }
}
