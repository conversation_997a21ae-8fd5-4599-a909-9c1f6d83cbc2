import { HydratedDocument } from 'mongoose';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbCommentDocument = HydratedDocument<DbComment>;

@Schema({ collection: 'comments' })
export class DbComment {
  @Prop()
  symbol: string;

  @Prop()
  user: string;

  @Prop()
  comment: string;

  @Prop({ type: Date })
  timestamp: any;
}

export const DbCommentSchema = SchemaFactory.createForClass(DbComment);
DbCommentSchema.index({ symbol: 1, user: 1 });
