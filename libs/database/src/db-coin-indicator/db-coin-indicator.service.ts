import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import { Model } from 'mongoose';
import {
  DbCoinIndicator,
  DbCoinIndicatorDocument,
} from '@app/database/db-coin-indicator/db-coin-indicator.model';

@Injectable()
export class DbCoinIndicatorService {
  constructor(
    @InjectModel(DbCoinIndicator.name)
    private coinIndicatorDocumentModel: Model<DbCoinIndicatorDocument>,
  ) {}

  public async getAllIndicators(
    args: GQL.IFetchOnInstrumentsQueryArguments,
    exchange: string,
  ) {
    const query: any = {
      exchange,
    };

    if (args?.filters?.interval) {
      query.interval = args.filters.interval;
    }

    if (args?.filters?.ao) {
      if (args?.filters?.ao === 'over') {
        query.ao = { $gt: 0 };
      }
    }

    if (args?.filters?.rsi) {
      if (args?.filters?.rsi === 'range') {
        query.rsi = { $gt: 30, $lt: 80 };
      }
    }

    if (args?.filters?.nesterov) {
      if (args?.filters?.nesterov === 'long') {
        query.nesterov = 'long';
      }
    }

    return this.coinIndicatorDocumentModel.find(query).lean().exec();
  }

  public async getIndicatorsForSymbol(
    symbol: string,
    interval: string,
    exchange: string,
  ) {
    return this.coinIndicatorDocumentModel
      .findOne({
        symbol,
        interval,
        exchange,
      })
      .lean()
      .exec();
  }

  public async update(
    symbol: string,
    interval: string,
    value: any,
    exchange: string,
  ) {
    await this.coinIndicatorDocumentModel
      .findOneAndUpdate(
        {
          symbol,
          interval,
          exchange,
        },
        { $set: { ...value, updateTimestamp: new Date() } },
        { upsert: true, new: true },
      )
      .lean()
      .exec();
  }
}
