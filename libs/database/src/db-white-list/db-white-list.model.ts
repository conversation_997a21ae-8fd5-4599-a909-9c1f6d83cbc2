import { HydratedDocument } from 'mongoose';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbWhiteListDocument = HydratedDocument<DbWhiteList>;

@Schema({ collection: 'whitelists' })
export class DbWhiteList {
  @Prop()
  userId: string;

  @Prop()
  symbol: string;

  @Prop({ type: Date })
  timestamp: any;

  @Prop()
  autoAdded: boolean;

  @Prop()
  disabled: boolean;

  @Prop()
  exchange: string;
}

export const DbWhiteListSchema = SchemaFactory.createForClass(DbWhiteList);
DbWhiteListSchema.index(
  { userId: 1, symbol: 1, exchange: 1 },
  { unique: true },
);
