import { Module } from '@nestjs/common';
import { DbKeyService } from './db-key.service';
import { DbLogModule } from '@app/database/db-log/db-log.module';
import { MongooseModule } from '@nestjs/mongoose';
import { <PERSON>b<PERSON><PERSON>, DbKeySchema } from '@app/database/db-key/db-key.model';

@Module({
  providers: [DbKeyService],
  exports: [
    DbKeyService,
    MongooseModule.forFeature([
      {
        name: DbKey.name,
        schema: DbKeySchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    MongooseModule.forFeature([
      {
        name: DbKey.name,
        schema: DbKeySchema,
      },
    ]),
  ],
})
export class DbKeyModule {}
