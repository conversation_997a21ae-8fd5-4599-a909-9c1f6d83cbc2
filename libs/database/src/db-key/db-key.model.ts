import { HydratedDocument } from 'mongoose';
import { <PERSON><PERSON>, <PERSON>hema, SchemaFactory } from '@nestjs/mongoose';

export type DbKeyDocument = HydratedDocument<DbKey>;

@Schema({ collection: 'keys' })
export class DbKey {
  @Prop()
  user: string;

  @Prop()
  key: string;

  @Prop()
  secret: string;

  @Prop()
  exchange: string;

  @Prop()
  activated: boolean;
}

export const DbKeySchema = SchemaFactory.createForClass(DbKey);
DbKeySchema.index({ user: 1 });
