import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { Db<PERSON><PERSON>, DbKeyDocument } from '@app/database/db-key/db-key.model';

@Injectable()
export class DbKeyService {
  constructor(
    @InjectModel(DbKey.name) private keyModel: Model<DbKeyDocument>,
    private logger: DbLogService,
  ) {}

  public async getApiKey(userId: string, exchange: string) {
    try {
      return this.keyModel
        .findOne({
          user: userId,
          exchange,
        })
        .lean()
        .exec();
    } catch (e) {
      this.logger.errorServer(e);
    }
  }

  public async changeBinanceApiKey(
    userId: string,
    key: string,
    secret: string,
    exchange: string,
  ) {
    try {
      return this.keyModel
        .findOneAndUpdate(
          { user: userId, exchange },
          {
            user: userId,
            key,
            secret,
            exchange,
          },
          { upsert: true, new: true },
        )
        .exec();
    } catch (e) {
      this.logger.errorServer(e);
    }
  }

  public async activateKey(userId: string, exchange: string) {
    try {
      await this.keyModel.findOneAndUpdate(
        {
          user: userId,
        },
        {
          $set: {
            activated: false,
          },
        },
      );

      await this.keyModel
        .findOneAndUpdate(
          { user: userId, exchange },
          {
            $set: {
              activated: true,
            },
          },
          { upsert: true, new: true },
        )
        .exec();
    } catch (e) {
      this.logger.errorServer(e);
    }
  }
}
