import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { DbLogService } from '@app/database/db-log/db-log.service';
import {
  DbSaldoUsd,
  DbSaldoUsdDocument,
} from '@app/database/db-saldo-usd/db-saldo-usd.model';
import { MbAccountInfo } from '@app/binance-api/binance-account-info/binance-account-info.service';

@Injectable()
export class DbSaldoUsdService {
  constructor(
    @InjectModel(DbSaldoUsd.name)
    private saldoUsdModel: Model<DbSaldoUsdDocument>,
    private logger: DbLogService,
  ) {}

  async saveUserSaldoUsd(
    userId: string,
    accountInfo: MbAccountInfo,
    exchange: string,
  ) {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const date = currentDate.getDate();
    const dateUTC = new Date(Date.UTC(year, month, date));
    const dateUTCPrev = new Date(Date.UTC(year, month, date - 1));

    const prev = await this.saldoUsdModel
      .findOne({
        user: userId,
        timestamp: dateUTCPrev,
        exchange,
      })
      .lean()
      .exec();

    const prevDiff = prev
      ? Number(accountInfo.usdValue - prev.usd).toFixed(2)
      : 0;

    try {
      await this.saldoUsdModel
        .findOneAndUpdate(
          {
            user: userId,
            timestamp: dateUTC,
            exchange,
          },
          {
            $set: {
              user: userId,
              btc: accountInfo.btcValue ? accountInfo.btcValue : 0,
              usd: accountInfo.usdValue ? accountInfo.usdValue : 0,
              usdDiff: prevDiff,
              timestamp: dateUTC,
              exchange,
            },
          },
          {
            upsert: true,
            new: true,
          },
        )
        .exec();
    } catch (e) {
      this.logger.errorServer(e);
    }

    return null;
  }

  public async getUserSaldoUsd(userId: string, args: any, exchange: string) {
    try {
      const saldos = await this.saldoUsdModel
        .find({
          user: userId,
          exchange,
        })
        .sort({
          timestamp: -1,
        })
        .limit(args.limit || 31)
        .lean()
        .exec();

      return {
        saldos,
      };
    } catch (e) {
      return {};
    }
  }
}
