import { HydratedDocument } from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbSaldoUsdDocument = HydratedDocument<DbSaldoUsd>;

@Schema({ collection: 'saldousds' })
export class DbSaldoUsd {
  @Prop()
  user: string;

  @Prop()
  btc: number;

  @Prop()
  usd: number;

  @Prop()
  usdDiff: number;

  @Prop({ type: Date })
  timestamp: any;

  @Prop()
  exchange: string;
}

export const DbSaldoUsdSchema = SchemaFactory.createForClass(DbSaldoUsd);
DbSaldoUsdSchema.index({ user: 1, exchange: 1 });
DbSaldoUsdSchema.index({ user: 1, timestamp: 1, exchange: 1 });
