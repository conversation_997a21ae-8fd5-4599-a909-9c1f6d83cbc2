import { HydratedDocument } from 'mongoose';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbFearAltIndexDocument = HydratedDocument<DbFearAltIndex>;

@Schema({ collection: 'fearaltindex' })
export class DbFearAltIndex {
  @Prop()
  fearIndex: number;

  @Prop()
  fearText: string;

  @Prop()
  altIndex: number;

  @Prop()
  altText: string;

  @Prop({ type: Date })
  timestamp: any;
}

export const DbFearAltIndexSchema =
  SchemaFactory.createForClass(DbFearAltIndex);
