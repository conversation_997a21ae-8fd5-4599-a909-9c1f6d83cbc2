import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import {
  DbFearAltIndex,
  DbFearAltIndexDocument,
} from '@app/database/db-fear-alt-index/db-fear-alt-index.model';
import { Model } from 'mongoose';

@Injectable()
export class DbFearAltIndexService {
  constructor(
    @InjectModel(DbFearAltIndex.name)
    private fearAltIndexModel: Model<DbFearAltIndexDocument>,
  ) {}

  public async dbSaveFearIndex(fearIndex: any, altIndex: any) {
    await new this.fearAltIndexModel({
      fearIndex: Number(fearIndex?.value),
      fearText: fearIndex?.value_classification,
      altIndex: Number(altIndex?.index),
      altText: altIndex?.text,
      timestamp: new Date(),
    }).save();
  }

  public async dbGetFearAltIndex() {
    return this.fearAltIndexModel
      .findOne()
      .sort({ timestamp: -1 })
      .lean()
      .exec();
  }
}
