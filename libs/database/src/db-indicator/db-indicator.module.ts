import { Module } from '@nestjs/common';
import { DbIndicatorService } from '@app/database/db-indicator/db-indicator.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  DbIndicator,
  DbIndicatorSchema,
} from '@app/database/db-indicator/db-indicator.model';

@Module({
  providers: [DbIndicatorService],
  exports: [
    DbIndicatorService,
    MongooseModule.forFeature([
      {
        name: DbIndicator.name,
        schema: DbIndicatorSchema,
      },
    ]),
  ],
  imports: [
    MongooseModule.forFeature([
      {
        name: DbIndicator.name,
        schema: DbIndicatorSchema,
      },
    ]),
  ],
})
export class DbIndicatorModule {}
