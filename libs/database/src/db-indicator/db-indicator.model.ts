import { HydratedDocument } from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbIndicatorDocument = HydratedDocument<DbIndicator>;

@Schema({ collection: 'indicators' })
export class DbIndicator {
  @Prop()
  symbol: string;

  @Prop({ type: Date })
  timestamp: any;

  @Prop({ type: Date })
  candleTime: any;

  @Prop()
  interval: string;

  @Prop()
  indicator: string;

  @Prop()
  indicatorValue?: number;

  @Prop()
  action: string;

  @Prop()
  price: number;

  @Prop()
  profit?: number;

  @Prop()
  newAdded?: boolean;

  @Prop()
  exchange: string;
}

export const DbIndicatorSchema = SchemaFactory.createForClass(DbIndicator);
DbIndicatorSchema.index({ candleTime: 1 });
DbIndicatorSchema.index({ newAdded: 1 });
DbIndicatorSchema.index({
  indicator: 1,
  interval: 1,
  candleTime: 1,
  exchange: 1,
});
DbIndicatorSchema.index({ indicator: 1, interval: 1, exchange: 1 });
DbIndicatorSchema.index({ symbol: 1, candleTime: 1, exchange: 1 });
DbIndicatorSchema.index({
  symbol: 1,
  candleTime: 1,
  exchange: 1,
  indicator: 1,
});
DbIndicatorSchema.index({ action: 1, profit: 1 });
DbIndicatorSchema.index({ exchange: 1 });
DbIndicatorSchema.index({ symbol: 1, exchange: 1 });
DbIndicatorSchema.index({ symbol: 1, interval: 1, indicator: 1, exchange: 1 });
DbIndicatorSchema.index({
  symbol: 1,
  interval: 1,
  indicator: 1,
  exchange: 1,
  action: 1,
});
