import { HydratedDocument } from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbLogDocument = HydratedDocument<DbLog>;

@Schema({
  collection: 'loggers',
  capped: { size: 5024288000, max: 800000 },
})
export class DbLog {
  @Prop()
  msg: string;

  @Prop()
  service: string;

  @Prop()
  level: string;

  @Prop({ type: Date })
  timestamp: any;
}

export const DbLogSchema = SchemaFactory.createForClass(DbLog);
DbLogSchema.index({ timestamp: 1, level: 1, service: 1 });
DbLogSchema.index({ level: 1 });
DbLogSchema.index({ level: 1, service: 1 });
DbLogSchema.index({ timestamp: 1 });
