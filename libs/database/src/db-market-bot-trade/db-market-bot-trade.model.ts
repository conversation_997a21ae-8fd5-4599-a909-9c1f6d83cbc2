import { HydratedDocument } from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbMarketBotTradeDocument = HydratedDocument<DbMarketBotTrade>;

@Schema({ collection: 'activebottrades' })
export class DbMarketBotTrade {
  // Autobot link
  @Prop()
  autobotId: string;

  // Active Bot link
  @Prop()
  marketBotId: string;

  @Prop()
  asset: string;

  // long, short
  @Prop()
  position: string;

  // За сколько BTC было куплено/продано
  @Prop()
  price: number;

  // Солько USD стоила вся сумма при копупке / продаже
  @Prop()
  valueInUsd: number;

  // Профит в процентах
  @Prop()
  profit: number;

  // Профит в USD
  @Prop()
  profitUsd: number;

  // Цена BTC при трэйде
  @Prop()
  btcPrice: number;

  // Сколько коинов
  @Prop()
  amount: number;

  // Trade Timestamp
  @Prop({ type: Date })
  tradeDate: any;

  // Binance response
  @Prop({ type: Object })
  orderResponse: any;

  @Prop()
  exchange: string;
}

export const DbMarketBotTradeSchema =
  SchemaFactory.createForClass(DbMarketBotTrade);

DbMarketBotTradeSchema.index({ marketBotId: 1, exchange: 1 });
DbMarketBotTradeSchema.index({ marketBotId: 1, tradeDate: 1, exchange: 1 });
