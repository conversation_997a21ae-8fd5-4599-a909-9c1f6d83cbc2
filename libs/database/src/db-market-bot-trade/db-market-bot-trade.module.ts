import { Module } from '@nestjs/common';
import { DbMarketBotTradeService } from './db-market-bot-trade.service';
import { MongooseModule } from '@nestjs/mongoose';
import { DbLogModule } from '@app/database/db-log/db-log.module';
import {
  DbMarketBotTrade,
  DbMarketBotTradeSchema,
} from '@app/database/db-market-bot-trade/db-market-bot-trade.model';

@Module({
  providers: [DbMarketBotTradeService],
  exports: [
    DbMarketBotTradeService,
    MongooseModule.forFeature([
      {
        name: DbMarketBotTrade.name,
        schema: DbMarketBotTradeSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    MongooseModule.forFeature([
      {
        name: DbMarketBotTrade.name,
        schema: DbMarketBotTradeSchema,
      },
    ]),
  ],
})
export class DbMarketBotTradeModule {}
