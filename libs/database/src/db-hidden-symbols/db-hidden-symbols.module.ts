import { Modu<PERSON> } from '@nestjs/common';
import { DbHiddenSymbolsService } from './db-hidden-symbols.service';
import { MongooseModule } from '@nestjs/mongoose';
import { DbLogModule } from '@app/database/db-log/db-log.module';
import {
  DbHiddenSymbols,
  DbHiddenSymbolsSchema,
} from '@app/database/db-hidden-symbols/db-hidden-symbols.model';

@Module({
  providers: [DbHiddenSymbolsService],
  exports: [
    DbHiddenSymbolsService,
    MongooseModule.forFeature([
      {
        name: DbHiddenSymbols.name,
        schema: DbHiddenSymbolsSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    MongooseModule.forFeature([
      {
        name: DbHiddenSymbols.name,
        schema: DbHiddenSymbolsSchema,
      },
    ]),
  ],
})
export class DbHiddenSymbolsModule {}
