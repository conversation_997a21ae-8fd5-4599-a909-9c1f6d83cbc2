import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import { Model } from 'mongoose';
import {
  DbHiddenSymbols,
  DbHiddenSymbolsDocument,
} from '@app/database/db-hidden-symbols/db-hidden-symbols.model';

@Injectable()
export class DbHiddenSymbolsService {
  constructor(
    @InjectModel(DbHiddenSymbols.name)
    private hiddenSymbolsModel: Model<DbHiddenSymbolsDocument>,
  ) {}

  async dbGetHiddenSymbols() {
    const result = await this.hiddenSymbolsModel.find({}).lean().exec();

    return result.map((data) => data.symbol);
  }
}
