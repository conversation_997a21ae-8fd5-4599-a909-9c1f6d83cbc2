import { HydratedDocument } from 'mongoose';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbHiddenSymbolsDocument = HydratedDocument<DbHiddenSymbols>;

@Schema({ collection: 'hiddensymbols' })
export class DbHiddenSymbols {
  @Prop()
  symbol: string;
}

export const DbHiddenSymbolsSchema =
  SchemaFactory.createForClass(DbHiddenSymbols);
DbHiddenSymbolsSchema.index({ symbol: 1 }, { unique: true });
