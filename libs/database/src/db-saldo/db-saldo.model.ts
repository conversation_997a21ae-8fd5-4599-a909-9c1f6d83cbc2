import { HydratedDocument } from 'mongoose';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbSaldoDocument = HydratedDocument<DbSaldo>;

@Schema({ collection: 'saldos' })
export class DbSaldo {
  @Prop()
  user: string;

  @Prop()
  btc: number;

  @Prop()
  usd: number;

  @Prop({ type: Date })
  timestamp: any;

  @Prop()
  exchange: string;
}

export const DbSaldoSchema = SchemaFactory.createForClass(DbSaldo);
DbSaldoSchema.index({ user: 1, exchange: 1 });
DbSaldoSchema.index({ user: 1, timestamp: 1, exchange: 1 });
