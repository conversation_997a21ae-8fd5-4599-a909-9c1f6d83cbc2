import { Modu<PERSON> } from '@nestjs/common';
import { DbSaldoService } from './db-saldo.service';
import { MongooseModule } from '@nestjs/mongoose';
import { DbLogModule } from '@app/database/db-log/db-log.module';
import { Db<PERSON>aldo, DbSaldoSchema } from '@app/database/db-saldo/db-saldo.model';

@Module({
  providers: [DbSaldoService],
  exports: [
    DbSaldoService,
    MongooseModule.forFeature([
      {
        name: DbSaldo.name,
        schema: DbSaldoSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    MongooseModule.forFeature([
      {
        name: DbSaldo.name,
        schema: DbSaldoSchema,
      },
    ]),
  ],
})
export class DbSaldoModule {}
