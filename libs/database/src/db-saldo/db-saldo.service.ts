import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { DbLogService } from '@app/database/db-log/db-log.service';
import {
  Db<PERSON>aldo,
  DbSaldoDocument,
} from '@app/database/db-saldo/db-saldo.model';
import { MbAccountInfo } from '@app/binance-api/binance-account-info/binance-account-info.service';
import { Exchange } from '../../../../types/exchanges';

@Injectable()
export class DbSaldoService {
  constructor(
    @InjectModel(DbSaldo.name) private saldoModel: Model<DbSaldoDocument>,
    private readonly logger: DbLogService,
  ) {}

  async saveUserSaldo(
    userId: string,
    accountInfo: MbAccountInfo,
    exchange: string,
  ) {
    try {
      return await new this.saldoModel({
        user: userId,
        btc: accountInfo.btcValue,
        usd: accountInfo.usdValue,
        timestamp: new Date(),
        exchange,
      }).save();
    } catch (e) {
      this.logger.errorServer(e);
    }

    return null;
  }

  async getUserSaldo(
    userId: string,
    exchange: string,
    args?: GQL.ISaldoOnUserQueryArguments,
  ) {
    try {
      const until = new Date(
        new Date().getFullYear(),
        new Date().getMonth(),
        new Date().getDate(),
      );

      const saldos = await this.saldoModel
        .find({ exchange, user: userId })
        .sort({
          timestamp: -1,
        })
        .limit(args.limit || 999)
        .lean()
        .exec();

      const yesterday = await this.saldoModel
        .findOne({
          user: userId,
          exchange,
          timestamp: { $lte: until },
        })
        .sort({
          timestamp: -1,
        })
        .lean()
        .exec();

      return {
        saldos,
        yesterday,
      };
    } catch (e) {
      return {};
    }
  }
}
