import { Module } from '@nestjs/common';
import { DbUserService } from './db-user.service';
import { MongooseModule } from '@nestjs/mongoose';
import { DbLogModule } from '@app/database/db-log/db-log.module';
import { DbUser, DbUserSchema } from '@app/database/db-user/db-user.model';

@Module({
  providers: [DbUserService],
  exports: [
    DbUserService,
    MongooseModule.forFeature([
      {
        name: DbUser.name,
        schema: DbUserSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    MongooseModule.forFeature([
      {
        name: DbUser.name,
        schema: DbUserSchema,
      },
    ]),
  ],
})
export class DbUserModule {}
