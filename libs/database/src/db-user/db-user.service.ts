import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { path } from 'ramda';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbUser, DbUserDocument } from '@app/database/db-user/db-user.model';
import { Exchange } from '../../../../types/exchanges';

@Injectable()
export class DbUserService {
  constructor(
    @InjectModel(DbUser.name) private userModel: Model<DbUserDocument>,
    private readonly logger: DbLogService,
  ) {}

  async getAuthUser(userId: string) {
    return this.userModel.findOne({ _id: userId }).lean().exec();
  }

  async getAuthUserByUsername(username: string) {
    return this.userModel.findOne({ username }).lean().exec();
  }

  findUserById(id): Promise<any> {
    try {
      return this.userModel.findById(id).exec();
    } catch (e) {
      this.logger.errorServer(e);
    }

    return null;
  }

  dbSaveAccountSettings = async (userId: string, args) => {
    const { email, pumpbot, alwaysSetTP, alwaysSetStoploss } = args;

    const currentSettings = await this.userModel.findById(userId).lean().exec();

    const setObject: any = {
      notifications: {
        pumpbot,
      },
      ...currentSettings,
    };

    if (email) {
      setObject.email = email;
    }

    if (alwaysSetStoploss !== undefined) {
      if (!setObject.trade) {
        setObject.trade = {};
      }

      setObject.trade.alwaysSetStoploss = alwaysSetStoploss;
    }

    if (alwaysSetTP !== undefined) {
      if (!setObject.trade) {
        setObject.trade = {};
      }

      setObject.trade.alwaysSetTP = alwaysSetTP;
    }

    return this.userModel
      .findOneAndUpdate(
        {
          _id: userId,
        },
        {
          $set: setObject,
        },
        { new: true },
      )
      .lean()
      .exec();
  };

  dbSaveSplitMutation = async (userId: string, args: any) => {
    const { window, market, interval } = args;

    const user = await this.userModel
      .findOne({
        _id: userId,
      })
      .lean()
      .exec();

    const newSplitView = [
      {
        window,
        market,
        interval,
      },
    ];

    if (user.splitview) {
      for (const views of user.splitview) {
        if (views.window !== window) {
          newSplitView.push(views);
        }
      }
    }

    return this.userModel
      .findOneAndUpdate(
        {
          _id: userId,
        },
        {
          $set: {
            splitview: newSplitView,
          },
        },
        { new: true },
      )
      .lean()
      .exec();
  };

  dbSaveAccountSignalNotification = async (userId, args) => {
    const user = await this.getAuthUser(userId);
    const { strategy } = args;

    const signals: any = path(['notifications', 'signals'])(user) || {};
    signals[strategy] = !signals[strategy];

    user.notifications = { ...user.notifications, signals };

    return this.userModel
      .findOneAndUpdate(
        {
          _id: userId,
        },
        {
          $set: {
            notifications: user.notifications,
          },
        },
        { new: true },
      )
      .lean()
      .exec();
  };

  dbGetUserAccount = async (userId: string): Promise<any> => {
    try {
      return this.getAuthUser(userId);
    } catch (e) {
      throw e;
    }
  };

  async dbGetUsers() {
    return await this.userModel.find().lean().exec();
  }

  async findUserByMail(email: string) {
    return this.userModel.find({ email }).countDocuments();
  }

  async countUserByUsername(username: string) {
    return this.userModel.find({ username }).countDocuments();
  }

  async addUser(username: string, hashedPassword: string, email: string) {
    return new this.userModel({
      username,
      password: hashedPassword,
      email,
      confirmed: false,
    }).save();
  }

  public async confirmUserRegistration(userId: string) {
    return this.userModel
      .findByIdAndUpdate(userId, {
        $set: {
          confirmed: true,
        },
      })
      .lean()
      .exec();
  }

  public async saveExchange(userId: string, exchange: string) {
    return this.userModel
      .findByIdAndUpdate(userId, {
        $set: {
          exchange: exchange,
        },
      })
      .lean()
      .exec();
  }

  public async getUserExchange(userId: string) {
    const user = await this.userModel.findById(userId).lean().exec();

    return user.exchange || Exchange.BINANCE;
  }
}
