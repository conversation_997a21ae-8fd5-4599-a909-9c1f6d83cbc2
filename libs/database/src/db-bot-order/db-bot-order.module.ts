import { Module } from '@nestjs/common';
import { DbBotOrderService } from './db-bot-order.service';
import { MongooseModule } from '@nestjs/mongoose';
import { DbLogModule } from '@app/database/db-log/db-log.module';
import {
  DbBotOrder,
  DbBotOrderSchema,
} from '@app/database/db-bot-order/db-bot-order.model';

@Module({
  providers: [DbBotOrderService],
  exports: [
    DbBotOrderService,
    MongooseModule.forFeature([
      {
        name: DbBotOrder.name,
        schema: DbBotOrderSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    MongooseModule.forFeature([
      {
        name: DbBotOrder.name,
        schema: DbBotOrderSchema,
      },
    ]),
  ],
})
export class DbBotOrderModule {}
