import { HydratedDocument } from 'mongoose';
import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbBotOrderDocument = HydratedDocument<DbBotOrder>;

@Schema({ collection: 'botorders' })
export class DbBotOrder {
  @Prop()
  user: string;

  @Prop()
  symbol: string;

  @Prop()
  marketBotId: string;

  @Prop()
  autobotId: string;

  @Prop()
  type: string;

  @Prop()
  amount: number;

  @Prop()
  price: number;

  // Mark for Take Profit
  @Prop()
  markForTP: boolean;

  @Prop()
  tpPrice: number;

  @Prop({ type: Date })
  addTimestamp: any;

  @Prop({ type: Date })
  executionTimestamp: any;

  @Prop()
  status: string; // EXECUTED, NEW

  @Prop({ type: Object })
  response: any;

  @Prop()
  exchange: string;
}

export const DbBotOrderSchema = SchemaFactory.createForClass(DbBotOrder);
DbBotOrderSchema.index({ user: 1, symbol: 1 });
