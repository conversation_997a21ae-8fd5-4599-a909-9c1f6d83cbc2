import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import { Model, Types } from 'mongoose';
import {
  DbBotOrder,
  DbBotOrderDocument,
} from '@app/database/db-bot-order/db-bot-order.model';

@Injectable()
export class DbBotOrderService {
  constructor(
    @InjectModel(DbBotOrder.name)
    private botOrderModel: Model<DbBotOrderDocument>,
  ) {}

  async dbAddBotOrder(userId: string, exchange: string, args) {
    const { price, symbol, quantity: amount, type } = args;

    return new this.botOrderModel({
      price,
      symbol,
      amount,
      type,
      status: 'NEW',
      addTimestamp: new Date(),
      user: userId,
      exchange,
    }).save();
  }

  async dbGetBotOrders(
    userId: string,
    all = false,
    exchange?: string,
  ): Promise<DbBotOrderDocument[]> {
    if (all) {
      return this.botOrderModel.find().lean().exec();
    }

    const query: any = {
      user: userId,
    };

    if (exchange) {
      query.exchange = exchange;
    }

    return this.botOrderModel.find(query).lean().exec();
  }

  async dbDeleteBotOrder(userId: string, orderId: string) {
    await this.botOrderModel
      .deleteOne({
        user: userId,
        _id: orderId,
      })
      .exec();
  }

  async dbBotOrderResponse(data: any, response: any) {
    await this.botOrderModel
      .findOneAndUpdate(
        {
          user: data.user,
          _id: data._id,
        },
        {
          $set: {
            status: 'EXECUTED',
            executionTimestamp: new Date(),
            response,
          },
        },
      )
      .lean()
      .exec();
  }

  async dbUpdateSLBotOrder(
    stoplossPrice: number,
    orderId: Types.ObjectId | string,
  ) {
    await this.botOrderModel
      .findOneAndUpdate(
        {
          _id: orderId,
        },
        {
          $set: {
            price: stoplossPrice,
          },
        },
      )
      .lean()
      .exec();
  }
}
