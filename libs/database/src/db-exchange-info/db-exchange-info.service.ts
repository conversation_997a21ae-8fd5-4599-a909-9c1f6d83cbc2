import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';

import { Model } from 'mongoose';
import { filter, pluck } from 'ramda';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbExcludedSymbolsService } from '@app/database/db-excluded-symbols/db-excluded-symbols.service';
import {
  DbExchangeInfo,
  DbExchangeInfoDocument,
} from '@app/database/db-exchange-info/db-exchange-info.model';
import { Symbol } from 'binance-api-node';

@Injectable()
export class DbExchangeInfoService {
  constructor(
    @InjectModel(DbExchangeInfo.name)
    private exchangeInfoModel: Model<DbExchangeInfoDocument>,
    private excludedSymbolsService: DbExcludedSymbolsService,
    private loggerService: DbLogService,
  ) {}

  async getLatestExchangeInfo(exchange: string, args?: any): Promise<Symbol[]> {
    try {
      const query: any = {
        exchange,
      };

      if (args.symbol) {
        query.symbol = args.symbol;
      }

      if (args.base) {
        query['symbolData.quoteAsset'] = args.base;
      } else if (!args.all) {
        query['symbolData.quoteAsset'] = 'USDT';
      }

      const dbResult = await this.exchangeInfoModel
        .find(query)
        .sort({ symbol: 1 })
        .lean()
        .exec();

      if (dbResult && dbResult.length > 0) {
        const symbols =
          (pluck('symbolData', dbResult) as Symbol[]) || ([] as Symbol[]);

        // Remove excluded symbols from result
        const excluded =
          await this.excludedSymbolsService.dbGetExcludedSymbols(exchange);
        return symbols.filter((x) => !excluded.includes(x.symbol));
      }

      return [];
    } catch (e) {
      this.loggerService.errorServer('dbExchangeInfo', e.message, e);
    }
  }

  async getUsdtSymbols(exchange: string) {
    try {
      const exchangeInfo: any = await this.getLatestExchangeInfo(exchange, {
        base: 'USDT',
      });
      return pluck('symbol' as any, exchangeInfo) || [];
    } catch (e) {
      this.loggerService.errorServer('getBtcSymbols', e);
    }
  }

  async getUsdtSymbolsWithoutExcluded(exchange: string) {
    const symbols: string[] = await this.getUsdtSymbols(exchange);
    const excluded =
      await this.excludedSymbolsService.dbGetExcludedSymbols(exchange);
    return filter((x) => !excluded.includes(x), symbols);
  }

  async getOneUsdtSymbol(symbol: string, exchange: string) {
    const dbResult = await this.exchangeInfoModel
      .findOne({
        symbol,
        exchange,
      })
      .lean()
      .exec();

    return dbResult.symbolData;
  }

  async removeAll(exchange: string) {
    await this.exchangeInfoModel
      .deleteMany({
        exchange,
      })
      .exec();
  }

  async saveExchangeInfoForSymbol(symbol: any, exchange: string) {
    return new this.exchangeInfoModel({
      symbol: symbol.symbol,
      symbolData: symbol,
      timestamp: new Date(),
      exchange,
    }).save();
  }
}
