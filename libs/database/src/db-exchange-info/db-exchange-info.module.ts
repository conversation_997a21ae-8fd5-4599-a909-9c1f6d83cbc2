import { Modu<PERSON> } from '@nestjs/common';
import { DbExchangeInfoService } from './db-exchange-info.service';
import { MongooseModule } from '@nestjs/mongoose';

import { DbLogModule } from '@app/database/db-log/db-log.module';
import { DbExcludedSymbolsModule } from '@app/database/db-excluded-symbols/db-excluded-symbols.module';
import {
  DbExchangeInfo,
  DbExchangeInfoSchema,
} from '@app/database/db-exchange-info/db-exchange-info.model';

@Module({
  providers: [DbExchangeInfoService],
  exports: [
    DbExchangeInfoService,
    MongooseModule.forFeature([
      {
        name: DbExchangeInfo.name,
        schema: DbExchangeInfoSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    DbExcludedSymbolsModule,
    MongooseModule.forFeature([
      {
        name: DbExchangeInfo.name,
        schema: DbExchangeInfoSchema,
      },
    ]),
  ],
})
export class DbExchangeInfoModule {}
