import { HydratedDocument } from 'mongoose';
import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';

export type DbPumpDocument = HydratedDocument<DbPump>;

@Schema({ collection: 'pumps' })
export class DbPump {
  @Prop()
  symbol: string;

  @Prop()
  notified: boolean;

  @Prop()
  price: number;

  @Prop()
  diff: number;

  @Prop({ type: Date })
  timestamp: any;

  @Prop()
  exchange: string;
}

export const DbPumpSchema = SchemaFactory.createForClass(DbPump);
DbPumpSchema.index({ symbol: 1, exchange: 1 });
DbPumpSchema.index({ timestamp: 1, exchange: 1 });
