import { Module } from '@nestjs/common';
import { DbPumpService } from './db-pump.service';
import { MongooseModule } from '@nestjs/mongoose';
import { DbLogModule } from '@app/database/db-log/db-log.module';
import { DbPump, DbPumpSchema } from '@app/database/db-pump/db-pump.model';

@Module({
  providers: [DbPumpService],
  exports: [
    DbPumpService,
    MongooseModule.forFeature([
      {
        name: DbPump.name,
        schema: DbPumpSchema,
      },
    ]),
  ],
  imports: [
    DbLogModule,
    MongooseModule.forFeature([
      {
        name: DbPump.name,
        schema: DbPumpSchema,
      },
    ]),
  ],
})
export class DbPumpModule {}
