import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbPump, DbPumpDocument } from '@app/database/db-pump/db-pump.model';

@Injectable()
export class DbPumpService {
  constructor(
    @InjectModel(DbPump.name) private pumpModel: Model<DbPumpDocument>,
    private logger: DbLogService,
  ) {}

  async dbGetPump(symbol: string, exchange: string) {
    return await this.pumpModel
      .findOne({
        symbol,
        notified: true,
        exchange,
      })
      .lean()
      .exec();
  }

  async dbAddPump(
    symbol: string,
    price: number,
    diff: number,
    exchange: string,
  ) {
    await new this.pumpModel({
      symbol,
      notified: true,
      price,
      diff,
      timestamp: new Date(),
      exchange,
    }).save();
  }

  async dbClearPump() {
    const oldTime = new Date(new Date().getTime() - 5 * 60 * 1000);

    await this.pumpModel
      .updateMany(
        {
          notified: true,
          timestamp: { $lt: oldTime },
        },
        { $set: { notified: false } },
      )
      .exec();
  }

  async dbClearOldPumps() {
    const oldTime = new Date(new Date().getTime() - 15 * 24 * 60 * 60 * 1000);

    await this.pumpModel
      .deleteMany({
        timestamp: { $lt: oldTime },
      })
      .exec();
  }

  async dbGetLastPumps(size: number = 12, exchange: string) {
    const result = await this.pumpModel
      .find({ exchange })
      .sort({ timestamp: -1 })
      .limit(100)
      .lean()
      .exec();

    const symbols = new Set();
    const completeResult = [];

    result.forEach((pump) => {
      if (symbols.size <= size && !symbols.has(pump.symbol)) {
        symbols.add(pump.symbol);
        completeResult.push(pump);
      }
    });

    return completeResult;
  }
}
