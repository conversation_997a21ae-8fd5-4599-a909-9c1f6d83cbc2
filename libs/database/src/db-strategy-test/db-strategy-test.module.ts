import { Module } from '@nestjs/common';
import { DbStrategyTestService } from './db-strategy-test.service';
import { MongooseModule } from '@nestjs/mongoose';
import {
  DbStrategyTest,
  DbStrategyTestSchema,
} from '@app/database/db-strategy-test/db-strategy-test.model';

@Module({
  providers: [DbStrategyTestService],
  exports: [
    DbStrategyTestService,
    MongooseModule.forFeature([
      {
        name: DbStrategyTest.name,
        schema: DbStrategyTestSchema,
      },
    ]),
  ],
  imports: [
    MongooseModule.forFeature([
      {
        name: DbStrategyTest.name,
        schema: DbStrategyTestSchema,
      },
    ]),
  ],
})
export class DbStrategyTestModule {}
