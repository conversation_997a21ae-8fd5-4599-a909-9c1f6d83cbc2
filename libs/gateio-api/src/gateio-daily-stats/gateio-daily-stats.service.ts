import { Injectable } from '@nestjs/common';
import { GateioConnectService } from '@app/gateio-api/gateio-connect/gateio-connect.service';
import { DailyStatsResult } from 'binance-api-node';
import { SpotTicker } from 'gateio-api';
import { DbLogService } from '@app/database/db-log/db-log.service';

@Injectable()
export class GateioDailyStatsService {
  constructor(
    private connectService: GateioConnectService,
    private logger: DbLogService,
  ) {}

  async getDailyStats(args?: any): Promise<DailyStatsResult[]> {
    try {
      const client = this.connectService.init();

      if (args) {
        const result = await client.getSpotTicker({
          currency_pair: args.symbol.slice(0, -4) + '_USDT',
        });

        const mappedResult = this.mapResult(result);
        return mappedResult[0];
      }

      const result = await client.getSpotTicker({
        currency_pair: args.symbol.slice(0, -4) + '_USDT',
      });

      return this.mapResult(result)[0];
    } catch (e) {
      this.logger.errorServer(e.message, e);
    }
  }

  mapResult(
    re: SpotTicker[],
  ): DailyStatsResult[] | PromiseLike<DailyStatsResult[]> {
    return re.map((x) => {
      return {
        symbol: x.currency_pair.replace('_', ''),
        volume: x.base_volume,
        quoteVolume: x.quote_volume,
        bidPrice: x.highest_bid,
        askPrice: x.lowest_ask,
        highPrice: x.high_24h,
        lowPrice: x.low_24h,
        askQty: null,
        bidQty: null,
        count: 0,
        lastId: 0,
        firstId: 0,
        lastQty: null,
        closeTime: new Date(x.etf_pre_timestamp).getTime() * 1000,
        lastPrice: x.last,
        openPrice: x.last,
        openTime: new Date(x.etf_pre_timestamp).getTime() * 1000,
        prevClosePrice: x.last,
        priceChange: x.change_utc0,
        priceChangePercent: x.change_percentage,
        weightedAvgPrice: x.last,
      } as DailyStatsResult;
    });
  }
}
