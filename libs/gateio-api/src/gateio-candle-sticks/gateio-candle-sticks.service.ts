import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { CandleChartResult, CandlesOptions } from 'binance-api-node';
import { GateioConnectService } from '@app/gateio-api/gateio-connect/gateio-connect.service';
import { GetSpotCandlesReq } from 'gateio-api/dist/mjs/types/request/spot';
import { SpotCandle } from 'gateio-api';

@Injectable()
export class GateioCandleSticksService {
  constructor(
    private logger: DbLogService,
    private connectService: GateioConnectService,
  ) {}

  /**
   * [{
   openTime: 1508328900000,
   open: '0.05655000',
   high: '0.05656500',
   low: '0.05613200',
   close: '0.05632400',
   volume: '68.88800000',
   closeTime: 1508329199999,
   quoteAssetVolume: '2.29500857',
   trades: 85,
   baseAssetVolume: '40.61900000'
   }]
   */
  async getCandleSticks(args: CandlesOptions): Promise<CandleChartResult[]> {
    const client = this.connectService.init();
    try {
      const result: SpotCandle[] = await client.getSpotCandles({
        currency_pair: args.symbol.slice(0, -4) + '_USDT',
        from: args.startTime ? Math.floor(args.startTime / 1000) : undefined,
        to: args.endTime ? Math.floor(args.endTime / 1000) : undefined,
        limit: args.limit,
        interval: args.interval,
      } as GetSpotCandlesReq);

      return result.map((x) => {
        return {
          openTime: Number(x.at(0)) * 1000,
          volume: x.at(1),
          quoteVolume: x.at(1),
          quoteAssetVolume: x.at(1),
          close: x.at(2),
          high: x.at(3),
          low: x.at(4),
          open: x.at(5),
          baseAssetVolume: x.at(6),
          closeTime: Number(x.at(0)) * 1000,
          trades: 0,
        } as CandleChartResult;
      });
    } catch (e) {
      this.logger.errorServer(e.message, e);
    }

    return null;
  }
}
