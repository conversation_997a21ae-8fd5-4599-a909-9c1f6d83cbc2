import { Injectable } from '@nestjs/common';
import {
  RestClient,
  WebsocketClient,
  WSClientConfigurableOptions,
} from 'gateio-api';

@Injectable()
export class GateioConnectService {
  init(key?: string, secret?: string) {
    if (!key || !secret) {
      return new RestClient();
    }

    return new RestClient({
      apiKey: key,
      apiSecret: secret,
    });
  }

  initWS(key?: string, secret?: string) {
    const wsConfig = {
      apiKey: key,
      apiSecret: secret,
    } as WSClientConfigurableOptions;

    if (!key || !secret) {
      return new WebsocketClient();
    }

    wsConfig.apiKey = key;
    wsConfig.apiSecret = secret;

    return new WebsocketClient(wsConfig);
  }
}
