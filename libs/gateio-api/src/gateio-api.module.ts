import { Modu<PERSON> } from '@nestjs/common';
import { DatabaseModule } from '@app/database';
import { GateioConnectService } from './gateio-connect/gateio-connect.service';
import { GateioExchangeInfoService } from './gateio-exchange-info/gateio-exchange-info.service';
import { GateioAccountInfoService } from './gateio-account-info/gateio-account-info.service';
import { GateioPricesService } from './gateio-prices/gateio-prices.service';
import { GateioMyTradesService } from './gateio-my-trades/gateio-my-trades.service';
import { GateioCancelOpenOrderService } from './gateio-cancel-open-order/gateio-cancel-open-order.service';
import { GateioCandleSticksService } from './gateio-candle-sticks/gateio-candle-sticks.service';
import { GateioOrderBookService } from './gateio-order-book/gateio-order-book.service';
import { GateioOpenOrdersService } from './gateio-open-orders/gateio-open-orders.service';
import { GateioNewOrderService } from './gateio-new-order/gateio-new-order.service';
import { GateioDailyStatsService } from './gateio-daily-stats/gateio-daily-stats.service';
import { GateioWsAlltickersService } from '@app/gateio-api/ws/gateio-ws-alltickers.service';

@Module({
  providers: [
    GateioConnectService,
    GateioExchangeInfoService,
    GateioAccountInfoService,
    GateioPricesService,
    GateioMyTradesService,
    GateioCancelOpenOrderService,
    GateioCandleSticksService,
    GateioOrderBookService,
    GateioOpenOrdersService,
    GateioNewOrderService,
    GateioDailyStatsService,
    GateioWsAlltickersService,
  ],
  exports: [
    GateioConnectService,
    GateioExchangeInfoService,
    GateioAccountInfoService,
    GateioPricesService,
    GateioMyTradesService,
    GateioCancelOpenOrderService,
    GateioCandleSticksService,
    GateioOrderBookService,
    GateioOpenOrdersService,
    GateioNewOrderService,
    GateioDailyStatsService,
    GateioWsAlltickersService,
  ],
  imports: [DatabaseModule],
})
export class GateioApiModule {}
