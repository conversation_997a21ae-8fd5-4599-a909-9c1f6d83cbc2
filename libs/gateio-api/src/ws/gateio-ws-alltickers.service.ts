import { Injectable } from '@nestjs/common';
import { GateioConnectService } from '@app/gateio-api/gateio-connect/gateio-connect.service';
import { Ticker } from 'binance-api-node';

@Injectable()
export class GateioWsAlltickersService {
  client: any;
  lastInput: any;

  constructor(private connectService: GateioConnectService) {
    this.client = this.connectService.initWS();
  }

  async unsubscribeFromAll() {
    await this.client.unsubscribe(this.lastInput, 'spotV4');
  }

  async allTickers(callback: any, symbols: string[]) {
    this.lastInput = {
      topic: 'spot.tickers',
      payload: symbols,
    };

    await this.client.subscribe(this.lastInput, 'spotV4');

    this.client.on('update', (data) => {
      const t = data.result;

      const result: Ticker = {
        symbol: t.currency_pair.replace('_', ''),
        bestAsk: t.highest_ask,
        bestBid: t.highest_bid,
        low: t.low_24h,
        high: t.high_24h,
        open: t.last,
        volume: t.quote_volume,
        eventTime: new Date().getTime(),
        bestAskQnt: t.quote_volume,
        bestBidQnt: t.quote_volume,
        closeTime: new Date().getTime(),
        curDayClose: null,
        eventType: null,
        firstTradeId: null,
        lastTradeId: null,
        openTime: new Date().getTime(),
        prevDayClose: null,
        priceChange: null,
        priceChangePercent: t.change_percentage,
        totalTrades: null,
        volumeQuote: t.quote_volume,
        weightedAvg: null,
        closeTradeQuantity: t.quote_volume,
      };
      callback([result]);
    });

    // Optional: Listen to websocket connection open event (automatic after subscribing to one or more topics)
    this.client.on('open', ({ wsKey, event }) => {
      console.log('connection open for websocket with ID: ' + wsKey);
    });

    // Optional: Listen to responses to websocket queries (e.g. the reply after subscribing to a topic)
    this.client.on('response', (response) => {
      console.log('response', response.result);
    });

    // Optional: Listen to connection close event. Unexpected connection closes are automatically reconnected.
    this.client.on('close', () => {
      console.log('connection closed');
    });

    // Optional: listen to internal exceptions. Useful for debugging if something weird happens
    this.client.on('exception', (data) => {
      console.error('exception: ', data);
    });

    // Optional: Listen to raw error events.
    this.client.on('error', (err) => {
      console.error('ERR', err);
    });
  }
}
