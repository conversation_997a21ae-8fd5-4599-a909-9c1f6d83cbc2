import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbKeyService } from '@app/database/db-key/db-key.service';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import { DbHiddenSymbolsService } from '@app/database/db-hidden-symbols/db-hidden-symbols.service';
import { GateioConnectService } from '@app/gateio-api/gateio-connect/gateio-connect.service';
import { DbKey } from '@app/database/db-key/db-key.model';
import { MbAccountInfo } from '@app/binance-api/binance-account-info/binance-account-info.service';
import { Exchange } from '../../../../types/exchanges';
import { v4 } from 'uuid';
import { SpotAccount } from 'gateio-api';

export const enum TradingType {
  MARGIN = 'MARGIN',
  SPOT = 'SPOT',
}

@Injectable()
export class GateioAccountInfoService {
  constructor(
    private logger: DbLogService,
    private keyService: DbKeyService,
    private coinPriceService: DbCoinPriceService,
    private hiddenSymbolsService: DbHiddenSymbolsService,
    private gateioConnectService: GateioConnectService,
  ) {}

  SMALLEST_AMOUNT = 2;

  public async getAccountInfo(
    apiKey: DbKey,
    args: {
      noSmallAmount: boolean;
      symbols?: string[];
    },
  ): Promise<MbAccountInfo> {
    const client = this.gateioConnectService.init(apiKey.key, apiKey.secret);

    try {
      const mbAccountInfo: MbAccountInfo = {
        usdValue: 0,
        btcValue: 0,
        balances: [],
        accountType: TradingType.SPOT as any,
        buyerCommission: 0,
        canDeposit: true,
        canTrade: true,
        canWithdraw: true,
        makerCommission: 0,
        updateTime: new Date().getTime(),
        permissions: [],
        sellerCommission: 0,
        takerCommission: 0,
      };

      const accountInfo = await client.getSpotAccounts();

      return this.addBtcBalances(accountInfo, mbAccountInfo, args);
    } catch (e) {
      this.logger.errorServer('Error fetching account info', e);
    }

    return null;
  }

  private async addBtcBalances(
    result: SpotAccount[],
    mbAccountInfo: MbAccountInfo,
    args: { noSmallAmount: boolean; symbols?: string[] },
  ): Promise<MbAccountInfo> {
    const newResult = { ...mbAccountInfo };

    newResult.usdValue = 0;
    newResult.btcValue = 0;

    const btcPrice = await this.coinPriceService.dbGetCoinPrice(
      'BTCUSDT',
      Exchange.GATEIO,
    );
    const balances = [];

    const hiddenSymbols = await this.hiddenSymbolsService.dbGetHiddenSymbols();
    const coinPrices = await this.coinPriceService.dbGetCoinPricesIn(
      Exchange.GATEIO,
      result.map((x) => x.currency + 'USDT'),
    );

    result.forEach((obj) => {
      const coin = { ...obj };
      const free = Number(coin.available);
      const locked = Number(coin.locked);
      const { currency } = coin;

      let usdValue = 0;
      let btcValue = 0;

      // only show items with balances
      if (free === 0 && locked === 0) {
        return;
      }

      if (currency === 'BTC') {
        btcValue = free + locked;
        usdValue = btcValue * btcPrice;
      } else if (currency === 'USDT') {
        usdValue = free + locked;
        btcValue = usdValue / btcPrice;
      } else {
        const coinPrice = coinPrices.find((x) => x.symbol == `${currency}USDT`);
        if (!coinPrice) {
          return;
        }

        btcValue = Number(((free + locked) * coinPrice.price) / btcPrice);
        usdValue = Number((free + locked) * coinPrice.price);

        if (args.noSmallAmount) {
          if (Number(usdValue) < Number(this.SMALLEST_AMOUNT)) {
            return;
          }
        }
      }

      if (Number.isNaN(btcValue)) {
        btcValue = 0;
      }
      if (Number.isNaN(usdValue)) {
        usdValue = 0;
      }

      newResult.usdValue += usdValue;
      newResult.btcValue += btcValue;

      if (!hiddenSymbols.includes(currency)) {
        if (
          !args.symbols ||
          (args.symbols && args.symbols.includes(currency))
        ) {
          balances.push({
            asset: currency,
            free,
            locked,
            usdValue,
            btcValue,
            idx: v4(),
          });
        }
      }
    });

    newResult.balances = balances;

    return newResult;
  }

  async getAccountInfoWithRetry(userId: string) {
    const keys = await this.keyService.getApiKey(userId, Exchange.GATEIO);
    let accountInfo = await this.getAccountInfo(keys, {
      noSmallAmount: false,
    });
    let i = 0;

    while (!accountInfo && i < 10) {
      i++;

      this.logger.logServer(
        Exchange.GATEIO,
        'Account info fetch failed. Retry ' + i,
      );

      accountInfo = await this.getAccountInfo(keys, {
        noSmallAmount: false,
      });

      if (accountInfo) {
        return accountInfo;
      }
    }

    return accountInfo;
  }
}
