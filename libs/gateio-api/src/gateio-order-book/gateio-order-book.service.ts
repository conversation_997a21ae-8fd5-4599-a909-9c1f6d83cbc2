import { Injectable } from '@nestjs/common';
import { Db<PERSON><PERSON> } from '@app/database/db-key/db-key.model';
import { GateioConnectService } from '@app/gateio-api/gateio-connect/gateio-connect.service';
import { Bid, OrderBook } from 'binance-api-node';
import { DbLogService } from '@app/database/db-log/db-log.service';

@Injectable()
export class GateioOrderBookService {
  constructor(
    private connectService: GateioConnectService,
    private logger: DbLogService,
  ) {}

  async getOrderBook(
    apiKey: DbKey,
    args: GQL.IOrderbookOnExchangeQueryArguments,
  ): Promise<OrderBook> {
    try {
      const client = this.connectService.init(apiKey.key, apiKey.secret);

      const result = await client.getSpotOrderBook({
        currency_pair: args.symbol.slice(0, -4) + '_USDT',
        limit: args.limit,
      });

      const bids = result.bids.map((x) => {
        return {
          price: x[0],
          quantity: x[1],
        } as Bid;
      });

      const asks = result.asks.map((x) => {
        return {
          price: String(x[0]),
          quantity: String(x[1]),
        } as Bid;
      });

      return {
        bids,
        asks,
        lastUpdateId: result.id,
      };
    } catch (e) {
      this.logger.errorServer(e.message, e);
    }
  }
}
