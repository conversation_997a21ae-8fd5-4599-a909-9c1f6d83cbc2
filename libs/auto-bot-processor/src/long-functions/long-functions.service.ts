import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbMarketBotService } from '@app/database/db-market-bot/db-market-bot.service';
import { DbKeyService } from '@app/database/db-key/db-key.service';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import { BinanceDailyStatsService } from '@app/binance-api/binance-daily-stats/binance-daily-stats.service';
import { MarketBotAdviceProcessorService } from '@app/auto-bot-processor/market-bot-advice-processor/market-bot-advice-processor.service';
import { DbAutoBotDocument } from '@app/database/db-auto-bot/db-auto-bot.model';
import { MbAccountInfo } from '@app/binance-api/binance-account-info/binance-account-info.service';
import { DbMarketBotDocument } from '@app/database/db-market-bot/db-market-bot.model';
import { DbIndicator } from '@app/database/db-indicator/db-indicator.model';
import { sleep } from '../../../../utils/wait';

@Injectable()
export class LongFunctionsService {
  constructor(
    private logger: DbLogService,
    private marketBotService: DbMarketBotService,
    private keyService: DbKeyService,
    private coinPriceService: DbCoinPriceService,
    private dailyStatsService: BinanceDailyStatsService,
    private marketBotAdviceProcessorService: MarketBotAdviceProcessorService,
  ) {}

  public async longAutoBot(
    autobot: DbAutoBotDocument,
    newAdvice: DbIndicator,
    accountInfo: MbAccountInfo,
  ) {
    try {
      // Добавить новый бот в лист
      const marketBot = await this.addNewBot(newAdvice, autobot, accountInfo);

      // Купить коины на сумму указанную в автоботе
      if (marketBot) {
        await this.buyCoins(newAdvice, marketBot, autobot);
      }

      await sleep(() => {});
    } catch (e) {
      this.logger.errorOrders(newAdvice.symbol, '->', e.message);
      await this.removeMarketBotOnFailure(autobot, newAdvice);
    }
  }

  private async removeMarketBotOnFailure(
    autobot: DbAutoBotDocument,
    newAdvice: DbIndicator,
  ) {
    const marketBot =
      await this.marketBotService.dbGetMarketBotForAutobotAndSymbol(
        autobot._id.toString(),
        newAdvice.symbol,
      );

    this.logger.logOrders(
      marketBot.symbol,
      autobot.exchange,
      '-> Remove market bot',
    );
    await this.marketBotService.dbDeleteMarketBotByIdAndUserId(
      marketBot._id,
      marketBot.user,
    );

    this.logger.logOrders(
      marketBot.symbol,
      marketBot.exchange,
      '-> Removing market bot from autobot',
      autobot._id.toString(),
    );
  }

  private async addNewBot(
    newAdvice: DbIndicator,
    autobot: DbAutoBotDocument,
    accountInfo: MbAccountInfo,
  ) {
    const { balances } = accountInfo;
    let maxBotSlots = autobot.numbots || 50;

    const coinPrice = await this.coinPriceService.dbGetCoinPrice(
      newAdvice.symbol,
      autobot.exchange,
    );

    // Вырешать на сколько БТС купить (исходя из долларов)
    this.logger.logOrders(
      newAdvice.symbol,
      newAdvice.exchange,
      '-> Fetching current market data...',
    );
    const btcLastData: any = await this.dailyStatsService.getDailyStats({
      symbol: 'BTCUSDT',
    });
    const btcPrice = btcLastData ? Number(btcLastData.lastPrice) : 0;

    const usdtBalance = balances.find((x) => x.asset === 'USDT') || null;
    const marketBots = await this.marketBotService.dbGetMarketBotsForAutobot(
      autobot._id.toString(),
    );
    const usedBotSlots = marketBots.length;

    let investUsd = this.calcInvestSum(
      newAdvice.symbol,
      usdtBalance.usdValue,
      maxBotSlots,
      usedBotSlots,
      autobot,
    );
    const valueInBtc = Number(Number(investUsd) / btcPrice).toFixed(8);

    this.logger.logOrders(
      newAdvice.symbol,
      newAdvice.exchange,
      '-> Adding market bot | Free USD balance:',
      usdtBalance.usdValue,
      '| Current used bot slots:',
      marketBots.length,
      '| Max bot slots:',
      maxBotSlots,
    );
    this.logger.logOrders(
      newAdvice.symbol,
      newAdvice.exchange,
      '-> Current coin price:',
      coinPrice,
      'USDT',
    );
    this.logger.logOrders(
      newAdvice.symbol,
      newAdvice.exchange,
      '-> Adviced buy price:',
      newAdvice.price,
      'USDT',
    );
    this.logger.logOrders(
      newAdvice.symbol,
      newAdvice.exchange,
      '-> Prediction price:',
      newAdvice.indicatorValue,
      'USDT',
    );
    this.logger.logOrders(
      newAdvice.symbol,
      newAdvice.exchange,
      '-> Investing:',
      investUsd,
      'USD /',
      valueInBtc,
      'BTC',
    );

    const data = {
      strategy: autobot.strategy,
      symbol: newAdvice.symbol,
      strategyParams: null,
      interval: null,
      amountUsdtInvested: investUsd,
      simulation: false,
      startWithPosition: 'long',
      longPrice: newAdvice.price,
      limitSell: false,
      fromAutoBot: true,
      autobotId: autobot._id.toString(),
      btcPrice,
      predictionPrice: newAdvice.indicatorValue,
      sellInMinus: autobot.enableSellInMinus,
    };

    return await this.marketBotService.dbAddNewMarketBot(
      autobot.userId,
      data,
      autobot.exchange,
    );
  }

  private calcInvestSum(
    symbol: string,
    freeUsdValue: number,
    maxBotSlots: number,
    usedBotSlots: number,
    autobot: DbAutoBotDocument,
  ): string {
    if (!autobot.autoUsdPerCoin) {
      return String(autobot.usdPerCoin);
    }

    if (usedBotSlots >= maxBotSlots) {
      this.logger.warnOrders(
        symbol,
        '-> All free slots are used!',
        '| Max bot slots now:',
        maxBotSlots,
        '| Used bot slots:',
        usedBotSlots,
      );
    }

    // 1. Calc usd for coin
    const slots = Math.max(maxBotSlots - usedBotSlots, 1);
    this.logger.logOrders(symbol, autobot.exchange, `-> Free slots: ${slots}`);

    const usdForCoin = Number(Number(freeUsdValue / slots).toFixed(2));
    this.logger.logOrders(
      symbol,
      autobot.exchange,
      `-> USD for coin: ${usdForCoin}`,
    );

    // 2. If usdForCoin < free sum, buy for full price
    // if (freeUsdValue >= usdForCoin) {
    return (Number(usdForCoin) * 0.97).toFixed(2);
    // }

    // 3. Otherwise buy half of rest usd
    // else {
    //   return Number(freeUsdValue / 2).toFixed(2);
    // }

    // Old routine
    // return Number(
    //   freeUsdValue / (maxBotSlots - usedBotSlots),
    // ).toFixed(2);
  }

  private async buyCoins(
    newAdvice: DbIndicator,
    newBot: DbMarketBotDocument,
    autobot: DbAutoBotDocument,
  ) {
    // Get API Keys from user's bot
    const binanceKeys = await this.keyService.getApiKey(
      autobot.userId,
      autobot.exchange,
    );

    this.logger.logOrders(
      newAdvice.symbol,
      newAdvice.exchange,
      '-> Process Long Market Trade | Investing USD:',
      Number(newBot.amountUsdtInvested).toString(),
    );
    await this.marketBotAdviceProcessorService.processLong(
      newBot,
      newAdvice.price,
      autobot.userId,
      binanceKeys,
      'long',
      newAdvice.symbol,
      autobot.strategy,
      newAdvice.indicatorValue,
    );
  }
}
