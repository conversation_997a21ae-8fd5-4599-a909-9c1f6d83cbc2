import { Module } from '@nestjs/common';
import { DatabaseModule } from '@app/database';
import { SendMailModule } from '@app/send-mail';
import { BinanceApiModule } from '@app/binance-api';
import { LongFunctionsService } from './long-functions/long-functions.service';
import { MarketBotAdviceProcessorService } from './market-bot-advice-processor/market-bot-advice-processor.service';
import { AdviceProcessorService } from './advice-processor/advice-processor.service';
import { TradeProcessorService } from '@app/auto-bot-processor/trade-processor/trade-processor.service';
import { ShortFunctionsService } from './short-functions/short-functions.service';
import { BuySellService } from './buy-sell/buy-sell.service';
import { GateioApiModule } from '@app/gateio-api';
import { TradeProcessorChecksService } from '@app/auto-bot-processor/trade-processor/trade-processor-checks.service';

@Module({
  imports: [DatabaseModule, BinanceApiModule, GateioApiModule, SendMailModule],
  providers: [
    LongFunctionsService,
    MarketBotAdviceProcessorService,
    AdviceProcessorService,
    ShortFunctionsService,
    TradeProcessorService,
    TradeProcessorChecksService,
    BuySellService,
  ],
  exports: [
    LongFunctionsService,
    MarketBotAdviceProcessorService,
    AdviceProcessorService,
    ShortFunctionsService,
    TradeProcessorService,
    TradeProcessorChecksService,
    BuySellService,
  ],
})
export class AutoBotProcessorModule {}
