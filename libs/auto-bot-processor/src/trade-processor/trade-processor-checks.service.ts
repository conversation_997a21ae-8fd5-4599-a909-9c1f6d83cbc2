import { Injectable } from '@nestjs/common';
import { DbAutoBotDocument } from '@app/database/db-auto-bot/db-auto-bot.model';
import {
  DbIndicator,
  DbIndicatorDocument,
} from '@app/database/db-indicator/db-indicator.model';
import { DbMarketBotService } from '@app/database/db-market-bot/db-market-bot.service';
import { DbWhiteListService } from '@app/database/db-white-list/db-white-list.service';
import { DbMacdService } from '@app/database/db-macd/db-macd.service';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { MbAccountInfo } from '@app/binance-api/binance-account-info/binance-account-info.service';

@Injectable()
export class TradeProcessorChecksService {
  constructor(
    private marketBotService: DbMarketBotService,
    private whitelistService: DbWhiteListService,
    private macdService: DbMacdService,
    private logger: DbLogService,
  ) {}

  checkAOIndicator = async (
    autobot: DbAutoBotDocument,
    newAdvice: DbIndicator,
  ) => {
    if (autobot.buyOnlyOnAOPlus === true) {
      const aoIndicator = await this.macdService.getMACD(
        {
          symbol: newAdvice.symbol,
          interval: '1d',
        },
        autobot.exchange,
      );

      const signal = aoIndicator.signal;

      if (signal != null && signal < 0) {
        this.logger.warnOrders(
          newAdvice.symbol,
          '-> AO Indicator failed ' +
            Number(signal).toFixed(8) +
            " < 0. Don't process",
        );
        return false;
      } else {
        this.logger.logOrders(
          newAdvice.symbol,
          autobot.exchange,
          '-> AO Indicator Check OK: ' + Number(signal).toFixed(8) + ' > 0',
        );
        return true;
      }
    }

    return true;
  };

  async checkBtcCrossIndicator(
    autobot: DbAutoBotDocument,
    newAdvice: DbIndicator,
  ) {
    if (autobot.useBtcAOCross === true) {
      const aoIndicator = await this.macdService.getMACD(
        {
          symbol: 'BTCUSDT',
          interval: '1d',
        },
        autobot.exchange,
      );

      const signal = aoIndicator.signal;

      if (signal != null && signal < 0) {
        this.logger.warnOrders(
          newAdvice.symbol,
          '-> BTC AO Indicator < 0. ' + "Don't process",
        );
        return false;
      } else {
        this.logger.logOrders(
          newAdvice.symbol,
          autobot.exchange,
          '-> BTC AO Indicator Check OK: ' + Number(signal).toFixed(8) + ' > 0',
        );
        return true;
      }
    }

    return true;
  }

  checkOnlyBtc = async (autobot: DbAutoBotDocument) => {
    return autobot.btcOnly;
  };

  checkAlreadyBought = async (
    autobot: DbAutoBotDocument,
    newAdvice: DbIndicatorDocument,
  ) => {
    const botList =
      await this.marketBotService.dbGetMarketBotsFromAutobot(autobot);

    return botList.filter((x) => x.symbol === newAdvice.symbol).length > 0;
  };

  maxBotCountReached = async (autobot, newAdvice: DbIndicatorDocument) => {
    const botList =
      await this.marketBotService.dbGetMarketBotsFromAutobot(autobot);

    const numbots = autobot.numbots ? autobot.numbots : 47;
    const maxCountReached = botList.length > numbots;

    if (maxCountReached) {
      this.logger.warnOrders(
        newAdvice.symbol,
        '-> Max bot count reached. Current bots:',
        botList.length,
        ". Don't process",
      );
    }

    return maxCountReached;
  };

  isLowPriceCoin = async (newAdvice: DbIndicatorDocument) => {
    return newAdvice.price < 0.000001;
  };

  isOnWhitelist = async (
    autobot: DbAutoBotDocument,
    newAdvice: DbIndicator,
  ) => {
    if (autobot.buyOnlyFromWhitelist) {
      const whitelist: any = await this.whitelistService.dbGetWhiteListCoins(
        autobot.userId,
        autobot.exchange,
      );

      const foundInWhitelist = whitelist.find(
        (x) => x.symbol === newAdvice.symbol,
      );
      if (foundInWhitelist != undefined) {
        this.logger.logOrders(
          newAdvice.symbol,
          autobot.exchange,
          '-> Found on whitelist',
        );
      }
      return foundInWhitelist != undefined;
    }

    return true;
  };

  canBuyOneBot = async (
    autobot: DbAutoBotDocument,
    newAdvice: DbIndicatorDocument,
    accountInfo: MbAccountInfo,
  ) => {
    const { balances } = accountInfo;
    const usdBalance = balances.find((x) => x.asset === 'USDT') || null;
    const freeUsd = Number(usdBalance.free);

    this.logger.logOrders(
      newAdvice.symbol,
      autobot.exchange,
      '-> Prepare buy. Free USDT:',
      freeUsd,
      '| USD per coin:',
      autobot.usdPerCoin,
      '| Auto USD:',
      String(autobot.autoUsdPerCoin),
    );

    return freeUsd >= 10;
  };
}
