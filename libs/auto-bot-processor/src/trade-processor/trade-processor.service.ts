import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbAutoBotDocument } from '@app/database/db-auto-bot/db-auto-bot.model';
import { BinanceAccountInfoService } from '@app/binance-api/binance-account-info/binance-account-info.service';
import { LongFunctionsService } from '@app/auto-bot-processor/long-functions/long-functions.service';
import { ShortFunctionsService } from '@app/auto-bot-processor/short-functions/short-functions.service';
import { BuySellService } from '@app/auto-bot-processor/buy-sell/buy-sell.service';
import { DbIndicatorDocument } from '@app/database/db-indicator/db-indicator.model';
import { Exchange } from '../../../../types/exchanges';
import { GateioAccountInfoService } from '@app/gateio-api/gateio-account-info/gateio-account-info.service';
import { TradeProcessorChecksService } from '@app/auto-bot-processor/trade-processor/trade-processor-checks.service';
import { Advice } from '../../../../types/advice';
import { DbNoticedCoinService } from '@app/database/db-noticed-coin/db-noticed-coin.service';

@Injectable()
export class TradeProcessorService {
  constructor(
    private logger: DbLogService,
    private binanceAccountInfoService: BinanceAccountInfoService,
    private gateioAccountInfoService: GateioAccountInfoService,
    private longFunction: LongFunctionsService,
    private shortFunctions: ShortFunctionsService,
    private buySellService: BuySellService,
    private checkService: TradeProcessorChecksService,
    private noticedCoinService: DbNoticedCoinService,
  ) {}

  processAdvice = async (
    newAdvice: DbIndicatorDocument,
    autobot: DbAutoBotDocument,
  ) => {
    // При лонге алгоритма бот сам покупает по маркету на эту сумму ( и ставит стоп лосс?)
    if (newAdvice.action === Advice.LONG) {
      await this.onLong(newAdvice, autobot);
    }

    // При шорте открывается слот для новой сделки при следующем лонге бота
    else if (newAdvice.action === Advice.SHORT) {
      await this.onShort(newAdvice, autobot);
    }
  };

  private async onLong(
    newAdvice: DbIndicatorDocument,
    autobot: DbAutoBotDocument,
  ) {
    const buyOnlyFromWhitelist = await this.checkService.isOnWhitelist(
      autobot,
      newAdvice,
    );

    const noticedCoints = await this.noticedCoinService.getAllNoticedCoins(
      autobot.exchange,
    );
    if (noticedCoints.includes(newAdvice.symbol)) {
      this.logger.logOrders(
        newAdvice.symbol,
        newAdvice.action,
        newAdvice.indicator,
        newAdvice.interval,
        newAdvice.exchange,
        '->',
        'onLong',
      );
    }

    if (buyOnlyFromWhitelist) {
      this.logger.logOrders(
        newAdvice.symbol,
        autobot.exchange,
        '-> Coin is on whitelist. Processing...',
      );
    } else {
      return;
    }
    this.logger.logOrders(
      newAdvice.symbol,
      autobot.exchange,
      '-> Start LONG trade',
    );

    const checkAO = await this.checkService.checkAOIndicator(
      autobot,
      newAdvice,
    );
    if (!checkAO) {
      return;
    }

    const checkBtcCross = await this.checkService.checkBtcCrossIndicator(
      autobot,
      newAdvice,
    );
    if (!checkBtcCross) {
      return;
    }

    // Тест есть ли этот коин в портфолио
    const alreadyBought = await this.checkService.checkAlreadyBought(
      autobot,
      newAdvice,
    );
    if (alreadyBought) {
      this.logger.logOrders(
        newAdvice.symbol,
        autobot.exchange,
        "-> Already in portfolio! Don't process",
      );
      return;
    }

    const isLowPrice = await this.checkService.isLowPriceCoin(newAdvice);
    if (isLowPrice) {
      this.logger.logOrders(
        newAdvice.symbol,
        autobot.exchange,
        '-> Price below 0.******** | Adviced price:',
        newAdvice.price,
        ". Don't process",
      );
      return;
    }

    this.logger.logOrders(
      newAdvice.symbol,
      autobot.exchange,
      '-> Fetching account info',
    );
    let accountInfo = null;
    switch (autobot.exchange) {
      case Exchange.BINANCE:
        accountInfo =
          await this.binanceAccountInfoService.getAccountInfoWithRetry(
            autobot.userId,
          );
        break;
      case Exchange.GATEIO:
        accountInfo =
          await this.gateioAccountInfoService.getAccountInfoWithRetry(
            autobot.userId,
          );
        break;
    }

    const canBuy = await this.checkService.canBuyOneBot(
      autobot,
      newAdvice,
      accountInfo,
    );
    if (!canBuy) {
      this.logger.warnOrders(
        newAdvice.symbol,
        autobot.exchange,
        '-> Not enough credit for long trade',
        newAdvice.indicator,
        ". Don't process",
      );
      return;
    }

    // Если достигнуто максимальное количество активных ботов, не продолжать дальше и перепрыгнуть к следующему
    const countReached = await this.checkService.maxBotCountReached(
      autobot,
      newAdvice,
    );
    if (countReached) {
      return;
    }

    await this.longFunction.longAutoBot(autobot, newAdvice, accountInfo);
  }

  /***
   On Short
   **/
  private async onShort(
    newAdvice: DbIndicatorDocument,
    autobot: DbAutoBotDocument,
  ) {
    const coinBought = await this.checkService.checkAlreadyBought(
      autobot,
      newAdvice,
    );

    const noticedCoins = await this.noticedCoinService.getAllNoticedCoins(
      autobot.exchange,
    );
    if (noticedCoins.includes(newAdvice.symbol)) {
      this.logger.logOrders(
        newAdvice.symbol,
        newAdvice.action,
        newAdvice.indicator,
        newAdvice.interval,
        newAdvice.exchange,
        '->',
        'onShort',
        'Coin already bought:',
        String(coinBought),
      );
    }

    // Skip if coin not in bought list
    if (!coinBought) {
      return;
    }

    this.logger.logOrders(
      newAdvice.symbol,
      autobot.exchange,
      '-> Start SHORT trade',
    );

    let accountInfo = null;
    switch (autobot.exchange) {
      case Exchange.BINANCE:
        accountInfo =
          await this.binanceAccountInfoService.getAccountInfoWithRetry(
            autobot.userId,
          );
        break;
      case Exchange.GATEIO:
        accountInfo =
          await this.gateioAccountInfoService.getAccountInfoWithRetry(
            autobot.userId,
          );
        break;
    }

    const result = await this.shortFunctions.shortAutoBot(
      autobot,
      newAdvice,
      accountInfo,
    );

    // Докупка после продажи
    if (result && autobot.fillPortfolioAfterSell) {
      this.logger.logOrders(
        newAdvice.symbol,
        autobot.exchange,
        '-> Fill portfolio after selling',
      );

      await this.buySellService.fillPortfolio(autobot, newAdvice, accountInfo);
    }
  }
}
