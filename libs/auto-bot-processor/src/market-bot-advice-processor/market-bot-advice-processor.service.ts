import { Injectable } from '@nestjs/common';
import { FEE } from '../../../../utils/Binance';
import { DbMarketBotService } from '@app/database/db-market-bot/db-market-bot.service';
import { SendMailService } from '@app/send-mail';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { serverUrl } from '../../../../utils/currentServer';
import PriceCalculation from '../../../../utils/PriceCalculation';
import { last } from 'ramda';
import { BinanceNewOrderService } from '@app/binance-api/binance-new-order/binance-new-order.service';
import { v4 } from 'uuid';
import { DbExchangeInfoService } from '@app/database/db-exchange-info/db-exchange-info.service';
import { DbMarketBotTradeService } from '@app/database/db-market-bot-trade/db-market-bot-trade.service';
import { BinanceAccountInfoService } from '@app/binance-api/binance-account-info/binance-account-info.service';
import { DbKeyService } from '@app/database/db-key/db-key.service';
import { DbMarketBotTradeDocument } from '@app/database/db-market-bot-trade/db-market-bot-trade.model';
import {
  DbMarketBot,
  DbMarketBotDocument,
} from '@app/database/db-market-bot/db-market-bot.model';
import { DbKey } from '@app/database/db-key/db-key.model';
import { DbUserService } from '@app/database/db-user/db-user.service';
import { DbIndicatorDocument } from '@app/database/db-indicator/db-indicator.model';
import { GateioNewOrderService } from '@app/gateio-api/gateio-new-order/gateio-new-order.service';
import { Exchange } from '../../../../types/exchanges';
import { Order } from 'binance-api-node';
import { GateioAccountInfoService } from '@app/gateio-api/gateio-account-info/gateio-account-info.service';

@Injectable()
export class MarketBotAdviceProcessorService {
  constructor(
    private marketBotService: DbMarketBotService,
    private marketBotTradeService: DbMarketBotTradeService,
    private binanceAccountInfoService: BinanceAccountInfoService,
    private gateioAccountInfoService: GateioAccountInfoService,
    private sendMailService: SendMailService,
    private keyService: DbKeyService,
    private logger: DbLogService,
    private binanceNewOrderService: BinanceNewOrderService,
    private gateioNewOrderService: GateioNewOrderService,
    private exchangeInfoService: DbExchangeInfoService,
    private userService: DbUserService,
  ) {}

  processSimulation = async (
    matchedBot: DbMarketBotDocument,
    advicedData: DbIndicatorDocument,
  ) => {
    try {
      // 1. Register new trade
      if (matchedBot.position !== advicedData.action) {
        const newTrade: any = {
          tradeDate: new Date(),
          position: advicedData.action,
        };
        await this.marketBotService.dbAddNewTradeToMarketBot(
          matchedBot._id,
          matchedBot.autobotId,
          matchedBot.symbol,
          newTrade,
          matchedBot.exchange,
        );

        const { price } = advicedData;

        /**
         * Send Mail Notifications to users with registered bots
         */
        await this.sendMailService.sendMailNotification(
          matchedBot.user,
          `Moonbot: New trade advice on ${matchedBot.symbol}`,

          `Symbol: ${advicedData.symbol}\n` +
            `Position: ${advicedData.action}\n` +
            `Strategy: ${advicedData.indicator}\n` +
            `Interval: ${advicedData.interval}\n` +
            `Current Price: ${price}`,
        );
      }
    } catch (e) {
      this.logger.errorOrders('processSimulation', e);
    }
  };

  processShort = async ({
    marketBot,
    sellPrice,
    sellAmount,
    userId,
    binanceKeys: apiKeys,
    limitSell,
    advice,
    symbol,
    strategy,
    lastPrice,
    percentDiff,
  }) => {
    // Sell if price is positive or sellInMinus is true
    // Subtract 0.075 + 0.075 (BNB) for binance commision
    if (percentDiff - FEE >= 0 || marketBot.sellInMinus) {
      // Execute trade
      const limitRequest = {
        symbol,
        side: 'SELL',
        type: 'LIMIT',
        price: Number(sellPrice).toFixed(8),
        timeInForce: 'GTC',
        quantity: sellAmount,
        recvWindow: 10000,
      };

      const marketRequest = {
        symbol,
        side: 'SELL',
        type: 'MARKET',
        quantity: sellAmount,
        recvWindow: 10000,
      };

      let response = null;

      try {
        const exchange = await this.userService.getUserExchange(userId);

        this.logger.logOrders(symbol, exchange, '-> Executing Short Trade');
        let orderResponse = null;

        switch (exchange) {
          case Exchange.BINANCE:
            orderResponse = await this.binanceNewOrderService.newOrder(
              userId,
              apiKeys,
              limitSell ? limitRequest : marketRequest,
            );
            break;
          case Exchange.GATEIO:
            const exchangeInfo =
              (await this.exchangeInfoService.getLatestExchangeInfo(exchange, {
                symbol,
              })) as any;
            orderResponse = await this.gateioNewOrderService.newOrder(
              userId,
              apiKeys,
              exchangeInfo,
              sellPrice,
              limitSell
                ? {
                    ...limitRequest,
                    price: Number(sellPrice),
                    quantityUsd: Number(sellPrice) * Number(sellAmount),
                  }
                : {
                    ...marketRequest,
                    quantityUsd: Number(sellPrice) * Number(sellAmount),
                  },
            );
        }

        // Save Trade to db
        const newTrade: any = {
          id: v4(),
          tradeDate: new Date(),
          position: advice,
          amount: sellAmount,
          orderResponse,
        };

        response = await this.marketBotService.dbAddNewTradeToMarketBot(
          marketBot._id,
          marketBot.autobotId,
          symbol,
          newTrade,
          exchange,
        );
      } catch (e) {
        this.logger.errorOrders(`Error on SHORT of ${symbol}`, e);
        // await sendErrorMail(marketBot.user,
        //     `Moonbot: Error on SHORT of ${symbol}`,
        //     "Short order",
        //     {
        //         "Asset:": `<a href="${serverUrl}/coin/${symbol}">${symbol}</a>`,
        //         "Position:": `${advice}`,
        //         "Type:": `${limitSell ? 'LIMIT' : 'MARKET'}`,
        //         "Strategy:": `${strategy}`,
        //         "Buy Price:": `${Number(lastPrice).toFixed(8)}`,
        //         "Sell Price:": `${Number(sellPrice).toFixed(8)}`,
        //         "Amount:": `${sellAmount}`,
        //         "Percent:": `<span class="accent">${(percentDiff - FEE).toFixed(2)}%</span>`,
        //         "Timestamp:": `${new Date().toLocaleString('de-DE')}`,
        //         "Error": JSON.stringify(e)
        //     });
      }

      /**
       * Send Mail Notifications to users with registered bots
       */
      try {
        await this.sendMailService.sendOrderMail(
          marketBot.user,
          `Moonbot: SHORT on ${marketBot.symbol}`,
          'Short order',
          {
            'Symbol:': `<a href="${serverUrl}/coin/${symbol}">${marketBot.symbol}</a>`,
            'Position:': `${advice}`,
            'Type:': `${limitSell ? 'LIMIT' : 'MARKET'}`,
            'Strategy:': `${strategy}`,
            'Buy Price:': `${Number(lastPrice).toFixed(8)}`,
            'Sell Price:': `${Number(sellPrice).toFixed(8)}`,
            'Amount:': `${sellAmount}`,
            'Percent:': `<span class="accent">${(percentDiff - FEE).toFixed(2)}%</span>`,
            'Timestamp:': `${new Date().toLocaleString('de-DE')}`,
          },
          false,
        );
      } catch (e) {
        this.logger.errorOrders(e);
      }

      return response;
    } else {
      try {
        const symbol = marketBot.symbol;
        await this.sendMailService.sendOrderMail(
          marketBot.user,
          `Moonbot: NOT EXECUTED because buyPrice < sellPrice ${symbol}`,
          `NOT EXECUTED because buyPrice < sellPrice ${symbol}`,
          {
            'Symbol:': `<a href="${serverUrl}/coin/${symbol}">${symbol}</a>`,
            'Position:': `${advice}`,
            'Type:': `${limitSell ? 'LIMIT' : 'MARKET'}`,
            'Strategy:': `${strategy}`,
            'Buy Price:': `${Number(lastPrice).toFixed(8)}`,
            'Sell Price:': `${Number(sellPrice).toFixed(8)}`,
            'Amount:': `${sellAmount}`,
            'Percent:': `${(percentDiff - FEE).toFixed(2)}%`,
            'Timestamp:': `${new Date().toLocaleString('de-DE')}`,
          },
          true,
        );
      } catch (e) {
        this.logger.errorOrders(e);
      }
    }
  };

  processLong = async (
    marketBot: DbMarketBotDocument,
    advicedPrice: number,
    userId: string,
    keys: DbKey,
    advice: string,
    symbol: string,
    strategy: string,
    prediction,
  ) => {
    try {
      const exchange = await this.userService.getUserExchange(userId);
      const investedUsdt = marketBot.amountUsdtInvested;
      const exchangeSymbol = await this.exchangeInfoService.getOneUsdtSymbol(
        marketBot.symbol,
        exchange,
      );
      const buyAmount = PriceCalculation.getFixedAmount(
        investedUsdt / advicedPrice,
        exchangeSymbol,
      );

      this.logger.logOrders(
        marketBot.symbol,
        exchange,
        '-> Executing Long trade',
      );

      // Execute trade
      let orderResponse: Order;

      switch (exchange) {
        case Exchange.GATEIO:
          const exchangeInfo =
            (await this.exchangeInfoService.getLatestExchangeInfo(exchange, {
              symbol: marketBot.symbol,
            })) as any;
          orderResponse = await this.gateioNewOrderService.newOrder(
            userId,
            keys,
            exchangeInfo,
            advicedPrice,
            {
              symbol: marketBot.symbol,
              side: 'BUY',
              type: 'MARKET',
              quantity: buyAmount,
              quantityUsd: buyAmount * advicedPrice,
              recvWindow: 10000,
            },
          );
          break;
        case Exchange.BINANCE:
        default:
          orderResponse = await this.binanceNewOrderService.newOrder(
            userId,
            keys,
            {
              symbol: marketBot.symbol,
              side: 'BUY',
              type: 'MARKET',
              quantity: buyAmount,
              recvWindow: 10000,
            },
          );
          break;
      }

      const newTrade: any = {
        tradeDate: new Date(),
        position: advice,
        amount: buyAmount,
        orderResponse,
      };
      await this.marketBotService.dbAddNewTradeToMarketBot(
        marketBot._id,
        marketBot.autobotId,
        marketBot.symbol,
        newTrade,
        marketBot.exchange,
      );

      // Сохраняем действительную цену покупки / продажи в трэйде. Если не можен определить, берем актуальную цену.
      const lastFillPrice = Number(last(orderResponse.fills).price);
      const symbol = marketBot.symbol;
      await this.sendMailService.sendOrderMail(
        marketBot.user,
        `Moonbot: LONG on ${marketBot.symbol}`,
        'Long order',
        {
          'Symbol:': `<a href="${serverUrl}/coin/${symbol}">${marketBot.symbol}</a>`,
          'Position:': `${advice}`,
          'Strategy:': `${strategy}`,
          'Adv. Price:': `${Number(advicedPrice).toFixed(8)}`,
          'Buy Price:': `${lastFillPrice.toFixed(8)}`,
          'Prediction:': `${Number(prediction).toFixed(2)}`,
          'Amount:': `${buyAmount}`,
          'Timestamp:': `${new Date().toLocaleString('de-DE')}`,
        },
        false,
      );
    } catch (e) {
      this.logger.errorOrders(e.message, e);
    }
  };

  processManualOrder = async (
    matchedBot: DbMarketBotDocument,
    advicedData: DbIndicatorDocument,
  ) => {
    try {
      const { price, indicator, symbol, action } = advicedData;
      const advicedPrice = Number(price);

      // New advice
      if (matchedBot.position !== action) {
        const userId = matchedBot.user;

        // Get API Keys from user's bot
        const binanceKeys = await this.keyService.getApiKey(
          userId,
          matchedBot.exchange,
        );

        // Get user's coin balance
        let accountInfo = null;
        switch (matchedBot.exchange) {
          case Exchange.BINANCE:
            accountInfo = await this.binanceAccountInfoService.getAccountInfo(
              binanceKeys,
              {
                noSmallAmount: false,
              },
            );
            break;
          case Exchange.GATEIO:
            accountInfo = await this.gateioAccountInfoService.getAccountInfo(
              binanceKeys,
              {
                noSmallAmount: false,
              },
            );
            break;
        }

        // Account balance
        const { balances } = accountInfo;
        const asset = matchedBot.symbol;

        // Coin balance
        const coinBalance = balances.find((x) => x.asset === asset) || null;

        // Last Trade
        const lastTrade =
          (await this.marketBotTradeService.dbGetLastMarketBotTrade(
            matchedBot._id.toString(),
          )) || ({} as DbMarketBotTradeDocument);
        const lastPrice = Number(lastTrade.price);
        const priceDiff = advicedPrice - lastPrice;
        const percentDiff = PriceCalculation.toPercentGain(
          lastPrice,
          advicedPrice,
        );

        // Free balance
        if (!coinBalance && action === 'short') {
          this.logger.logOrders(
            'No coin in portfolio for automatic trade.',
            matchedBot.exchange,
            userId,
            matchedBot.symbol,
          );

          try {
            await this.sendMailService.sendOrderMail(
              matchedBot.user,
              `Moonbot: NOT EXECUTED because no coin in portfolio for automatic trade. ${matchedBot.symbol}`,
              `NOT EXECUTED because no coin in portfolio for automatic trade. ${matchedBot.symbol}`,
              {
                'Symbol:': `<a href="${serverUrl}/coin/${asset}">${matchedBot.symbol}</a>`,
                'Position:': `${action}`,
                'Strategy:': `${indicator}`,
                'Buy Price:': `${lastPrice.toFixed(8)}`,
                'Sell Price:': `${advicedPrice.toFixed(8)}`,
                'Amount:': '$0',
                'Percent:': `${percentDiff.toFixed(2)}%`,
                'Timestamp:': `${new Date().toLocaleString('de-DE')}`,
              },
              true,
            );
          } catch (e) {
            this.logger.errorOrders(e);
          }
          return;
        }

        // Use limit sell instead of market
        const { limitSell } = matchedBot;

        const exchangeInfoForSymbol =
          await this.exchangeInfoService.getLatestExchangeInfo(
            matchedBot.exchange,
            {
              base: 'USDT',
              symbol,
            },
          );

        const freeBalance = PriceCalculation.getFixedAmount(
          Number(coinBalance.free),
          last(exchangeInfoForSymbol),
        );

        if (action === 'short') {
          await this.processShort({
            marketBot: matchedBot,
            sellPrice: advicedPrice,
            sellAmount: freeBalance,
            userId,
            binanceKeys,
            limitSell,
            advice: action,
            symbol,
            strategy: indicator,
            lastPrice,
            percentDiff,
          });
        } else if (action === 'long') {
          await this.processLong(
            matchedBot,
            advicedPrice,
            userId,
            binanceKeys,
            action,
            symbol,
            indicator,
            0,
          );
        }
      }
    } catch (e) {
      this.logger.errorOrders('processRealBot', e);
    }
  };

  processManualOrders = async (
    adviceData: DbIndicatorDocument,
    autobot: DbMarketBot,
  ) => {
    try {
      const { symbol, indicator } = adviceData;
      const matchedBots = await this.marketBotService.dbGetManualMarketBots(
        adviceData.exchange,
        symbol,
        indicator,
      );

      // 2. Go through all active bots and check if they are simulated or not
      for (const matchedBot of matchedBots) {
        const { simulation } = matchedBot;

        if (simulation) {
          await this.processSimulation(matchedBot, adviceData);
        } else {
          await this.processManualOrder(matchedBot, adviceData);
        }
      }
    } catch (e) {
      this.logger.errorOrders('processMarketBots', e);
    }
  };
}
