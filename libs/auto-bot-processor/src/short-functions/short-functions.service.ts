import { Injectable } from '@nestjs/common';
import { DbAutoBotDocument } from '@app/database/db-auto-bot/db-auto-bot.model';
import { DbMarketBotDocument } from '@app/database/db-market-bot/db-market-bot.model';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbMarketBotService } from '@app/database/db-market-bot/db-market-bot.service';
import PriceCalculation from '../../../../utils/PriceCalculation';
import { DbMarketBotTrade } from '@app/database/db-market-bot-trade/db-market-bot-trade.model';
import { DbMarketBotTradeService } from '@app/database/db-market-bot-trade/db-market-bot-trade.service';
import { serverUrl } from '../../../../utils/currentServer';
import { DbKeyService } from '@app/database/db-key/db-key.service';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import { MarketBotAdviceProcessorService } from '@app/auto-bot-processor/market-bot-advice-processor/market-bot-advice-processor.service';
import { SendMailService } from '@app/send-mail';
import { MbAccountInfo } from '@app/binance-api/binance-account-info/binance-account-info.service';
import { DbIndicator } from '@app/database/db-indicator/db-indicator.model';
import { sleep } from '../../../../utils/wait';

@Injectable()
export class ShortFunctionsService {
  constructor(
    private logger: DbLogService,
    private marketBotService: DbMarketBotService,
    private marketBotTradeService: DbMarketBotTradeService,
    private keyService: DbKeyService,
    private coinPriceService: DbCoinPriceService,
    private marketBotAdviceProcessorService: MarketBotAdviceProcessorService,
    private sendMailService: SendMailService,
  ) {}

  shortAutoBot = async (
    autobot: DbAutoBotDocument,
    newAdvice: DbIndicator,
    accountInfo: MbAccountInfo,
  ) => {
    const marketBot =
      await this.marketBotService.dbGetMarketBotForAutobotAndSymbol(
        autobot._id.toString(),
        newAdvice.symbol,
      );

    // Найден активный коин
    if (!marketBot) {
      this.logger.logOrders(
        newAdvice.symbol,
        newAdvice.exchange,
        '-> No market bot found with symbol',
        newAdvice.symbol,
      );
      await this.marketBotService.dbDeleteMarketBotBySymbol(
        autobot.userId,
        newAdvice.symbol,
        autobot.exchange,
      );

      return false;
    }

    // Prevent sell in minus
    const lastTrade =
      (await this.marketBotTradeService.dbGetLastMarketBotTrade(
        marketBot._id.toString(),
      )) || ({} as DbMarketBotTrade);
    const diff = PriceCalculation.toPercentGain(
      lastTrade.price,
      newAdvice.price,
    );

    if (!autobot.enableSellInMinus && diff < 0) {
      this.logger.warnOrders(
        newAdvice.symbol,
        '-> sellInMinus is disabled and profit is negative (',
        diff,
        '%). Stopping here',
      );
      return false;
    }

    try {
      // Бот удаляется при шорте и продает по маркету и профит записывается в профит авто трэйд системы
      const { percentDiff, savedMarketBot, sellAmount } = await this.sellBot(
        newAdvice,
        autobot,
        marketBot,
        accountInfo,
      );

      if (savedMarketBot) {
        // 0.25 + 0.25 commission
        await this.addProfitToHistory({
          profit: percentDiff,
          sellAmount,
          newAdvice,
          autobot,
          marketBot: savedMarketBot,
        });
      }

      await this.removeBotFromAutoBot(autobot, marketBot);
      await this.deleteBot(autobot, marketBot);

      await sleep(() => {});
      return true;
    } catch (e) {
      /**
       * Send Mail Notifications to users with registered bots
       */
      try {
        const symbol = marketBot.symbol;
        await this.sendMailService.sendOrderMail(
          marketBot.user,
          `Moonbot: Failed Short on ${marketBot.symbol}`,
          'Failed SHORT',
          {
            'Symbol:': `<a href="${serverUrl}/coin/${symbol}">${marketBot.symbol}</a>`,
            'Position:': 'short',
            'Type:': 'MARKET',
            'Strategy:': `${autobot.strategy}`,
            'Timestamp:': `${new Date().toLocaleString('de-DE')}`,
            'Error:': e,
          },
          true,
        );
      } catch (ee) {
        this.logger.errorOrders(ee);
      }

      this.logger.errorOrders(e);

      await this.removeBotFromAutoBot(autobot, marketBot);
      await this.deleteBot(autobot, marketBot);

      return false;
    }
  };
  sellBot = async (
    newAdvice: DbIndicator,
    autobot: DbAutoBotDocument,
    marketBot: DbMarketBotDocument,
    accountInfo: MbAccountInfo,
  ) => {
    const apiKeys = await this.keyService.getApiKey(
      autobot.userId,
      autobot.exchange,
    );

    const lastTrade =
      (await this.marketBotTradeService.dbGetLastMarketBotTrade(
        marketBot._id.toString(),
      )) || ({} as DbMarketBotTrade);
    const percentDiff = PriceCalculation.toPercentGain(
      lastTrade.price,
      newAdvice.price,
    );

    const { balances } = accountInfo;
    const asset = marketBot.symbol.replace('USDT', '');
    const assetBalance: any = balances.find((x) => x.asset === asset) || {};

    if (!assetBalance) {
      this.logger.errorOrders(
        marketBot.symbol,
        '-> Problem on sell bot. Coin balance not found',
      );
      throw new Error(
        marketBot.symbol + ' -> Problem on sell bot. Coin balance not found',
      );
    }

    let tradeAmount = lastTrade.orderResponse?.finalQty
      ? lastTrade.orderResponse?.finalQty
      : lastTrade.orderResponse?.executedQty;
    if (Number(assetBalance) < tradeAmount) {
      tradeAmount = assetBalance;
    }

    this.logger.logOrders(
      marketBot.symbol,
      marketBot.exchange,
      '-> Processing Short Market Trade | Amount:',
      assetBalance.free,
    );
    const savedMarketBot =
      await this.marketBotAdviceProcessorService.processShort({
        marketBot,
        sellPrice: newAdvice.price,
        sellAmount: tradeAmount,
        userId: autobot.userId,
        binanceKeys: apiKeys,
        limitSell: false,
        advice: newAdvice.action,
        symbol: newAdvice.symbol,
        strategy: newAdvice.indicator,
        lastPrice: lastTrade.price,
        percentDiff,
      });
    return { percentDiff, savedMarketBot, sellAmount: tradeAmount };
  };

  deleteBot = async (
    autobot: DbAutoBotDocument | any,
    matchedMarketBot: DbMarketBotDocument | any,
  ) => {
    try {
      this.logger.logOrders(
        matchedMarketBot.symbol,
        matchedMarketBot.exchange,
        '-> Deleting bot',
        matchedMarketBot._id.toString(),
        'from autobot',
        autobot._id.toString(),
      );

      await this.marketBotService.dbDeleteMarketBotByIdAndUserId(
        matchedMarketBot._id.toString(),
        autobot.userId,
      );
    } catch (e) {
      throw e;
    }
  };

  addProfitToHistory = async ({
    profit,
    sellAmount,
    newAdvice,
    autobot,
    marketBot,
  }) => {
    const lastTrade =
      (await this.marketBotTradeService.dbGetLastLongMarketBotTrade(
        marketBot._id.toString(),
      )) || ({} as DbMarketBotTrade);
    const currentCoinPrice = await this.coinPriceService.dbGetCoinPrice(
      newAdvice.symbol,
      autobot.exchange,
    );

    let sellValue = 0;
    let profitUsd = 0;

    if (!isNaN(lastTrade.valueInUsd)) {
      sellValue = currentCoinPrice * sellAmount;
      profitUsd = Number(
        (Number(sellValue) - Number(lastTrade.valueInUsd)).toFixed(2),
      );
    }

    this.logger.logOrders(
      newAdvice.symbol,
      newAdvice.exchange,
      '-> Add profit for autobot',
      autobot._id.toString(),
      '| Sell value:',
      Number(sellValue).toFixed(2) + '$',
      '| Profit:',
      profitUsd.toFixed(2) + '$',
      '| Profit %:',
      profit.toFixed(2) + '%',
    );
  };

  removeBotFromAutoBot = async (
    autobot: DbAutoBotDocument | any,
    matchedMarketBot: DbMarketBotDocument | any,
  ) => {
    await this.marketBotService.dbDeleteMarketBotByIdAndUserId(
      matchedMarketBot._id,
      autobot.userId,
    );

    this.logger.logOrders(
      matchedMarketBot.symbol,
      matchedMarketBot.exchange,
      '-> Remove tradebot',
      matchedMarketBot._id.toString(),
      'from autobot',
      autobot._id.toString(),
    );
  };
}
