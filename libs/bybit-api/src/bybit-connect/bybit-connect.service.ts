import { Injectable } from '@nestjs/common';
import {
  RestClientV5,
  WebsocketClient,
  WSClientConfigurableOptions,
} from 'bybit-api';

@Injectable()
export class BybitConnectService {
  private useTestnet = false;

  bybitRestInit(key?: string, secret?: string) {
    if (!key || !secret) {
      return new RestClientV5();
    }

    return new RestClientV5({
      key: key,
      secret: secret,
      testnet: this.useTestnet,
    });
  }

  bybitWSInit(key?: string, secret?: string) {
    const wsConfig = {
      market: 'v5',
    } as WSClientConfigurableOptions;

    if (!key || !secret) {
      return new WebsocketClient(wsConfig);
    }

    wsConfig.key = key;
    wsConfig.secret = secret;

    return new WebsocketClient(wsConfig);
  }
}
