import { Injectable } from '@nestjs/common';
import { BinanceConnectService } from '@app/binance-api/binance-connect/binance-connect.service';

@Injectable()
export class BinanceTradesService {
  constructor(private connectService: BinanceConnectService) {}

  async getBinanceTrades(apiKey, args) {
    const client = this.connectService.init(apiKey.key, apiKey.secret);

    if (!(args.symbol === 'BTCBTC') && !(args.symbol === 'USDTBTC')) {
      try {
        return await client.trades(args);
      } catch (e) {
        throw e;
      }
    }

    return null;
  }
}
