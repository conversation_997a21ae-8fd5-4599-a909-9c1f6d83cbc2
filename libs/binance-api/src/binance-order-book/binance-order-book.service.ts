import { Injectable } from '@nestjs/common';
import { BinanceConnectService } from '@app/binance-api/binance-connect/binance-connect.service';
import { DbK<PERSON> } from '@app/database/db-key/db-key.model';

@Injectable()
export class BinanceOrderBookService {
  constructor(private connectService: BinanceConnectService) {}

  async getOrderBook(
    apiKey: DbKey,
    args: GQL.IOrderbookOnExchangeQueryArguments,
  ) {
    const client = this.connectService.init(apiKey.key, apiKey.secret);
    return client.book(args);
  }
}
