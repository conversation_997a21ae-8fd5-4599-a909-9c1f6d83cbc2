import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { DbCoinPriceService } from '@app/database/db-coin-price/db-coin-price.service';
import { BinanceConnectService } from '@app/binance-api/binance-connect/binance-connect.service';
import { Exchange } from '../../../../types/exchanges';

@Injectable()
export class BinancePricesService {
  constructor(
    private logger: DbLogService,
    private coinPriceService: DbCoinPriceService,
    private binanceConnectService: BinanceConnectService,
  ) {}

  async getPrices() {
    try {
      const client = this.binanceConnectService.init();
      const binancePrices = await client.prices();

      for (const symbol of Object.keys(binancePrices)) {
        try {
          await this.coinPriceService.dbSetCurrentCoinPrice(
            symbol,
            Number(binancePrices[symbol]),
            Exchange.BINANCE,
          );
        } catch (e) {
          this.logger.errorServer(symbol, e);
        }
      }
    } catch (e) {
      this.logger.errorServer(e);
    }

    return null;
  }
}
