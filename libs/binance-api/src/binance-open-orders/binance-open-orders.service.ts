import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { BinanceConnectService } from '@app/binance-api/binance-connect/binance-connect.service';

@Injectable()
export class BinanceOpenOrdersService {
  constructor(
    private logger: DbLogService,
    private connectService: BinanceConnectService,
  ) {}

  async getOpenOrders(apiKey, args: any) {
    const client = this.connectService.init(apiKey.key, apiKey.secret);
    try {
      const query: any = {};

      if (args && args.symbol) {
        query.symbol = args.symbol;
      }

      return client.openOrders(query);
    } catch (e) {
      this.logger.errorServer(e);
      return [];
    }
  }
}
