import { Injectable } from '@nestjs/common';
import { CandlesOptions } from 'binance-api-node';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { BinanceConnectService } from '@app/binance-api/binance-connect/binance-connect.service';

@Injectable()
export class BinanceCandleSticksService {
  constructor(
    private logger: DbLogService,
    private connectService: BinanceConnectService,
  ) {}

  /**
   * [{
   openTime: 1508328900000,
   open: '0.05655000',
   high: '0.05656500',
   low: '0.05613200',
   close: '0.05632400',
   volume: '68.88800000',
   closeTime: 1508329199999,
   quoteAssetVolume: '2.29500857',
   trades: 85,
   baseAssetVolume: '40.61900000'
   }]
   */
  async getCandleSticks(args: CandlesOptions) {
    const client = this.connectService.init();
    try {
      return await client.candles(args);
    } catch (e) {
      this.logger.errorServer(e.message);
    }

    return null;
  }
}
