import { Injectable } from '@nestjs/common';
import { DbLogService } from '@app/database/db-log/db-log.service';
import { BinanceConnectService } from '@app/binance-api/binance-connect/binance-connect.service';

@Injectable()
export class BinanceExchangeInfoService {
  constructor(
    private logger: DbLogService,
    private connect: BinanceConnectService,
  ) {}

  async getExchangeInfo() {
    const client = this.connect.init();
    try {
      return await client.exchangeInfo();
    } catch (e) {
      this.logger.errorServer(e);
    }

    return null;
  }
}
