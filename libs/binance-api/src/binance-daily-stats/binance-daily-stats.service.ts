import { Injectable } from '@nestjs/common';
import { BinanceConnectService } from '@app/binance-api/binance-connect/binance-connect.service';

@Injectable()
export class BinanceDailyStatsService {
  constructor(private connectService: BinanceConnectService) {}

  async getDailyStats(args?: GQL.IDailyStatsOnExchangeQueryArguments) {
    const client = this.connectService.init();

    if (args) {
      return client.dailyStats({
        symbol: args.symbol,
      });
    }

    return client.dailyStats();
  }
}
