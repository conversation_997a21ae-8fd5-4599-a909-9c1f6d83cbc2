import { BinanceConnectService } from '@app/binance-api/binance-connect/binance-connect.service';
import { Db<PERSON><PERSON> } from '@app/database/db-key/db-key.model';
import { Injectable } from '@nestjs/common';

@Injectable()
export class BinanceWsUserService {
  constructor(private connectService: BinanceConnectService) {}

  user(callback: any, apiKey: Db<PERSON><PERSON>) {
    const client = this.connectService.init(apiKey.key, apiKey.secret);

    client.ws.user(callback).then((onfulfilled) => {
      //
    });
  }
}
