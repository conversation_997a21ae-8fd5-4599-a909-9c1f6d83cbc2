import os
from decimal import Decimal
from typing import List, Dict, Any

import numpy as np
import pandas as pd
from hummingbot.connector.connector_base import ConnectorBase
from hummingbot.core.clock import Clock
from pydantic import Field, validator

from hummingbot.client.config.config_data_types import ClientFieldData
from hummingbot.core.data_type.common import OrderType, TradeType, PositionMode, PriceType
from hummingbot.core.data_type.order_candidate import OrderCandidate
from hummingbot.data_feed.candles_feed.candles_factory import CandlesConfig
from hummingbot.strategy.strategy_v2_base import StrategyV2ConfigBase, StrategyV2Base
from hummingbot.strategy_v2.executors.position_executor.data_types import PositionExecutorConfig, TripleBarrierConfig
from hummingbot.strategy_v2.models.executor_actions import CreateExecutorAction, StopExecutorAction


class AOCrossConfig(StrategyV2ConfigBase):
    script_file_name: str = Field(default_factory=lambda: os.path.basename(__file__))
    markets: Dict[str, List[str]] = {}
    candles_config: List[CandlesConfig] = []
    controllers_config: List[str] = []
    exchange: str = Field(default="gate_io", client_data=ClientFieldData(
        prompt_on_new=True, prompt=lambda mi: "Exchange where the bot will trade"))
    trading_pair: str = Field(default="BTC5L-USDT", client_data=ClientFieldData(
        prompt_on_new=True, prompt=lambda mi: "Trading pair where the bot will trade"))
    candles_pair: str = Field(default="BTC-USDT", client_data=ClientFieldData(
        prompt_on_new=True, prompt=lambda mi: "Trading pair where candles are fetched from"))
    candles_interval: str = Field(default="2h", client_data=ClientFieldData(
        prompt_on_new=True, prompt=lambda mi: "Candle interval (e.g. 1m for 1 minute)"))
    order_amount: Decimal = Field(default=0.01, gt=0, client_data=ClientFieldData(
        prompt_on_new=True, prompt=lambda mi: "Order amount in % of a balance"))
    position_mode: PositionMode = Field(default="ONEWAY", client_data=ClientFieldData(
        prompt_on_new=True, prompt=lambda mi: "Position mode (HEDGE/ONEWAY)"))

    # Triple Barrier Configuration
    stop_loss: Decimal = Field(default=Decimal("0.5"), gt=0, client_data=ClientFieldData(
        prompt_on_new=True, prompt=lambda mi: "Position stop loss (e.g. 0.03 for 3%)"))
    take_profit: Decimal = Field(default=Decimal("3.0"), gt=0, client_data=ClientFieldData(
        prompt_on_new=True, prompt=lambda mi: "Position take profit (e.g. 0.01 for 1%)"))
    time_limit: int = Field(default=60 * 5000, gt=0, client_data=ClientFieldData(
        prompt_on_new=True, prompt=lambda mi: "Position time limit in seconds (e.g. 300 for 5 minutes)"))
    leverage: int = Field(default=1, gt=0, client_data=ClientFieldData(
        prompt_on_new=True, prompt=lambda mi: "Leverage"))
    cooldown: int = Field(default=7200, gt=0, client_data=ClientFieldData(
        prompt_on_new=True, prompt=lambda mi: "Cooldown in seconds"
    ))

    @property
    def triple_barrier_config(self) -> TripleBarrierConfig:
        return TripleBarrierConfig(
            stop_loss=self.stop_loss,
            take_profit=self.take_profit,
            time_limit=self.time_limit,
            open_order_type=OrderType.MARKET,
            take_profit_order_type=OrderType.MARKET,
            stop_loss_order_type=OrderType.MARKET,
            time_limit_order_type=OrderType.MARKET,
        )

    @validator('position_mode', pre=True, allow_reuse=True)
    def validate_position_mode(cls, v: str) -> PositionMode:
        if v.upper() in PositionMode.__members__:
            return PositionMode[v.upper()]
        raise ValueError(f"Invalid position mode: {v}. Valid options are: {', '.join(PositionMode.__members__)}")


class AOCross(StrategyV2Base):
    candle_size = 100
    cooldown = 0
    last_action = TradeType.SELL
    candles: Any
    results_df = None
    fee_bps = 10

    @classmethod
    def init_markets(cls, config: AOCrossConfig):
        cls.markets = {config.exchange: {config.trading_pair}}

    def __init__(self, connectors: Dict[str, ConnectorBase], config: AOCrossConfig):
        self._last_timestamp = None
        if len(config.candles_config) == 0:
            config.candles_config.append(CandlesConfig(
                connector=config.exchange,
                trading_pair=config.trading_pair,
                interval=config.candles_interval,
                max_records=self.candle_size
            ))
        super().__init__(connectors, config)
        self.config = config

    def start(self, clock: Clock, timestamp: float) -> None:
        """
        Start the strategy.
        :param clock: Clock to use.
        :param timestamp: Current time.
        """
        self._last_timestamp = timestamp

    def create_actions_proposal(self) -> List[CreateExecutorAction]:
        create_actions = []
        # if self.cooldown > 0:
        #     self.cooldown = self.cooldown - 1
        #
        #     if self.cooldown % 400 == 0:
        #         print("Cooling down... " + str(self.cooldown))
        #
        #     return create_actions

        signal = self.get_signal()

        if signal is not None:
            price = self.market_data_provider.get_price_by_type(self.config.exchange,
                                                                self.config.trading_pair,
                                                                PriceType.MidPrice)

            if signal == 1 and self.last_action == TradeType.SELL and price > Decimal(0):
                amount = self.get_amount(TradeType.BUY, price)
                if amount <= 0:
                    return []

                self.cooldown = self.config.cooldown / 5
                self.last_action = TradeType.BUY

                create_actions.append(CreateExecutorAction(
                    executor_config=PositionExecutorConfig(
                        timestamp=self.current_timestamp,
                        connector_name=self.config.exchange,
                        trading_pair=self.config.trading_pair,
                        side=TradeType.BUY,
                        entry_price=price,
                        amount=amount,
                        triple_barrier_config=self.config.triple_barrier_config,
                    )))

            elif signal == -1 and self.last_action == TradeType.BUY and price > Decimal(0):
                amount = self.get_amount(TradeType.SELL, price)
                self.cooldown = self.config.cooldown / 5
                self.last_action = TradeType.SELL

                create_actions.append(CreateExecutorAction(
                    executor_config=PositionExecutorConfig(
                        timestamp=self.current_timestamp,
                        connector_name=self.config.exchange,
                        trading_pair=self.config.trading_pair,
                        side=TradeType.SELL,
                        entry_price=price,
                        amount=amount,
                        triple_barrier_config=self.config.triple_barrier_config,
                    )))

                print("Selling " + self.config.trading_pair)

        return create_actions

    def get_amount(self, position, mid_price):
        proposal: OrderCandidate = self.create_proposal(position, mid_price)
        proposal_adjusted = self.connectors[self.config.exchange].budget_checker.adjust_candidates([proposal],
                                                                                                   all_or_none=True)
        balance = self.connectors[self.config.exchange].budget_checker._get_available_balances(proposal_adjusted[0])
        usdt = 0
        if position == TradeType.BUY:
            if "USDT" in balance:
                usdt_balance = Decimal(balance["USDT"])
                usdt = usdt_balance * self.config.order_amount
                amount = usdt / mid_price
            else:
                amount = 0

            self.notify_hb_app_with_timestamp(f"""
                    Creating new trade:
                    Position: {position.name}
                    Amount: {amount}
                    Amount USDT: {usdt}""")
        else:
            asset = self.config.trading_pair.split("-")[0]
            if asset in balance:
                asset_balance = Decimal(balance[asset])
                amount = asset_balance
            else:
                amount = 0
            amount_usdt = amount * mid_price
            self.notify_hb_app_with_timestamp(f"""
                    Creating new trade:
                    Position: {position.name}
                    Amount: {amount}
                    Amount USDT: {amount_usdt}""")

        return amount

    def get_active_executors_by_side(self, connector_name: str, trading_pair: str):
        active_executors_by_connector_pair = self.filter_executors(
            executors=self.get_all_executors(),
            filter_func=lambda e: e.connector_name == connector_name and e.trading_pair == trading_pair and e.is_active
        )
        active_longs = [e for e in active_executors_by_connector_pair if e.side == TradeType.BUY]
        return active_longs

    def stop_actions_proposal(self) -> List[StopExecutorAction]:
        stop_actions = []
        return stop_actions

    def get_signal(self) -> int:
        indicator_value = self.get_indicator_value()
        if indicator_value > 0:
            signal_value = 1
        elif indicator_value < 0:
            signal_value = -1
        else:
            signal_value = 0
        return signal_value

    def get_indicator_value(self) -> float:
        candles = self.market_data_provider.get_candles_df(self.config.exchange,
                                                           self.config.candles_pair,
                                                           self.config.candles_interval,
                                                           self.candle_size)
        candles.ta.ao(fast=5, slow=34, append=True)
        if "AO_5_34" in candles:
            last_candle = candles.iloc[-1]
            ao = last_candle["AO_5_34"]
            return ao

        return 0

    def create_proposal(self, position, mid_price) -> OrderCandidate:
        return OrderCandidate(
            trading_pair=self.config.trading_pair,
            is_maker=False,
            order_type=OrderType.MARKET,
            order_side=position,
            amount=Decimal(self.config.order_amount),
            price=mid_price)

    def format_status(self) -> str:
        if not self.ready_to_trade:
            return "Market connectors are not ready."

        original_info = super().format_status()
        extra_info = []
        ao_indicator = self.get_indicator_value()

        extra_info.append(f"AO: {ao_indicator}")

        # Combine original and extra information
        format_status = f"{original_info}\n\n" + "\n".join(extra_info)
        return format_status

    def active_orders_df(self) -> pd.DataFrame:
        """
        Return a data frame of all active orders for displaying purpose.
        """
        columns = ["Exchange", "Market", "Side", "Price", "Amount", "Age"]
        data = []
        for connector_name, connector in self.connectors.items():
            for order in self.get_active_orders(connector_name):
                age_txt = "n/a" if order.age() <= 0. else pd.Timestamp(order.age(), unit='s').strftime('%H:%M:%S')
                data.append([
                    connector_name,
                    order.trading_pair,
                    "buy" if order.is_buy else "sell",
                    float(order.price),
                    float(order.quantity),
                    age_txt
                ])
        if not data:
            raise ValueError
        df = pd.DataFrame(data=data, columns=columns)
        df.sort_values(by=["Exchange", "Market", "Side"], inplace=True)
        return df

    def get_balance_df(self) -> pd.DataFrame:
        """
        Returns a data frame for all asset balances for displaying purpose.
        """
        columns: List[str] = ["Exchange", "Asset", "Total Balance", "Available Balance"]
        data: List[Any] = []
        for connector_name, connector in self.connectors.items():
            for asset in self.get_assets(connector_name):
                data.append([connector_name,
                             asset,
                             float(connector.get_balance(asset)),
                             float(connector.get_available_balance(asset))])
        df = pd.DataFrame(data=data, columns=columns).replace(np.nan, '', regex=True)
        df.sort_values(by=["Exchange", "Asset"], inplace=True)
        return df
