import os
from decimal import Decimal
from typing import Dict, List, Optional

import pandas_ta as ta  # noqa: F401
from pydantic import Field, field_validator

from hummingbot.connector.connector_base import ConnectorBase
from hummingbot.core.clock import Clock
from hummingbot.core.data_type.common import OrderType, PositionMode, PriceType, TradeType
from hummingbot.core.data_type.order_candidate import OrderCandidate
from hummingbot.data_feed.candles_feed.candles_factory import CandlesConfig
from hummingbot.strategy.strategy_v2_base import StrategyV2Base, StrategyV2ConfigBase
from hummingbot.strategy_v2.executors.position_executor.data_types import PositionExecutorConfig, TripleBarrierConfig
from hummingbot.strategy_v2.models.executor_actions import CreateExecutorAction, StopExecutorAction


class SimpleDirectionalAOConfig(StrategyV2ConfigBase):
    script_file_name: str = os.path.basename(__file__)
    markets: Dict[str, List[str]] = {}
    candles_config: List[CandlesConfig] = []
    controllers_config: List[str] = []
    exchange: str = Field(default="gate_io")
    trading_pair: str = Field(default="BTC5L-USD")
    candles_pair: str = Field(default="BTC-USDT")
    candles_interval: str = Field(default="15m")
    candles_length: int = Field(default=60, gt=0)
    ao_fast_length: int = Field(default=5, gt=0)
    ao_slow_length: int = Field(default=34, gt=0)
    order_amount: Decimal = Field(default=Decimal("0.98"), gt=0)  # Relative amount as percentage
    leverage: int = Field(default=1, gt=0)
    position_mode: PositionMode = Field(default="ONEWAY")

    # Triple Barrier Configuration
    stop_loss: Decimal = Field(default=Decimal("0.5"), gt=0)
    take_profit: Decimal = Field(default=Decimal("3.0"), gt=0)
    time_limit: int = Field(default=60 * 50000, gt=0)

    @property
    def triple_barrier_config(self) -> TripleBarrierConfig:
        return TripleBarrierConfig(
            stop_loss=self.stop_loss,
            take_profit=self.take_profit,
            time_limit=self.time_limit,
            open_order_type=OrderType.MARKET,
            take_profit_order_type=OrderType.MARKET,
            stop_loss_order_type=OrderType.MARKET,
            time_limit_order_type=OrderType.MARKET,
        )

    @field_validator('position_mode', mode="before")
    @classmethod
    def validate_position_mode(cls, v: str) -> PositionMode:
        if v.upper() in PositionMode.__members__:
            return PositionMode[v.upper()]
        raise ValueError(f"Invalid position mode: {v}. Valid options are: {', '.join(PositionMode.__members__)}")


class SimpleDirectionalAO(StrategyV2Base):
    """
    This strategy uses AO (Awesome Oscillator) to generate trading signals and execute trades based on the AO values.
    It defines the specific parameters and configurations for the AO strategy.
    """

    account_config_set = False
    last_action = TradeType.SELL

    @classmethod
    def init_markets(cls, config: SimpleDirectionalAOConfig):
        cls.markets = {config.exchange: {config.trading_pair}}

    def __init__(self, connectors: Dict[str, ConnectorBase], config: SimpleDirectionalAOConfig):
        self._last_timestamp = None
        if len(config.candles_config) == 0:
            config.candles_config.append(CandlesConfig(
                connector=config.exchange,
                trading_pair=config.trading_pair,
                interval=config.candles_interval,
                max_records=config.candles_length + 10
            ))
        super().__init__(connectors, config)
        self.config = config
        self.current_ao = None
        self.current_signal = None

    def start(self, clock: Clock, timestamp: float) -> None:
        """
        Start the strategy.
        :param clock: Clock to use.
        :param timestamp: Current time.
        """
        self._last_timestamp = timestamp
        self.apply_initial_setting()

    def create_actions_proposal(self) -> List[CreateExecutorAction]:
        create_actions = []
        signal = self.get_signal()

        if signal is not None:
            price = self.market_data_provider.get_price_by_type(self.config.exchange,
                                                                self.config.trading_pair,
                                                                PriceType.MidPrice)

            # Calculate amount based on percentage of available balance

            if signal == 1 and self.last_action == TradeType.SELL and price > Decimal(0):  # Inverted logic: Buy when signal is -1
                amount = self.get_amount(TradeType.BUY, price)
                if amount <= 0:
                    return []

                self.last_action = TradeType.BUY

                create_actions.append(CreateExecutorAction(
                    executor_config=PositionExecutorConfig(
                        timestamp=self.current_timestamp,
                        connector_name=self.config.exchange,
                        trading_pair=self.config.trading_pair,
                        side=TradeType.BUY,
                        entry_price=price,
                        amount=amount,
                        triple_barrier_config=self.config.triple_barrier_config,
                        leverage=self.config.leverage
                    )))
            elif signal == -1 and self.last_action == TradeType.BUY and price > Decimal(0):
                amount = self.get_amount(TradeType.SELL, price)
                self.last_action = TradeType.SELL

                create_actions.append(CreateExecutorAction(
                    executor_config=PositionExecutorConfig(
                        timestamp=self.current_timestamp,
                        connector_name=self.config.exchange,
                        trading_pair=self.config.trading_pair,
                        side=TradeType.SELL,
                        entry_price=price,
                        amount=amount,
                        triple_barrier_config=self.config.triple_barrier_config,
                        leverage=self.config.leverage
                    )))

                print("Selling " + self.config.trading_pair)

        return create_actions

    def stop_actions_proposal(self) -> List[StopExecutorAction]:
        stop_actions = []
        signal = self.get_signal()
        active_longs, active_shorts = self.get_active_executors_by_side(self.config.exchange,
                                                                        self.config.trading_pair)
        if signal is not None:
            if signal == -1:
                stop_actions.extend([StopExecutorAction(executor_id=e.id) for e in active_longs])
            elif signal == 1:
                stop_actions.extend([StopExecutorAction(executor_id=e.id) for e in active_shorts])
        return stop_actions

    def get_active_executors_by_side(self, connector_name: str, trading_pair: str):
        active_executors_by_connector_pair = self.filter_executors(
            executors=self.get_all_executors(),
            filter_func=lambda e: e.connector_name == connector_name and e.trading_pair == trading_pair and e.is_active
        )
        active_longs = [e for e in active_executors_by_connector_pair if e.side == TradeType.BUY]
        active_shorts = [e for e in active_executors_by_connector_pair if e.side == TradeType.SELL]
        return active_longs, active_shorts

    def get_signal(self) -> int:
        indicator_value = self.get_indicator_value()
        if indicator_value > 0:
            signal_value = 1
        elif indicator_value < 0:
            signal_value = -1
        else:
            signal_value = 0
        return signal_value

    def get_indicator_value(self) -> float:
        candles = self.market_data_provider.get_candles_df(self.config.exchange,
                                                           self.config.candles_pair,
                                                           self.config.candles_interval,
                                                           self.config.candles_length + 10)
        candles.ta.ao(fast=5, slow=34, append=True)
        if "AO_5_34" in candles:
            last_candle = candles.iloc[-1]
            ao = last_candle["AO_5_34"]
            return ao

        return 0

    def apply_initial_setting(self):
        if not self.account_config_set:
            for connector_name, connector in self.connectors.items():
                if self.is_perpetual(connector_name):
                    connector.set_position_mode(self.config.position_mode)
                    for trading_pair in self.market_data_provider.get_trading_pairs(connector_name):
                        connector.set_leverage(trading_pair, self.config.leverage)
            self.account_config_set = True

    def format_status(self) -> str:
        if not self.ready_to_trade:
            return "Market connectors are not ready."

        original_info = super().format_status()
        extra_info = []
        ao_indicator = self.get_indicator_value()

        extra_info.append(f"AO: {ao_indicator}")

        # Combine original and extra information
        format_status = f"{original_info}\n\n" + "\n".join(extra_info)
        return format_status

    def get_amount(self, position, mid_price):
        proposal: OrderCandidate = self.create_proposal(position, mid_price)
        proposal_adjusted = self.connectors[self.config.exchange].budget_checker.adjust_candidates([proposal],
                                                                                                   all_or_none=True)
        balance = self.connectors[self.config.exchange].budget_checker._get_available_balances(proposal_adjusted[0])
        usdt = 0
        if position == TradeType.BUY:
            if "USDT" in balance:
                usdt_balance = Decimal(balance["USDT"])
                usdt = usdt_balance * self.config.order_amount
                amount = usdt / mid_price
            else:
                amount = 0

            self.notify_hb_app_with_timestamp(f"""
                    Creating new trade:
                    Position: {position.name}
                    Amount: {amount}
                    Amount USDT: {usdt}""")
        else:
            asset = self.config.trading_pair.split("-")[0]
            if asset in balance:
                asset_balance = Decimal(balance[asset])
                amount = asset_balance
            else:
                amount = 0
            amount_usdt = amount * mid_price
            self.notify_hb_app_with_timestamp(f"""
                    Creating new trade:
                    Position: {position.name}
                    Amount: {amount}
                    Amount USDT: {amount_usdt}""")

        return amount

    def create_proposal(self, position, mid_price) -> OrderCandidate:
        return OrderCandidate(
            trading_pair=self.config.trading_pair,
            is_maker=False,
            order_type=OrderType.MARKET,
            order_side=position,
            amount=Decimal(self.config.order_amount),
            price=mid_price)