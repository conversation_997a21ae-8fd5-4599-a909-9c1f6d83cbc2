import logging
import math
import os
from decimal import Decimal
from typing import Dict, List, Optional

import pandas as pd
import pandas_ta as ta  # noqa: F401
from pydantic import Field, field_validator

from hummingbot.client.config.config_data_types import Client<PERSON>ieldData
from hummingbot.connector.connector_base import ConnectorBase
from hummingbot.core.clock import Clock
from hummingbot.core.data_type.common import OrderType, PositionMode, PriceType, TradeType
from hummingbot.data_feed.candles_feed.candles_factory import CandlesConfig
from hummingbot.strategy.strategy_v2_base import StrategyV2Base, StrategyV2ConfigBase
from hummingbot.strategy_v2.executors.position_executor.data_types import PositionExecutorConfig, TripleBarrierConfig
from hummingbot.strategy_v2.models.executor_actions import CreateExecutorAction, StopExecutorAction


class SimpleDirectionalAOConfig(StrategyV2ConfigBase):
    script_file_name: str = os.path.basename(__file__)
    markets: Dict[str, List[str]] = {}
    candles_config: List[CandlesConfig] = []
    controllers_config: List[str] = []
    exchange: str = Field(default="hyperliquid_perpetual")
    trading_pair: str = Field(default="ETH-USD")
    candles_exchange: str = Field(default="hyperliquid_perpetual")
    candles_pair: str = Field(default="ETH-USD")
    candles_interval: str = Field(default="5m")
    candles_length: int = Field(default=60, gt=0)
    ao_fast_length: int = Field(default=5, gt=0)
    ao_slow_length: int = Field(default=34, gt=0)
    order_amount: Decimal = Field(default=Decimal("0.98"), gt=0)  # Relative amount as percentage
    leverage: int = Field(default=5, gt=0)
    position_mode: PositionMode = Field(default="ONEWAY")
    cooldown: int = Field(default=60, gt=0, client_data=ClientFieldData(
        prompt_on_new=True, prompt=lambda mi: "Cooldown in seconds"
    ))
    max_closure_wait_time: int = Field(default=60, gt=0, client_data=ClientFieldData(
        prompt_on_new=True, prompt=lambda mi: "Maximum time to wait for position closure (seconds)"
    ))

    # Triple Barrier Configuration
    stop_loss: Decimal = Field(default=Decimal("0.5"), gt=0)
    take_profit: Decimal = Field(default=Decimal("3.0"), gt=0)
    time_limit: int = Field(default=60 * 50000, gt=0)

    # Robustness Configuration
    max_order_retries: int = Field(default=3, gt=0)
    min_order_amount: Decimal = Field(default=Decimal("0.001"), gt=0)
    balance_buffer: Decimal = Field(default=Decimal("0.05"), ge=0, le=1)  # 5% buffer

    @property
    def triple_barrier_config(self) -> TripleBarrierConfig:
        return TripleBarrierConfig(
            stop_loss=self.stop_loss,
            take_profit=self.take_profit,
            time_limit=self.time_limit,
            open_order_type=OrderType.MARKET,
            take_profit_order_type=OrderType.MARKET,
            stop_loss_order_type=OrderType.MARKET,
            time_limit_order_type=OrderType.MARKET,
        )

    @field_validator('position_mode', mode="before")
    @classmethod
    def validate_position_mode(cls, v: str) -> PositionMode:
        if v.upper() in PositionMode.__members__:
            return PositionMode[v.upper()]
        raise ValueError(f"Invalid position mode: {v}. Valid options are: {', '.join(PositionMode.__members__)}")


class SimpleDirectionalAO(StrategyV2Base):
    """
    This strategy uses AO (Awesome Oscillator) to generate trading signals and execute trades based on the AO values.
    It defines the specific parameters and configurations for the AO strategy.
    Enhanced with robustness features for better order execution and error handling.
    """

    last_signal = None
    account_config_set = False
    cooldown = 0
    _order_failures = 0
    _last_balance_check = 0
    _websocket_logger_suppressed = False
    _waiting_for_closure = False
    _closure_start_time = 0

    @classmethod
    def init_markets(cls, config: SimpleDirectionalAOConfig):
        cls.markets = {config.exchange: {config.trading_pair}}

    def __init__(self, connectors: Dict[str, ConnectorBase], config: SimpleDirectionalAOConfig):
        self._last_timestamp = None
        if len(config.candles_config) == 0:
            config.candles_config.append(CandlesConfig(
                connector=config.exchange,
                trading_pair=config.candles_pair,
                interval=config.candles_interval,
                max_records=config.candles_length + 10
            ))
        super().__init__(connectors, config)
        self.config = config
        self.current_ao = None
        self.current_signal = None
        self._suppress_websocket_logs()

    def _suppress_websocket_logs(self):
        """Suppress websocket subscription messages after connection issues"""
        if not self._websocket_logger_suppressed:
            # Suppress various websocket-related loggers
            loggers_to_suppress = [
                "hummingbot.data_feed.candles_feed.candles_base",
                "hummingbot.core.data_type.order_book_tracker_data_source",
                "hummingbot.core.data_type.user_stream_tracker_data_source",
                "hummingbot.connector.derivative.hyperliquid_perpetual.hyperliquid_perpetual_api_order_book_data_source",
                "hummingbot.connector.derivative.hyperliquid_perpetual.hyperliquid_perpetual_user_stream_data_source"
            ]

            for logger_name in loggers_to_suppress:
                logger = logging.getLogger(logger_name)
                # Set to ERROR level to suppress INFO and WARNING messages about subscriptions
                logger.setLevel(logging.ERROR)

            self._websocket_logger_suppressed = True

    def start(self, clock: Clock, timestamp: float) -> None:
        """
        Start the strategy.
        :param clock: Clock to use.
        :param timestamp: Current time.
        """
        self._last_timestamp = timestamp
        self.apply_initial_setting()

    def create_actions_proposal(self) -> List[CreateExecutorAction]:
        create_actions = []
        if self.cooldown > 0:
            self.cooldown = self.cooldown - 5
            self.notify_hb_app_with_timestamp(f"Cooling down... {self.cooldown}s remaining")
            return create_actions

        # Check if we're waiting for positions to close completely
        if self._waiting_for_closure:
            if self._check_all_positions_closed():
                self._waiting_for_closure = False
                self._closure_start_time = 0
                self.notify_hb_app_with_timestamp("✓ All positions closed successfully. Ready for new trades.")
            else:
                elapsed_time = self.current_timestamp - self._closure_start_time
                if elapsed_time > self.config.max_closure_wait_time:
                    self.notify_hb_app_with_timestamp(f"⚠ Closure timeout after {elapsed_time:.1f}s. Forcing ready state.")
                    self._waiting_for_closure = False
                    self._closure_start_time = 0
                else:
                    remaining_time = self.config.max_closure_wait_time - elapsed_time
                    self.notify_hb_app_with_timestamp(f"⏳ Waiting for all positions to close... {remaining_time:.1f}s remaining")
                    return create_actions

        # Validate market readiness
        if not self._validate_market_conditions():
            return create_actions

        signal = self.get_signal()
        active_longs, active_shorts = self.get_active_executors_by_side(self.config.exchange,
                                                                        self.config.trading_pair)
        if signal is not None:
            mid_price = self.market_data_provider.get_price_by_type(self.config.exchange,
                                                                    self.config.trading_pair,
                                                                    PriceType.MidPrice)

            # Validate price is reasonable
            if not self._validate_price(mid_price):
                self.notify_hb_app_with_timestamp("Price validation failed, skipping order creation")
                return create_actions

            if signal == 1 and (self.last_signal is None or self.last_signal == 0):
                amount = self.get_amount(TradeType.BUY, mid_price)
                if amount <= 0:
                    return create_actions

                # Additional validation before creating order
                if self._validate_order_creation(TradeType.BUY, amount, mid_price):
                    self.last_signal = 1
                    self.cooldown = self.config.cooldown
                    self._reset_failure_counter()  # Reset on successful validation
                    create_actions.append(CreateExecutorAction(
                        executor_config=PositionExecutorConfig(
                            timestamp=self.current_timestamp,
                            connector_name=self.config.exchange,
                            trading_pair=self.config.trading_pair,
                            side=TradeType.BUY,
                            entry_price=mid_price,
                            amount=amount,
                            triple_barrier_config=self.config.triple_barrier_config,
                            leverage=self.config.leverage
                        )))
                else:
                    self._handle_order_failure("BUY order validation failed")
            elif signal == -1 and (self.last_signal is None or self.last_signal == 1):
                amount = self.get_amount(TradeType.SELL, mid_price)
                if amount <= 0:
                    return create_actions

                # Additional validation before creating order
                if self._validate_order_creation(TradeType.SELL, amount, mid_price):
                    self.last_signal = 0
                    self.cooldown = self.config.cooldown
                    self._reset_failure_counter()  # Reset on successful validation
                    create_actions.append(CreateExecutorAction(
                        executor_config=PositionExecutorConfig(
                            timestamp=self.current_timestamp,
                            connector_name=self.config.exchange,
                            trading_pair=self.config.trading_pair,
                            side=TradeType.SELL,
                            entry_price=mid_price,
                            amount=amount,
                            triple_barrier_config=self.config.triple_barrier_config,
                            leverage=self.config.leverage
                        )))
                else:
                    self._handle_order_failure("SELL order validation failed")
        return create_actions

    def get_amount(self, position, mid_price):
        """Calculate order amount with enhanced validation and safety checks"""
        try:
            usdt_balance = self.connectors[self.config.exchange].get_available_balance("USD")

            if usdt_balance <= 0:
                self.notify_hb_app_with_timestamp("Insufficient USD balance for trading")
                return Decimal("0")

            # Apply balance buffer to prevent using all available balance
            available_balance = usdt_balance * (Decimal("1") - self.config.balance_buffer)
            usdt = available_balance * self.config.order_amount

            if usdt <= 0:
                self.notify_hb_app_with_timestamp("Calculated USD amount is too small")
                return Decimal("0")

            amount = usdt / mid_price * self.config.leverage

            # Validate minimum order amount
            if amount < self.config.min_order_amount:
                self.notify_hb_app_with_timestamp(f"Order amount {amount} below minimum {self.config.min_order_amount}")
                return Decimal("0")

            # Get trading rules and validate
            trading_rules = self.connectors[self.config.exchange].trading_rules.get(self.config.trading_pair)
            if trading_rules:
                if amount < trading_rules.min_order_size:
                    self.notify_hb_app_with_timestamp(f"Order amount {amount} below exchange minimum {trading_rules.min_order_size}")
                    return Decimal("0")

                # Round to proper precision using connector's quantize method
                amount = self.connectors[self.config.exchange].quantize_order_amount(self.config.trading_pair, amount)

            self.notify_hb_app_with_timestamp(f"""
                    Creating new trade:
                    Position: {position.name}
                    Amount: {amount}
                    Amount USD: {usdt}
                    Available Balance: {usdt_balance}
                    Used Balance: {usdt}""")

            return amount

        except Exception as e:
            self.notify_hb_app_with_timestamp(f"Error calculating order amount: {e}")
            return Decimal("0")

    def stop_actions_proposal(self) -> List[StopExecutorAction]:
        stop_actions = []
        signal = self.get_signal()
        active_longs, active_shorts = self.get_active_executors_by_side(self.config.exchange,
                                                                        self.config.trading_pair)
        if signal is not None:
            if signal == -1 and len(active_longs) > 0:
                # Validate that we actually have long positions to close
                if self._validate_positions_to_close(active_longs, "LONG"):
                    stop_actions.extend([StopExecutorAction(executor_id=e.id) for e in active_longs])
                    # Start waiting for closure completion
                    self._start_waiting_for_closure()
                    self.notify_hb_app_with_timestamp(f"Closing {len(active_longs)} long position(s). Waiting for complete closure before new trades.")
                else:
                    self.notify_hb_app_with_timestamp("Skipping long position closure - no valid positions to close")
            elif signal == 1 and len(active_shorts) > 0:
                # Validate that we actually have short positions to close
                if self._validate_positions_to_close(active_shorts, "SHORT"):
                    stop_actions.extend([StopExecutorAction(executor_id=e.id) for e in active_shorts])
                    # Start waiting for closure completion
                    self._start_waiting_for_closure()
                    self.notify_hb_app_with_timestamp(f"Closing {len(active_shorts)} short position(s). Waiting for complete closure before new trades.")
                else:
                    self.notify_hb_app_with_timestamp("Skipping short position closure - no valid positions to close")
        return stop_actions

    def get_active_executors_by_side(self, connector_name: str, trading_pair: str):
        active_executors_by_connector_pair = self.filter_executors(
            executors=self.get_all_executors(),
            filter_func=lambda e: e.connector_name == connector_name and e.trading_pair == trading_pair and e.is_active
        )
        active_longs = [e for e in active_executors_by_connector_pair if e.side == TradeType.BUY]
        active_shorts = [e for e in active_executors_by_connector_pair if e.side == TradeType.SELL]
        return active_longs, active_shorts

    def get_signal(self) -> Optional[float]:
        indicator_value = self.get_indicator_value()
        self.current_ao = indicator_value

        if indicator_value > 0:
            signal_value = 1
        elif indicator_value < 0:
            signal_value = -1
        else:
            signal_value = 0
        return signal_value

    def get_indicator_value(self) -> float:
        """Get AO indicator value with enhanced error handling"""
        try:
            candles = self.market_data_provider.get_candles_df(self.config.exchange,
                                                               self.config.candles_pair,
                                                               self.config.candles_interval,
                                                               self.config.candles_length + 10)

            if candles is None or candles.empty:
                self.notify_hb_app_with_timestamp("No candle data available for indicator calculation")
                return 0

            if len(candles) < max(self.config.ao_fast_length, self.config.ao_slow_length):
                self.notify_hb_app_with_timestamp(f"Insufficient candle data: {len(candles)} candles, need at least {max(self.config.ao_fast_length, self.config.ao_slow_length)}")
                return 0

            candles.ta.ao(fast=self.config.ao_fast_length, slow=self.config.ao_slow_length, append=True)
            ao_column = f"AO_{self.config.ao_fast_length}_{self.config.ao_slow_length}"

            if ao_column in candles.columns:
                last_candle = candles.iloc[-1]
                ao = last_candle[ao_column]

                # Validate AO value is not NaN or infinite
                if pd.isna(ao) or not math.isfinite(float(ao)):
                    self.notify_hb_app_with_timestamp("Invalid AO indicator value (NaN or infinite)")
                    return 0

                return float(ao)
            else:
                self.notify_hb_app_with_timestamp(f"AO column {ao_column} not found in candles data")
                return 0

        except Exception as e:
            self.notify_hb_app_with_timestamp(f"Error calculating AO indicator: {e}")
            return 0

    def apply_initial_setting(self):
        if not self.account_config_set:
            for connector_name, connector in self.connectors.items():
                if self.is_perpetual(connector_name):
                    connector.set_position_mode(self.config.position_mode)
                    for trading_pair in self.market_data_provider.get_trading_pairs(connector_name):
                        connector.set_leverage(trading_pair, self.config.leverage)
            self.account_config_set = True

    def _validate_market_conditions(self) -> bool:
        """Validate that market conditions are suitable for trading"""
        try:
            # Check if market data provider is ready
            if not self.market_data_provider.ready:
                self.notify_hb_app_with_timestamp("Market data provider not ready")
                return False

            # Check if connector is ready
            connector = self.connectors.get(self.config.exchange)
            if not connector or not connector.ready:
                self.notify_hb_app_with_timestamp("Exchange connector not ready")
                return False

            # Check if we have recent price data
            try:
                mid_price = self.market_data_provider.get_price_by_type(
                    self.config.exchange, self.config.trading_pair, PriceType.MidPrice
                )
                if mid_price <= 0:
                    self.notify_hb_app_with_timestamp("Invalid mid price received")
                    return False
            except Exception as e:
                self.notify_hb_app_with_timestamp(f"Error getting price data: {e}")
                return False

            return True

        except Exception as e:
            self.notify_hb_app_with_timestamp(f"Error validating market conditions: {e}")
            return False

    def _validate_price(self, price: Decimal) -> bool:
        """Validate that the price is reasonable"""
        try:
            if price <= 0:
                return False

            # Check if price is within reasonable bounds (not too extreme)
            if price > Decimal("1000000") or price < Decimal("0.000001"):
                return False

            return True

        except Exception:
            return False

    def _validate_order_creation(self, side: TradeType, amount: Decimal, price: Decimal) -> bool:
        """Validate order creation parameters"""
        try:
            # Check minimum amount
            if amount < self.config.min_order_amount:
                self.notify_hb_app_with_timestamp(f"Order amount {amount} below minimum {self.config.min_order_amount}")
                return False

            # Check if we have sufficient balance
            connector = self.connectors[self.config.exchange]
            if side == TradeType.BUY:
                required_balance = amount * price / self.config.leverage
                available_balance = connector.get_available_balance("USD")
                if available_balance < required_balance:
                    self.notify_hb_app_with_timestamp(f"Insufficient balance: need {required_balance}, have {available_balance}")
                    return False

            # Check trading rules
            trading_rules = connector.trading_rules.get(self.config.trading_pair)
            if trading_rules:
                if amount < trading_rules.min_order_size:
                    self.notify_hb_app_with_timestamp(f"Order amount {amount} below exchange minimum {trading_rules.min_order_size}")
                    return False
                if amount > trading_rules.max_order_size:
                    self.notify_hb_app_with_timestamp(f"Order amount {amount} above exchange maximum {trading_rules.max_order_size}")
                    return False

            return True

        except Exception as e:
            self.notify_hb_app_with_timestamp(f"Error validating order creation: {e}")
            return False

    def _validate_positions_to_close(self, executors, expected_side: str) -> bool:
        """Validate that we have actual positions to close before attempting to close them"""
        try:
            connector = self.connectors[self.config.exchange]

            # Check if connector has account_positions attribute (for perpetual trading)
            if not hasattr(connector, 'account_positions'):
                self.notify_hb_app_with_timestamp("Connector does not support position tracking")
                return False

            # Get current positions from the exchange
            current_positions = connector.account_positions

            if not current_positions:
                self.notify_hb_app_with_timestamp(f"No positions found on exchange for {expected_side} closure")
                return False

            # Check if we have a position for this trading pair
            position = current_positions.get(self.config.trading_pair)

            if not position:
                self.notify_hb_app_with_timestamp(f"No position found for {self.config.trading_pair}")
                return False

            # Validate position direction matches what we're trying to close
            from hummingbot.core.data_type.common import PositionSide

            if expected_side == "LONG":
                # For closing long positions, we need a LONG position (positive amount)
                if position.amount <= 0:
                    self.notify_hb_app_with_timestamp(f"Cannot close LONG - current position amount: {position.amount}")
                    return False
            elif expected_side == "SHORT":
                # For closing short positions, we need a SHORT position (negative amount)
                if position.amount >= 0:
                    self.notify_hb_app_with_timestamp(f"Cannot close SHORT - current position amount: {position.amount}")
                    return False

            # Validate position size is significant enough to close
            min_position_size = self.config.min_order_amount
            if abs(position.amount) < min_position_size:
                self.notify_hb_app_with_timestamp(f"Position size {abs(position.amount)} too small to close (min: {min_position_size})")
                return False

            self.notify_hb_app_with_timestamp(f"Validated {expected_side} position for closure: {position.amount}")
            return True

        except Exception as e:
            self.notify_hb_app_with_timestamp(f"Error validating positions to close: {e}")
            return False

    def format_status(self) -> str:
        if not self.ready_to_trade:
            return "Market connectors are not ready."

        # Custom implementation to filter Main Controller Executors to only show active ones
        lines = []
        warning_lines = []
        warning_lines.extend(self.network_warning(self.get_market_trading_pair_tuples()))

        balance_df = self.get_balance_df()
        lines.extend(["", "  Balances:"] + ["    " + line for line in balance_df.to_string(index=False).split("\n")])

        try:
            df = self.active_orders_df()
            lines.extend(["", "  Orders:"] + ["    " + line for line in df.to_string(index=False).split("\n")])
        except ValueError:
            lines.extend(["", "  No active maker orders."])

        # Custom Main Controller Executors section - only show ACTIVE executors
        from hummingbot.client.ui.interface_utils import format_df_for_printout
        columns_to_show = ["type", "side", "status", "net_pnl_pct", "net_pnl_quote", "cum_fees_quote",
                           "filled_amount_quote", "is_trading", "close_type", "age"]

        main_executors_list = self.get_executors_by_controller("main")
        # Filter to only show active executors (not completed/terminated ones)
        active_main_executors = [executor for executor in main_executors_list if executor.is_active]

        if len(active_main_executors) > 0:
            lines.append("\n\nMain Controller Executors:")
            main_executors_df = self.executors_info_to_df(active_main_executors)
            main_executors_df["age"] = self.current_timestamp - main_executors_df["timestamp"]
            lines.extend([format_df_for_printout(main_executors_df[columns_to_show], table_format="psql")])
        else:
            lines.append("\n\nMain Controller Executors:")
            lines.append("No active executors.")

        # Strategy-specific information
        extra_info = []

        # Get AO indicator with error handling
        ao_indicator = self.get_indicator_value()
        signal = self.get_signal()

        extra_info.append(f"AO Indicator: {ao_indicator:.6f}")
        extra_info.append(f"Current Signal: {signal}")

        # Add robustness status information
        extra_info.append(f"Order Failures: {self._order_failures}")
        extra_info.append(f"Cooldown: {self.cooldown}s")

        # Add closure waiting status
        if self._waiting_for_closure:
            elapsed_time = self.current_timestamp - self._closure_start_time
            remaining_time = self.config.max_closure_wait_time - elapsed_time
            extra_info.append(f"⏳ Waiting for Closure: {remaining_time:.1f}s remaining")

        # Market condition status
        market_ready = self._validate_market_conditions()
        extra_info.append(f"Market Conditions: {'✓ Ready' if market_ready else '✗ Not Ready'}")

        # Balance information
        try:
            connector = self.connectors[self.config.exchange]
            balance = connector.get_available_balance("USD")
            extra_info.append(f"Available Balance: {balance:.2f} USD")
        except Exception:
            extra_info.append("Available Balance: Error retrieving")

        # Active positions
        active_longs, active_shorts = self.get_active_executors_by_side(self.config.exchange, self.config.trading_pair)
        extra_info.append(f"Active Executors: {len(active_longs)} Long, {len(active_shorts)} Short")

        # Exchange positions
        try:
            connector = self.connectors[self.config.exchange]
            if hasattr(connector, 'account_positions'):
                position = connector.account_positions.get(self.config.trading_pair)
                if position:
                    side_str = "LONG" if position.amount > 0 else "SHORT" if position.amount < 0 else "FLAT"
                    extra_info.append(f"Exchange Position: {side_str} {abs(position.amount):.6f}")
                else:
                    extra_info.append("Exchange Position: No position")
            else:
                extra_info.append("Exchange Position: Not supported")
        except Exception:
            extra_info.append("Exchange Position: Error retrieving")

        # Add strategy information to the display
        lines.extend(["\n\nStrategy Information:"] + ["  " + line for line in extra_info])

        if warning_lines:
            lines.extend(["", "*** WARNINGS ***"] + warning_lines)

        return "\n".join(lines)

    def on_tick(self):
        """Enhanced on_tick with additional robustness checks"""
        try:
            # Reset order failure counter periodically
            if self.current_timestamp - self._last_balance_check > 300:  # Every 5 minutes
                self._order_failures = max(0, self._order_failures - 1)
                self._last_balance_check = self.current_timestamp

            # Call parent on_tick
            super().on_tick()

        except Exception as e:
            self.notify_hb_app_with_timestamp(f"Error in on_tick: {e}")

    def _handle_order_failure(self, reason: str):
        """Handle order failures with exponential backoff"""
        self._order_failures += 1
        self.notify_hb_app_with_timestamp(f"Order failure #{self._order_failures}: {reason}")

        # Implement exponential backoff for cooldown
        if self._order_failures > self.config.max_order_retries:
            extended_cooldown = self.config.cooldown * (2 ** min(self._order_failures - self.config.max_order_retries, 5))
            self.cooldown = max(self.cooldown, extended_cooldown)
            self.notify_hb_app_with_timestamp(f"Extended cooldown applied: {self.cooldown}s due to repeated failures")

    def _reset_failure_counter(self):
        """Reset failure counter after successful operations"""
        if self._order_failures > 0:
            self.notify_hb_app_with_timestamp("Resetting failure counter after successful operation")
            self._order_failures = 0

    def _start_waiting_for_closure(self):
        """Start the waiting period for position closure"""
        self._waiting_for_closure = True
        self._closure_start_time = self.current_timestamp
        self.cooldown = self.config.cooldown  # Also apply the regular cooldown

    def _check_all_positions_closed(self) -> bool:
        """
        Comprehensive check to ensure all positions are completely closed.
        Returns True only when:
        1. No active executors exist
        2. No exchange positions exist
        3. No pending close orders exist
        """
        try:
            # Check 1: No active executors
            active_longs, active_shorts = self.get_active_executors_by_side(
                self.config.exchange, self.config.trading_pair)

            if len(active_longs) > 0 or len(active_shorts) > 0:
                self.notify_hb_app_with_timestamp(f"Still have active executors: {len(active_longs)} longs, {len(active_shorts)} shorts")
                return False

            # Check 2: No shutting down executors (executors in the process of closing)
            all_executors = self.get_all_executors()
            shutting_down_executors = [e for e in all_executors
                                     if e.connector_name == self.config.exchange
                                     and e.trading_pair == self.config.trading_pair
                                     and hasattr(e, 'status')
                                     and str(e.status) == 'RunnableStatus.SHUTTING_DOWN']

            if len(shutting_down_executors) > 0:
                self.notify_hb_app_with_timestamp(f"Still have {len(shutting_down_executors)} executors shutting down")
                return False

            # Check 3: No exchange positions
            connector = self.connectors[self.config.exchange]
            if hasattr(connector, 'account_positions'):
                position = connector.account_positions.get(self.config.trading_pair)
                if position and abs(position.amount) > self.config.min_order_amount:
                    self.notify_hb_app_with_timestamp(f"Still have exchange position: {position.amount}")
                    return False

            # Check 4: No pending orders related to position closing
            if hasattr(connector, 'in_flight_orders'):
                pending_orders = [order for order in connector.in_flight_orders.values()
                                if order.trading_pair == self.config.trading_pair]
                if len(pending_orders) > 0:
                    self.notify_hb_app_with_timestamp(f"Still have {len(pending_orders)} pending orders")
                    return False

            # All checks passed - positions are completely closed
            return True

        except Exception as e:
            self.notify_hb_app_with_timestamp(f"Error checking position closure: {e}")
            return False