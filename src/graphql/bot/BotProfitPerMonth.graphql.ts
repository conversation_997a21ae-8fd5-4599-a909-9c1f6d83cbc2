import gql from "graphql-tag";

export const BotProfitPerMonth = gql`query($strategy: String, $interval: String, $months: Int) {
    bot {
        profitPerMonth(strategy: $strategy, interval: $interval, months: $months) {
            strategy
            interval
            totalProfit
            tradesPos
            tradesNeg
            monthlyProfits {
                month
                monthStr
                profit
            }
        }
    }
}`