import gql from "graphql-tag";

export const BotSignals = gql`query($symbol: String, $strategy: String, $days: Int, $interval: String, $limit: Int) {
    bot {
        signals(symbol: $symbol, strategy: $strategy, days: $days, interval: $interval, limit: $limit) {
            id
            advice
            strategy
            price
            symbol
            profit
            timestamp
            candleTime
        }

        profit(strategy: $strategy, symbol: $symbol, interval: $interval) {
            profit
            tradesNeg
            tradesPos
        }
    }

    exchange {
        prices {
            symbol
            price
        }
    }
}`
