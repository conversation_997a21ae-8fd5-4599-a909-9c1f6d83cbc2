import gql from "graphql-tag";

export const MarketBots = gql`query marketBots {
    bot {
        marketBots {
            id
            symbol
            exchange
            strategy
            startBalanceUSD
            finalBalanceUSD
            startBalanceBTC
            finalBalanceBTC
            startPrice
            endPrice
            marketPercent
            startDate
            stopDate
            updatedOn
            strategyParams
            interval
            profit
            lastTrade
            active
            position
            timestamp
            amountUsdtInvested
            simulation
            sellInMinus
            roundTrips {
                entryDate
                entryPrice
                exitDate
                exitPrice
                duration
                profit
                profitWithoutFees
            }
            trades {
                tradeDate
                position
                price
                profit
                profitWithoutFees
                tradeResult
            }
        }
    }
}`