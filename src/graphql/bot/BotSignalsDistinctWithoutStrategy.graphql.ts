import gql from "graphql-tag";

export const BotSignalsDistinctWithoutStrategy = gql`query BotSignalsDistinctWithoutStrategy($symbol: String!, $indicator: String, $interval: String) {
    bot {
        getIndicators(symbol: $symbol, indicator: $indicator, interval: $interval) {
            symbol
            interval
            action
            indicator
            timestamp
            price
        }
        autobot {
            marketBotsBig {
                id
                symbol
                active
            }
        }
    }

    exchange {
        lastOrders(symbol: $symbol, limit: 1) {
            price
        }
    }
}`
