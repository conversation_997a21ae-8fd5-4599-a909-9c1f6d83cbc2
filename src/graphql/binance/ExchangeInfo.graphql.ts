import gql from "graphql-tag";

export const ExchangeInfoQuery = gql`query ExchangeInfo($base: String, $symbol: String) {
    exchange {
        exchangeInfo(base: $base, symbol: $symbol) {
            symbol
            baseAsset
            baseAssetPrecision
            quoteAsset
            quotePrecision
            icebergAllowed
            filters {
                filterType
                minPrice
                maxPrice
                tickSize
                minQty
                maxQty
                stepSize
                minNotional
            }
        }
    }
}`
