import gql from "graphql-tag";

export const OrderMutation = gql`mutation orderMutation($symbol: String!,
    $side: String!,
    $type: String!,
    $quantity: Float!,
    $price: Float,
    $stopPrice: Float,
    $botStopPrice: Float,
    $timeInForce: String,
    $buyStopActive: Boolean,
    $buyTPActive: Boolean,
    $botTPPrice: Float,
    $quantityUsd: Float,
    $icebergQty: Float) {

    orderMutation(symbol: $symbol,
        side: $side,
        type: $type,
        quantity: $quantity,
        price: $price,
        stopPrice: $stopPrice,
        buyStopActive: $buyStopActive,
        buyTPActive: $buyTPActive,
        botStopPrice: $botStopPrice,
        quantityUsd: $quantityUsd,
        botTPPrice: $botTPPrice,
        timeInForce: $timeInForce,
        icebergQty: $icebergQty) {
        symbol
        orderId
        transactTime
        price
        origQty
        executedQty
        status
        timeInForce
        type
        side
        ok
        error
    }
}`
