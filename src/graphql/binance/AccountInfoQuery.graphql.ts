import gql from "graphql-tag";

export const AccountInfoQuery: IAccountInfoQueryResult = gql`
    query accountInfoQuery {
        exchange {
            accountInfo(noSmallAmount: true) {
                makerCommission
                takerCommission
                buyerCommission
                sellerCommission
                canTrade
                canWithdraw
                canDeposit
                balances {
                    idx
                    asset
                    free
                    locked
                    btcValue
                    usdValue
                }
                btcValue
                usdValue
            }
        }
    }`;

export interface IAccountInfoQueryResult {
    exchange: {
        accountInfo: {
            makerCommission: number,
            takerCommission: number,
            buyerCommission: number,
            sellerCommission: number
            canTrade: boolean,
            canWithdraw: boolean,
            canDeposit: boolean,
            balances: {
                idx: number,
                asset: string,
                free: number,
                locked: number,
                btcValue: number,
                usdValue: number,
            },
            btcValue: number,
            usdValue: number,
        },
    };
}
