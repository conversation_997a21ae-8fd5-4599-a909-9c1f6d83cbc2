import gql from "graphql-tag";

export const CoinIndicatorsQueryGraphql = gql`query CoinIndicatorsQueryGraphql ($filters: InstrumentFilters) {
    instruments {
        fetch(filters: $filters) {
            symbol
            isWhitelisted
            isPump
            isRecommended
            isBinanceRecommended
            isNewListed
            percent1h
            percent4h
            percent24h
            percentVolume1h
            percentVolume2h
            percentVolume4h
            percentVolume24h
            rsi15
            rsi60
            rsi120
            rsi240
            rsi1d
            listedOn
            updateTimestamp
            interval
            ao
            adx
            rsi
            btcAO1d
            atr
            ema25
            ema50
            ema100
            mfi
            vwap
            ichimoku
            roc
            bb_upper
            bb_middle
            bb_lower
            bb_obv
            ad
            so_k
            so_d
            sma7
            sma21
            sma100
            macd
            price
        }
    }
}
`
