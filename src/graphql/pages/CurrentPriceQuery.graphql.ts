import gql from "graphql-tag";

export const CurrentPriceQuery = gql`query CurrentPriceQuery($symbol: String!, $limit: Int, $status: String, $interval: String) {
    indicators {
        macd(symbol: $symbol, interval: $interval) {
            signal
            openTime
            MACD
            histogram
        }
    }

    exchange {
        lastOrders(symbol: $symbol, limit: $limit, status: $status) {
            price
            status
            executedQty
        }
        currentPrice(symbol: $symbol) {
            price    
        }
        
        symbolTicker(symbol: $symbol) {
            volumeQuoteBTC
        }
    }
}`
