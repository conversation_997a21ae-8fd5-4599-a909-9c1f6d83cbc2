import * as React from 'react';

class OverviewHeader extends React.Component {
    static propTypes = {
      title: PropTypes.string,
      onSearch: PropTypes.func,
    };

    constructor(props) {
      super(props);

      this.state = {
        search: '',
      };
    }

    componentDidMount() {
    }

    onInputSearch = e => {
      this.setState({
        search: e.target.value,
      });

      this.props.onSearch(e.target.value);
    };

    render() {
      const { title } = this.props;

      return (<div className="row">
        <div className="col-lg-12">
          <div className="view-header overview-header">
            <div className="pull-right text-right" style={{ lineHeight: '14px' }}>
              <div className="form-group">
                <input
                  className="form-control"
                  id="searchMarket"
                  name="searchMarket"
                  placeholder="Market"
                  onChange={this.onInputSearch} />
              </div>
            </div>

            <div>
              <h3>{title}</h3>
            </div>
          </div>
        </div>
      </div>);
    }
}

export default OverviewHeader;
