import {ExchangeInfoQuery} from "@/graphql/binance/ExchangeInfo.graphql";
import {inject} from "mobx-react";
import {path} from "ramda";
import * as React from 'react';
import {graphql} from 'react-apollo';

import {withRouter} from 'react-router';
import {Link} from 'react-router-dom';

// @ts-ignore
@graphql(ExchangeInfoQuery, {
    options: {
        variables: {
            base: 'USDT',
        },
    },
})
@inject("exchangeStore")
@withRouter
export default class Navigation extends React.Component<any> {
    public state = {
        selection: 'DEFAULT',
    };

    public navItems = [
        {
            label: 'Dashboard',
            link: '/dashboard',
            icon: 'fa fa-home'
        },
        {
            label: 'Split Screen',
            link: '/splitscreen',
            icon: 'fa fa-desktop'

        },
        {
            label: 'Last Orders',
            link: '/orders',
            icon: 'fa fa-floppy-o'
        },
        {
            label: 'Instruments',
            link: '/instruments',
            icon: 'fa fa-floppy-o'
        },
        {
            label: 'Favorites',
            link: '/favorites',
            icon: 'fa fa-star'
        },
        {
            label: 'Trading Bot',
            link: '/tradingbot',
            icon: 'fa fa-bar-chart'
        },
        {
            label: 'Auto Bot',
            link: '/autobot',
            icon: 'fa fa-signal'

        },
        {
            label: 'Strategy Tester',
            link: '/strategyTester',
            icon: 'fa fa-fighter-jet'
        },
        {
            label: 'Settings',
            link: '/settings',
            icon: 'fa fa-gear'
        },
        {
            label: 'Logs',
            link: '/logs',
            icon: 'fa fa-history'
        },
    ];

    public onSelect = e => {
        if (e.target.value != null) {
            this.props.history.push(`/coin/${e.target.value}`);

            this.setState({
                selection: 'DEFAULT',
            });
        }
    };

    public onMobileNavClick = (e) => {
        if (!SERVER) {
            document.body.classList.remove('nav-toggle');
        }
    };

    public componentWillReceiveProps(nextProps: Readonly<any>, nextContext: any) {
        const exchangeInfo: any = path(['data', 'exchange', 'exchangeInfo'])(nextProps) || []
        if (exchangeInfo && !nextProps.data.loading) {
            this.props.exchangeStore.exchangeInfo = exchangeInfo
        }
    }

    public render() {
        const exchangeInfo: any = this.props.exchangeStore.exchangeInfo

        return (
            <aside className="navigation">
                <nav>
                    <ul className="nav luna-nav" style={{position: 'fixed'}}>
                        {this.navItems.map(item => (
                            <li key={item.label} className={`{{isActivePath ${item.link}}}`}>
                                <Link onClick={this.onMobileNavClick} to={item.link}>
                                    <i style={{width: '16px'}}
                                       className={item.icon + ' m-r-xs'}/>{item.label}</Link>
                            </li>))}

                        <li className="asset-link">
                            <hr/>
                            <select className="form-control"
                                    value={this.state.selection}
                                    onChange={this.onSelect}>
                                {exchangeInfo
                                    ? [
                                        <option key="default" value="DEFAULT">Select symbol</option>,
                                        ...exchangeInfo.map(symbol =>
                                            (<option key={symbol.symbol} value={symbol.symbol}>{symbol.baseAsset}
                                            </option>))]
                                    : null}
                            </select>

                        </li>
                    </ul>
                </nav>
            </aside>);
    }
}
