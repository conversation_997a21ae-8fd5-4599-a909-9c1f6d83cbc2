import * as React from "react";

interface IProps {
    filled: boolean;
    loading: boolean;
    className: string;
}

export class MPanel extends React.Component<IProps> {
    public static defaultProps = {
        filled: false,
        loading: false,
        className: "",
    };

    public render() {
        let classNames = "panel";

        if (this.props.filled) {
            classNames += " panel-filled ";
        }

        if (this.props.loading) {
            classNames += " ld-loading ";
        }

        return (<div className={`${classNames} ${this.props.className}`}>

            {this.props.loading ? <div className="loader">
                <div className="loader-spin"/>
            </div> : null}

            {this.props.children}
        </div>);
    }
}
