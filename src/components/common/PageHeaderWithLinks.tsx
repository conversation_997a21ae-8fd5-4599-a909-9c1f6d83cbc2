import {Link} from 'react-router-dom';
import * as React from "react";

export class PageHeaderWithLinks extends React.Component<any> {
    public render() {
        const {title, links} = this.props;

        return (<div>
            <div className="row">
                <div className="col-lg-12">
                    <div className="view-header market-header">
                        <div className="pull-right text-right" style={{lineHeight: '14px'}}>
                            <small><span className="c-white">{title}</span></small>
                        </div>
                        {links ? links.map((link, idx) =>
                            this.renderLinkedText(link, idx)) : null}
                    </div>
                </div>
            </div>
            <hr/>
        </div>);
    }

    public renderLinkedText(link, idx) {
        return (<div key={idx} className={`header-icon ${idx !== 0 ? 'm-l-md' : null}`}>
            <i className="pe page-header-icon"/>
            {link.active
                ? <span>{link.title}</span>
                : <span className="normal-text-color"> <Link
                    to={{pathname: link.url}}>{link.title}</Link> </span>}
        </div>);
    }
}
