import * as React from 'react';
import {Modal} from "react-bootstrap";

interface IProps {
    text: string;
    show: boolean;

    closeModal();

    confirm();
}

export class ConfirmationModal extends React.Component<IProps> {

    public confirm = () => {
        this.props.closeModal();
        this.props.confirm();
    };

    public render() {
        const {show, text, closeModal} = this.props;

        return <Modal show={show} onHide={closeModal} className="fade" role="dialog" aria-hidden="true" tabIndex="-1">
            <Modal.Header closeButton className="modal-header text-center">
                <h4 className="modal-title">
                    Confirm action
                </h4>
            </Modal.Header>

            <Modal.Body>
                {text ? text : "Please confirm your action!"}
            </Modal.Body>

            <div className="modal-footer">
                <button className="btn btn-default" data-dismiss="modal" onClick={closeModal}>Close</button>
                <button className="btn btn-accent" onClick={this.confirm}>Confirm</button>
            </div>
        </Modal>;
    }
}
