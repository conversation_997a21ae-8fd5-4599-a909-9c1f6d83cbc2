import * as React from "react";
import {contains} from "ramda";
import {iconList} from "@/components/pages/dashboard/IconList";

export class PageHeaderDetails extends React.Component<any> {

    render() {
        const {title, section, icon} = this.props;
        const logo = contains(icon.toLowerCase(), iconList)
            ? '/assets/img/' + icon.toLowerCase() + '.png'
            : '/assets/img/gen.png';

        return (<div className="row">
            <div className="col-lg-12">
                <div className="view-header market-header">
                    <div className="pull-right text-right" style={{lineHeight: '14px'}}>
                        <small><br/>{section}<br/> <span className="c-white">{title}</span></small>
                    </div>
                    <div className="header-icon">
                        <i className="pe page-header-icon"><img src={logo} height={32} width={32}/></i>
                    </div>
                </div>
            </div>
        </div>);
    }
}
