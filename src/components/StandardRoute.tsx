import * as React from 'react';
import {ChildP<PERSON>, graphql} from "react-apollo";
import {Route, RouteComponentProps, RouteProps} from "react-router";
import {SessionQuery} from "@/graphql/login/SessionQuery";
import {inject, observer} from "mobx-react";

// @ts-ignore
@graphql(SessionQuery, {
    options: {
        fetchPolicy: 'network-only'
    }
})
@inject("session")
@observer
export class StandardRoute extends React.Component<ChildProps<RouteProps & any>> {

    public componentWillReceiveProps(nextProps) {
        const {data} = nextProps;

        this.props.session.user = data.session;
    }

    public renderRoute = (routeProps: RouteComponentProps<{}>) => {
        const {data, component: Component} = this.props;

        if (data.session && data.session.id) {
            return <Component {...routeProps} />;
        }

        return <Component {...routeProps} />;
    };

    public render() {
        const {data: _, component: __, ...rest} = this.props;

        return <Route {...rest}
                      render={this.renderRoute}/>
    }
}
