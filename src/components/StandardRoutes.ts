// Routes

// ----------------------------------------------------------------------------
// IMPORTS

/* NPM */

// We're using `react-router-dom` to handle routing, so grab the `RouteProps`
// type that we'll use to ensure our own types conform to the expected configuration
// By default, pull in the ReactQL example. In your own project, just nix
// the `src/components/example` folder and replace the following line with
// your own React components
import Login from "@/components/pages/login/Login";
import Register from "@/components/pages/register/Register";
import {RouteProps} from "react-router-dom";
import LinkConfirmed from "@/components/pages/register/LinkConfirmed";
import ConfirmationRequired from "@/components/pages/register/ConfirmationRequired";

/* Local */

// Components

// ----------------------------------------------------------------------------

// Specify the routes. This is provided as an array of `RouteProp`, which is
// a type provided by `react-router-dom` for rendering a route. Typically, this
// will contain at least a component and a path
const StandardRoutes: RouteProps[] = [
    {
        component: Login, // <-- this is the component that'll be rendered
        exact: true, // <-- this says to ONLY match when the path is exactly '/'
        path: "/login", // <-- ... and this is the actual path to match on
    },
    {
        component: Register,
        exact: true,
        path: "/register",
    },
    {
        component: ConfirmationRequired,
        exact: true,
        path: "/confirmation"
    },
    {
        component: LinkConfirmed,
        exact: true,
        path: "/confirmed"
    },
];

export default StandardRoutes;
