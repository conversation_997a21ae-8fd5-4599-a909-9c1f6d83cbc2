import * as React from 'react';
import {ChildProps, graphql} from "react-apollo";
import {Redirect, Route, RouteComponentProps, RouteProps} from "react-router";
import {SessionQuery} from "@/graphql/login/SessionQuery";
import {Loading} from "@/components/common/Loading";
import {inject, observer} from "mobx-react";

// @ts-ignore
@graphql(SessionQuery, {
    options: {
        fetchPolicy: 'network-only'
    }
})
@inject("session")
@observer
export class AuthRouteNoHeader extends React.Component<ChildProps<RouteProps & any>> {

    public componentWillReceiveProps(nextProps) {
        const {data} = nextProps;

        this.props.session.user = data.session;
    }

    public renderRoute = (routeProps: RouteComponentProps<{}>) => {
        const {data, component: Component} = this.props;

        if (data && data.session && data.session.id) {
            return <Component {...routeProps} />;
        }

        if (!data || data.loading) {
            return <Loading/>
        }

        return <Redirect to="/login"/>
    };

    public render() {
        const {data: _, component: __, ...rest} = this.props;

        return <Route {...rest}
                      render={this.renderRoute}/>
    }
}
