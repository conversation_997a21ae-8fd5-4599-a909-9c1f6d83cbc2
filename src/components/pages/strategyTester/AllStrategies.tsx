import * as React from "react";
import {MPanel} from "@/components/common/MPanel";
import {Button, Panel, Table} from "react-bootstrap";
import gql from "graphql-tag";
import {graphql, withApollo} from "react-apollo";
import * as shortid from "shortid";
import {descend, path, prop, sortWith} from "ramda";
import {observable} from "mobx";
import {observer} from "mobx-react";
import {error, success} from "@/utils/Toast";

const GetStrategyTests = gql`
    query GetAllStrategyTests {
        strategy {
            getAllStrategyTests {
                strategy
                interval
                startDate
                endDate
                status
                profit
                profitUsd
                createTimestamp

                numShorts
                investPerCoin

                currentSymbol
                progressStrategy
                progressInterval
                progressSymbol

                bestSymbolProfit
                worstSymbolProfit
                
                numTrades
                posTrades
                negTrades

                bestPerformer
                worstPerformer

                avgProfit
                avgHoldMinutes
                avgTradesDay
                bestTrade
                avgTrade
                worstTrade
                createTimestamp
                updateTimestamp

                sl
            }
        }
    }
`;

const CalcAllStrategyMutation = gql`mutation CalcAllStrategyMutation($strategy: String!) {
    calcAllStrategy(strategy: $strategy) {
        ok
        error
    }
}
`

// @ts-ignore
@graphql(GetStrategyTests, {
    options: {
        pollInterval: 5000
    }
})
// @ts-ignore
@withApollo
@observer
export class AllStrategies extends React.Component<any, any> {

    @observable
    private strategy = "all"

    public render() {
        const toTestStrat = [
            "ao-cross",
            "ao-swing",
            "ao-cross-swing",

            "ao-cross-btc-swing",
            "ao-swing-btc-swing",
            "ao-cross-swing-btc-swing",

            "ao-cross-btc-cross",
            "ao-swing-btc-cross",
            "ao-cross-swing-btc-cross",

            "btc-cross",
            "btc-swing",
            "btc-cross-swing",

            "ema+adx",
            "ema+adx+ao-cross",

            "ema25+ema50",
            "ema50+ema100",
            "ema100+ema200",

            "nesterov",
            // "nesterov-btc-cross",
            // "nesterov-btc-swing",

            "all",
        ]

        return <MPanel filled className="panel-c-accent">
            <Panel.Heading>
                All Time Strategy Results

                <Button className={"pull-right m-r-xs btn-xs badge"}
                        onClick={() => this.onCalc()}>
                    <small>calculate</small>
                </Button>
                <select className="form-control single-view-select pull-right m-r-xs"
                        style={{width: 100}}
                        value={this.strategy}
                        onChange={e => this.onSelect(e)}>
                    {toTestStrat.map(x => <option value={x}>{x}</option>)}
                </select>
            </Panel.Heading>

            <Panel.Body>
                <Table className="table table-condensed small">
                    <thead>
                    <tr>
                        <th>Strategy</th>
                        <th>Interval</th>
                        <th>SL</th>
                        <th>Status</th>
                        <th>Best Profit</th>
                        <th>Worst Profit</th>
                        <th>Best Performer</th>
                        <th>Worst Performer</th>
                        <th>Trades</th>
                        <th>Pos/Neg-Ratio</th>
                        <th>Avg Profit</th>
                    </tr>
                    </thead>
                    <tbody>
                    {this.renderStrategies()}
                    </tbody>
                </Table>
            </Panel.Body>
        </MPanel>
    }

    private onSelect = async (event) => {
        this.strategy = event.target.value
    }

    private async onCalc() {
        const {client} = this.props

        try {
            await client.mutate({
                mutation: CalcAllStrategyMutation,
                variables: {
                    strategy: this.strategy
                }
            })
            success(`Calculation added for ${this.strategy}`);
        } catch (e) {
            error(e);
        }
    }

    private renderStrategies() {
        const data: any = path(['strategy', 'getAllStrategyTests'])(this.props.data) || [];
        const sortedData = sortWith([descend(prop('avgProfit'))])(data)

        return sortedData.map((r, idx) => {
            let rowClass = (idx === 0 ? 'text-accent' : '')

            if (r.status === "Progress") {
                rowClass += " text-primary font-extra-bold"
            }

            const posNegRatio = Number(r.posTrades / r.negTrades).toFixed(2)
            const currentSymbol = r.currentSymbol

            return <tr key={shortid.generate()} className={rowClass}>
                <td>{r.strategy}{currentSymbol ? <span className={"text-muted small m-l-sm"}>{currentSymbol}</span> : null}</td>
                <td>{r.interval}</td>
                <td>{r.sl}</td>
                <td>{r.status}</td>
                <td>{Number(r.bestSymbolProfit).toFixed(2)}%</td>
                <td>{Number(r.worstSymbolProfit).toFixed(2)}%</td>
                <td>{r.bestPerformer}</td>
                <td>{r.worstPerformer}</td>
                <td>{r.numTrades}</td>
                <td>{posNegRatio}</td>
                <td>{Number(r.avgProfit).toFixed(2)}%</td>
                {/*<td>{Number(r.bestTrade).toFixed(2)}%</td>*/}
                {/*<td>{Number(r.avgTrade).toFixed(2)}%</td>*/}
                {/*<td>{Number(r.worstTrade).toFixed(2)}%</td>*/}
                {/*<td>{(Number(r.avgHoldMinutes) / 60).toFixed(2)}h</td>*/}
            </tr>
        })
    }
}