import * as React from "react";
import {Col, Row} from "react-bootstrap";
import {AllStrategies} from "@/components/pages/strategyTester/AllStrategies";
import {PageHeaderWithLinks} from "@/components/common/PageHeaderWithLinks";

export class StrategyTesterPeriodicPage extends React.Component {

    public render(): React.ReactNode {
        return <div className="container-fluid">

            <PageHeaderWithLinks links={[
                {
                    title: 'Single Tests',
                    url: '/strategyTester',
                }, {
                    title: 'Periodic Tests',
                    url: '/strategyTester_periodic',
                    active: true,
                }]}
            />

            <Row>
                <Col lg={12} xs={12}>
                    <AllStrategies/>
                </Col>
            </Row>
        </div>
    }
}

