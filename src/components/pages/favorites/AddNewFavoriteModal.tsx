import {AddNewFavoriteInfo} from "@/graphql/noticedCoins/AddNewFavoriteInfo.graphql";
import {AddNewFavoriteMutation} from "@/graphql/noticedCoins/AddNewFavoriteMutation.graphql";
import {NoticesCoins} from "@/graphql/noticedCoins/NoticedCoins.graphql";
import {observable} from 'mobx';
import {inject, observer} from 'mobx-react';

import {equals, find, path, propEq} from 'ramda';
import * as React from 'react';
import {compose, graphql} from 'react-apollo';
import {Modal} from 'react-bootstrap';
import {error, success} from "@/utils/Toast";

@compose(graphql(AddNewFavoriteInfo), graphql(AddNewFavoriteMutation))
@inject("exchangeStore")
@observer
export class AddNewFavoriteModal extends React.Component<any> {

    @observable public buyPrice = 0;
    @observable public asset = 'DEFAULT';

    public componentWillReceiveProps(nextProps) {
        if (this.props.showModal === false && nextProps.showModal === true) {
            if (this.props.data.refetch) {
                this.props.data.refetch();
            }
        }

        if ((nextProps.showModal && !this.props.showModal)) {
            this.buyPrice = nextProps.buyPrice;
        }
    }

    public onSelect = e => {
        this[e.target.name] = e.target.value;

        const prices: any = path(['data', 'exchange', 'prices'])(this.props) || [];
        const buyPriceObj = find(propEq('symbol', `${e.target.value}USDT`))(prices) || {};

        this.buyPrice = Number(buyPriceObj.price) || 0;
    };

    public onChangeBuyPrice = e => {
        this.buyPrice = e.target.value;
    };

    public onAdd = async () => {
        const result = await this.props.mutate({
            variables: {
                symbol: `${this.asset}USDT`,
                buyPrice: this.buyPrice,
            },
            refetchQueries: [
                {
                    query: NoticesCoins,
                }],
        });

        if (result.data.saveNoticedCoinMutation.ok) {
            success('Favorite Coin added');
            this.props.closeModal();
        } else {
            error(result.data.saveNoticedCoinMutation.error);
        }
    };

    public render() {
        return (
            <Modal show={this.props.showModal} onHide={this.props.closeModal}>
                <Modal.Header closeButton>
                    Add new favorite coin
                </Modal.Header>
                <Modal.Body>
                    <div className="row">
                        <div className="col-lg-6 col-xs-12">
                            {this.renderAssets()}
                        </div>
                        <div className="col-lg-6 col-xs-12">
                            {this.renderBuyPrice()}
                        </div>
                    </div>

                </Modal.Body>

                <Modal.Footer className="panel-footer">
                    <button className="btn btn-default btn-w-sm text-left"
                            onClick={this.props.closeModal}>
                        Close
                    </button>
                    <button className="btn btn-success btn-w-sm pull-right"
                            disabled={equals(this.asset, 'DEFAULT')}
                            onClick={this.onAdd}>
                        Add
                    </button>
                </Modal.Footer>
            </Modal>
        );
    }

    public renderAssets() {
        const {exchangeInfo} = this.props.exchangeStore

        return (
            <div className="form-group">
                <label htmlFor="asset">
                    Symbol
                </label>
                <select className="select2_demo_1 form-control select2-hidden-accessible"
                        tabIndex={-1}
                        name="asset"
                        value={this.asset}
                        onChange={this.onSelect}
                        aria-hidden>
                    {exchangeInfo
                        ? [
                            <option key="default" value="DEFAULT">
                                Select symbol
                            </option>,
                            ...exchangeInfo.map(symbol => (
                                <option key={symbol.baseAsset} value={symbol.baseAsset}>
                                    {symbol.baseAsset}
                                </option>
                            ))]
                        : null}
                </select>
            </div>);
    }

    public renderBuyPrice() {
        return (
            <div className="form-group">
                <label htmlFor="buyPrice">
                    Buy Price
                </label>
                <input type="number"
                       className="form-control"
                       name="buyPrice"
                       value={this.buyPrice}
                       onChange={this.onChangeBuyPrice}/>
            </div>
        );
    }
}
