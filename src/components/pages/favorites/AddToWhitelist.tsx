import {graphql} from "react-apollo";
import * as React from "react";
import {error, success} from "@/utils/Toast";
import {AddNewWhiteListMutation} from "@/graphql/autobot/AddNewWhiteListMutation.graphql";

// @ts-ignore
@graphql(AddNewWhiteListMutation)
export class AddTo<PERSON>hitelist extends React.Component<any> {

    public addToWhitelist = async () => {
        const {symbol} = this.props.coin;

        const result = await this.props.mutate({
            variables: {
                symbol,
            },
        });

        if (result.data.deleteNoticedCoinMutation.ok) {
            success('Favorite Coin added to whitelist');
            this.props.closeModal();
        } else {
            error(result.data.deleteNoticedCoinMutation.error);
        }
    };

    public render() {
        return <button
            className="btn-default btn btn-xs m-l-xs"
            onClick={this.addToWhitelist}>whitelist
        </button>;
    }
}
