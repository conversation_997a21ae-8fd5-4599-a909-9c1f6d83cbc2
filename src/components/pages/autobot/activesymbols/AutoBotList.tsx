import {MPanel} from "@/components/common/MPanel";
import {AutobotAssetsQueryGraphql} from "@/graphql/autobot/AutobotAssetsQuery.graphql";
import PriceCalculation from "@/utils/PriceCalculation";
import gql from "graphql-tag";
import {inject, observer} from "mobx-react";
import {path} from "ramda";
import * as React from "react";
import {graphql, Mutation} from "react-apollo";
import {Table} from "react-bootstrap";
import {Link} from "react-router-dom";
import {error, success} from "@/utils/Toast";

const ShortActiveMutation = gql`
    mutation($symbol: String!) {
        shortMarketBotMutation(symbol: $symbol) {
            ok
        }
    }
`;

// @ts-ignore
@graphql(AutobotAssetsQueryGraphql, {
    options: {
        fetchPolicy: 'network-only',
    },
})
@inject("exchangeStore")
@observer
export class AutoBotList extends React.Component<any, any> {

    constructor(props) {
        super(props);

        this.state = {
            loading: false
        }
    }

    public render() {
        // Fetch
        const bots = path(['bot', 'autobot', 'marketBotsBig'], this.props.data) || [] as any;
        const count = bots.length;

        return <MPanel filled loading={this.props.data.loading}>
            <div className="panel-heading">
                Active Symbols

                <span className={"m-r-xs pull-right"}>{count} coins</span>
            </div>

            <div className="panel-body">
                <small>
                    <Table className="table table-sm">
                        <thead>
                        <tr>
                            <th>Symbol</th>
                            <th className={"visible-lg visible-md visible-sm"}>Add Date</th>
                            <th>Invested</th>
                            <th>Profit</th>
                            <th>Profit $</th>
                        </tr>
                        </thead>
                        <tbody>
                        {this.renderMarketBots(bots)}
                        </tbody>
                    </Table>
                </small>
            </div>
        </MPanel>
    }

    public renderMarketBots(bots) {
        return bots.map(bot => {
            const startDate = new Date(bot.startDate).toLocaleString()
            const invested = Number(bot.investedUsd).toFixed(2)
            const valueUsd = Number(bot.currentValueUsd).toFixed(2)
            const profit = Number(bot.currentProfit).toFixed(2)
            const profitUsd = (Number(valueUsd) - Number(invested))
            return <tr key={bot.symbol}>
                <td><Link to={'/coin/' + bot.symbol}>{bot.symbol}</Link></td>
                <td className={"visible-lg visible-md visible-sm"}>{startDate}</td>
                <td>${invested}</td>
                <td>
                    <span className={PriceCalculation.toClassName(bot.currentProfit)}>{profit}%</span>
                </td>
                <td>
                    <span className={PriceCalculation.toClassName(profitUsd)}>{profitUsd.toFixed(2)}</span>
                </td>
                <td>
                    <Mutation mutation={ShortActiveMutation}>
                        {mutation => <button className="badge badge-danger pull-right btn btn-danger btn-xs"
                                             disabled={this.state.loading}
                                             onClick={() => this.onShort(bot, mutation)}>
                            <i className="fa fa-trash"/></button>
                        }
                    </Mutation>
                </td>
            </tr>
        })
    };

    private onShort = async (bot, mutation) => {
        this.setState({
            loading: true
        })
        try {
            await mutation({
                    variables: {
                        symbol: bot.symbol,
                    },
                    refetchQueries: [{query: AutobotAssetsQueryGraphql}]
                }
            );
            success("Sold coin");
        } catch (e) {
            error(e);
        }

        this.setState({
            loading: false
        })
    };
}