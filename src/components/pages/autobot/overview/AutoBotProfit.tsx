import {Loading} from "@/components/common/Loading";
import {MPanel} from "@/components/common/MPanel";
import PriceCalculation from "@/utils/PriceCalculation";
import {path} from "ramda";
import * as React from "react";
import {Table} from "react-bootstrap";

export class AutoBotProfit extends React.Component<any, any> {
    public render() {
        const {loading} = this.props.data;
        const autobot: any = path(['data', 'bot', 'autobot'])(this.props);

        if (!autobot) {
            return <MPanel filled loading={loading}>
                <div className="panel-body text-center">
                    <Loading/>
                </div>
            </MPanel>
        }

        return (
            <MPanel className="panel-c-primary" filled loading={loading}>
                <div className="panel-heading">
                    Profit Stats
                </div>
                <div className="panel-body">
                    <Table className="table table-condensed small">
                        <thead>
                        <tr>
                            <th>Date</th>
                            <th>%</th>
                            <th>RTB %</th>
                            <th>USD</th>
                        </tr>
                        </thead>
                        <tbody>
                        {this.renderProfit(autobot.profitTodayRelative, autobot.profitToday, autobot.profitTodayUsd, "Today (Relative)")}
                        {this.renderProfitOther(autobot.profitYesterdayRelative, autobot.profitYesterday, autobot.profitYesterdayUsd, "Yesterday")}
                        {this.renderProfitOther(autobot.profitThisWeekRelative, autobot.profitThisWeek, autobot.profitThisWeekUsd, "This week")}
                        {this.renderProfitOther(autobot.profitThisMonthRelative, autobot.profitThisMonth, autobot.profitThisMonthUsd, "This month")}
                        {this.renderProfitOther(autobot.profitLastMonthRelative, autobot.profitLastMonth, autobot.profitLastMonthUsd, "Last month")}
                        {this.renderProfitOther(autobot.profitOverallRelative, autobot.profitOverall, autobot.profitOverallUsd, "Overall")}
                        </tbody>
                    </Table>
                </div>
            </MPanel>);
    }

    private renderProfit(profitTodayRelative, percent, usd, label) {
        return <tr>
            <td>
                <h6>{label}</h6>
            </td>
            <td>
                <h6>
                    {this.renderProfitPercent(percent)}
                </h6>
            </td>
            <td>
                <h6>
                    {this.renderProfitPercentRTB(percent, profitTodayRelative)}
                </h6>
            </td>
            <td>
                <h6>
                    {this.renderProfitUsd(usd)}
                </h6>
            </td>
        </tr>
    }

    private renderProfitOther(profitTodayRelative, percent, usd, label) {
        return <tr>
            <td>
                <h6>{label}</h6>
            </td>
            <td>
                <h6>
                    {this.renderProfitPercentOther(percent)}
                </h6>
            </td>
            <td>
                <h6>
                    {/*{this.renderProfitPercentRTBOther(profitTodayRelative)}*/}
                </h6>
            </td>
            <td>
                <h6>
                    {this.renderProfitUsdOther(usd)}
                </h6>
            </td>
        </tr>
    }

    private renderProfitPercent(profitStr) {
        const profit = Number(profitStr);

        return <span className={PriceCalculation.toClassName(profit) + " m-t-xs"}>
            {PriceCalculation.withPlus(profit.toFixed(2))}%
        </span>
    }

    private renderProfitPercentRTB(profitStr, profitStrRelative) {
        const profit = Number(profitStr);
        const profitRelative = Number(profitStrRelative);

        return <span className={PriceCalculation.toClassName(profit) + " m-t-xs"}>
            {PriceCalculation.withPlus(profitRelative.toFixed(2))}%
        </span>
    }

    private renderProfitUsd(profitStr) {
        const profitUsd = Number(profitStr);

        return <span className={PriceCalculation.toClassName(profitUsd) + " m-t-xs"}>
            ${profitUsd.toFixed(2)}</span>
    }

    private renderProfitPercentRTBOther(profitStrRelative) {
        const profitRelative = Number(profitStrRelative);

        return <span className={"text-muted m-t-xs"}>
            {PriceCalculation.withPlus(profitRelative.toFixed(2))}%
        </span>
    }

    private renderProfitPercentOther(profitStr) {
        const profit = Number(profitStr);

        return <span className={"text-muted m-t-xs"}>
            {PriceCalculation.withPlus(profit.toFixed(2))}%</span>
    }

    private renderProfitUsdOther(profitStr) {
        const profitUsd = Number(profitStr);

        return <span className={"text-muted m-t-xs"}>${profitUsd.toFixed(2)}</span>
    }
}