import {MPanel} from "@/components/common/MPanel";
import {AutobotQuery} from "@/graphql/autobot/AutobotQuery.graphql";
import PriceCalculation from "@/utils/PriceCalculation";
import gql from "graphql-tag";
import {observable} from "mobx";
import {observer} from "mobx-react";
import {find, path, propEq} from "ramda";
import * as React from "react";
import {Mutation, withApollo} from "react-apollo";
import {error, success} from "@/utils/Toast";
import {AddAutobotModal} from "@/components/pages/autobot/overview/AddAutobotModal";

const StartAutoBotMutation = gql`
    mutation startAutobotMutation ($botId: String!) {
        startAutoBotMutation(botId: $botId) {
            ok
        }
    }
`;

const StopAutoBotMutation = gql`
    mutation stopAutobotMutation ($botId: String!) {
        stopAutoBotMutation(botId: $botId) {
            ok
        }
    }
`;

const BuyNowMutation = gql`
    mutation buyNowMutation ($botId: String!) {
        buyNowMutation(botId: $botId) {
            ok
        }
    }
`;

const SellAllMutation = gql`
    mutation sellAllMutation ($botId: String!) {
        sellAllMutation(botId: $botId) {
            ok
        }
    }
`;


// @ts-ignore
@withApollo
@observer
export class AutoBotStatus extends React.Component<any, any> {

    @observable public mode = ''

    constructor(props) {
        super(props);

        this.state = {
            showAddModal: false,
        }
    }

    public render() {
        // Fetch
        const {data} = this.props;
        const autobot: GQL.IAutoBot = path(['bot', 'autobot'])(data) as any;

        if (!autobot) {
            return <MPanel filled>
                <div className="panel-body text-center">
                    <button className="btn btn-w-md btn-success btn-rounded"
                            onClick={this.onShowModal}>Add new AutoBot
                    </button>
                </div>

                <AddAutobotModal showModal={this.state.showAddModal} closeModal={this.closeModal}/>
            </MPanel>
        }

        const balances = path(['exchange', 'accountInfo', 'balances'])(data) || {} as any;
        const {
            strategy, accumulatedValueUsd, activeCount,
            accumulatedInvestUsd, accumulatedProfit,
            accumulatedProfitRelative, lastTradeTimestamp, active,
            interval
        } = autobot;

        // Modify
        const currentValueUsd = accumulatedValueUsd ? Number(accumulatedValueUsd) : 0;
        const investedValueUsd = accumulatedInvestUsd ? Number(accumulatedInvestUsd) : 0;

        const currentProfit = accumulatedProfit ? Number(accumulatedProfit) : 0;
        const currentProfitRelative = accumulatedProfitRelative ? Number(accumulatedProfitRelative) : 0;
        const accumulatedProfitUsd = currentValueUsd - investedValueUsd;
        const lastTrade = lastTradeTimestamp ? new Date(lastTradeTimestamp).toLocaleString() : 'never';

        const usdtBalance = find(propEq('asset', 'USDT'))(balances) || {};
        const freeUsd = usdtBalance.usdValue;

        return (
            <MPanel className="panel-c-primary" filled loading={data.loading}>
                <div className="panel-heading">
                    Bot Status

                    {this.renderStopBotButton(autobot, data.loading)}
                    {this.renderSellAllButton(autobot, data.loading)}
                    {this.renderBuyNow(autobot, data.loading)}
                </div>
                <div className="panel-body">
                    <table className="table table-condensed table-no-border">
                        <tbody>
                        <tr>
                            <td>
                                <h6>Status</h6>
                            </td>
                            <td className="text-right">
                                <span><b>{active ? <span className="text-success">Running</span> :
                                    <span className="text-danger">Stopped</span>}
                                </b>
                                </span>
                            </td>
                        </tr>

                        <tr>
                            <td>
                                <h6>Strategy</h6>
                            </td>
                            <td className="text-right">
                                <h6>
                                    <span className={"text-muted"}>{strategy} {interval}</span>
                                </h6>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <h6>Last Trade</h6>
                            </td>
                            <td className="text-right">
                                <h6>
                                    <span className={"text-muted"}>{lastTrade}</span>
                                </h6>
                            </td>
                        </tr>

                        <tr>
                            <td>
                                <h6>Active symbols</h6>
                            </td>
                            <td className="text-right">
                                <h6>
                                    {activeCount}
                                </h6>
                            </td>
                        </tr>

                        <tr>
                            <td>
                                <h6>Accumulated Profit % (on active)
                                </h6>
                            </td>
                            <td>
                                <h6>
                                    <b><span className={'m-l-xs pull-right ' +
                                        PriceCalculation.toClassName(currentProfit)}>
                                        {PriceCalculation.withPlus(currentProfit.toFixed(2))}% | RTB {PriceCalculation.withPlus(currentProfitRelative.toFixed(2))}%</span>
                                    </b>
                                </h6>
                            </td>
                        </tr>


                        <tr>
                            <td>
                                <h6>Accumulated Profit USD (on active)
                                </h6>
                            </td>
                            <td>
                                <h6>
                                    <b><span
                                        className={'m-l-xs pull-right ' + PriceCalculation.toClassName(accumulatedProfitUsd)}>
                                        {PriceCalculation.withPlus(accumulatedProfitUsd.toFixed(2))} USD</span>
                                    </b>
                                </h6>
                            </td>
                        </tr>

                        <tr>
                            <td>
                                <h6>Invested $</h6>
                            </td>
                            <td>
                                <span className={"m-l-xs pull-right"}>
                                <h6>{Number(investedValueUsd).toFixed(2)} USD</h6>
                                </span>
                            </td>
                        </tr>

                        <tr>
                            <td>
                                <h6>Free $</h6>
                            </td>
                            <td>
                                <span className={"m-l-xs pull-right"}>
                                <h6>
                                    {Number(freeUsd).toFixed(2)} USD</h6>
                                </span>
                            </td>
                        </tr>

                        </tbody>
                    </table>
                </div>
            </MPanel>);
    }

    public renderStopBotButton(autobot, loading) {
        if (autobot && !loading) {
            return <span>{autobot.active ?
                <Mutation mutation={StopAutoBotMutation}>
                    {mutation => <button className="badge badge-danger pull-right btn btn-danger"
                                         onClick={() => this.onStopBot(autobot, mutation)}>Stop</button>
                    }
                </Mutation> :
                <Mutation mutation={StartAutoBotMutation}>
                    {mutation => <button className="badge badge-success pull-right btn btn-success"
                                         onClick={() => this.onStartBot(autobot, mutation)}>Start</button>
                    }
                </Mutation>}
                                </span>
        }

        return null;
    }

    public renderBuyNow(autobot, loading) {
        if (autobot && !loading) {
            return <span>{autobot.active ?
                <Mutation mutation={BuyNowMutation}>
                    {mutation => <button className="badge pull-right btn btn-danger"
                                         onClick={() => this.onBuyNow(autobot, mutation)}>Buy Now</button>
                    }
                </Mutation> : null}
                </span>
        }

        return null;
    }

    public renderSellAllButton(autobot, loading) {
        if (autobot && !loading) {
            return <span>{!autobot.active ?
                <Mutation mutation={SellAllMutation}>
                    {mutation => <button className="badge badge-danger pull-right btn btn-danger"
                                         onClick={() => this.onSellAll(autobot, mutation)}>Sell all</button>
                    }
                </Mutation> : null}
                                </span>
        }

        return null;
    }

    private onSellAll = async (botData, mutation) => {
        try {
            await mutation({
                    variables: {
                        botId: botData._id
                    },
                    refetchQueries: [{query: AutobotQuery}]
                }
            );
            success("Sold all coins");
        } catch (e) {
            error(e);
        }
    };

    private onBuyNow = async (botData, mutation) => {
        try {
            await mutation({
                    variables: {
                        botId: botData._id
                    },
                    refetchQueries: [{query: AutobotQuery}]
                }
            );
            success("Bought coins on current bot settings");
        } catch (e) {
            error(e);
        }
    };

    private onStartBot = async (botData, mutation) => {
        try {
            await mutation({
                    variables: {
                        botId: botData._id
                    },
                    refetchQueries: [{query: AutobotQuery}]
                }
            );
            success("AutoTrader started");
        } catch (e) {
            error(e);
        }
    };


    private onStopBot = async (botData, mutation) => {
        try {
            await mutation({
                    variables: {
                        botId: botData._id
                    },
                    refetchQueries: [{query: AutobotQuery}]
                }
            );
            success("AutoTrader stopped");
        } catch (e) {
            error(e);
        }
    };

    public onShowModal = () => {
        this.setState({showAddModal: true})
    };

    public closeModal = () => {
        this.setState({showAddModal: false})
    };
}