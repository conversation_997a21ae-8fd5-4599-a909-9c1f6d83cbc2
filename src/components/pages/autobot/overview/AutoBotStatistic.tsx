import {MPanel} from "@/components/common/MPanel";
import * as React from "react";
import {Loading} from "@/components/common/Loading";
import {path} from "ramda";

export class AutoBotStatistic extends React.Component<any> {
    public render() {
        const {loading} = this.props.data;
        const autobot: any = path(['data', 'bot', 'autobot'])(this.props);

        if (!autobot) {
            return <MPanel filled loading={loading}>
                <div className="panel-body text-center">
                    <Loading/>
                </div>
            </MPanel>
        }

        return (
            <MPanel className="panel-c-primary" filled loading={loading}>
                <div className="panel-heading">
                    Trade Stats
                </div>
                <div className="panel-body">
                    <table className="table table-condensed table-no-border">
                        <tbody>

                        <tr>
                            <td>
                                <h6>Number Trades Today</h6>
                            </td>
                            <td className="text-right">
                                <h6>
                                    {autobot.tradesToday} ({autobot.tradesTodayLong} long, {autobot.tradesTodayShort} short)
                                </h6>
                            </td>
                        </tr>

                        <tr>
                            <td>
                                <h6>Success Rate</h6>
                            </td>
                            <td className="text-right">
                                <h6>
                                    <span
                                        className={autobot.winsToday > autobot.losesToday ? 'text-success' : 'text-danger'}>
                                        {Number(autobot.successRateToday).toFixed(2)}%
                                    </span> ({autobot.winsToday} wins, {autobot.losesToday} loses)
                                </h6>
                            </td>
                        </tr>

                        </tbody>
                    </table>
                </div>
            </MPanel>);
    }
}