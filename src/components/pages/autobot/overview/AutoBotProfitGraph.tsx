import {MPanel} from "@/components/common/MPanel";
import {observer} from "mobx-react";
import * as React from "react";
import {Loading} from "@/components/common/Loading";
import {path} from "ramda";
import {VerticalBarSeries, Crosshair, makeWidthFlexible, XAxis, XYPlot, YAxis,} from "react-vis"
import {observable} from "mobx";

const FlexibleXYPlot = makeWidthFlexible(XYPlot);

@observer
export class AutoBotProfitGraph extends React.Component<any> {
    @observable public crosshairValues = [];

    public render() {
        const {loading} = this.props.data;
        const profitGraphData: any = path(['data', 'bot', 'profitGraph'])(this.props) || [];

        if (loading) {
            return <MPanel filled loading={loading}>
                <div className="panel-body text-center">
                    <Loading/>
                </div>
            </MPanel>
        }

        return (
            <MPanel filled loading={loading}>
                <div className="panel-heading">
                    Daily Income {this.props.usd ? 'USD' : '%'}
                </div>
                <div className="panel-body">
                    <FlexibleXYPlot
                        onMouseLeave={this.onMouseLeave}
                        xType="ordinal"
                        margin={{bottom: 0}}
                        height={110}>
                        <XAxis title="Time"
                               tickFormat={time => new Date(time).getTime()}/>
                        <YAxis title={this.props.usd ? 'USD' : '%'}/>
                        <VerticalBarSeries data={this.getChartData(profitGraphData)}
                                           onNearestX={this.onNearestBarX}
                                           colorType="literal"
                                           getColor={d => {
                                               return d.y < 0 ? '#a54242' : '#479f47';
                                           }}
                                           color={"#f6a821"}/>
                        <Crosshair values={this.crosshairValues}
                                   titleFormat={this.formatCrosshairTitle}
                                   itemsFormat={this.formatCrosshairItems}/>
                    </FlexibleXYPlot>
                </div>
            </MPanel>);
    }

    public getChartData(profitGraph) {
        return profitGraph.map(item => {
            return {
                x: item.timestamp,
                y: this.props.usd ? item.usdHistory : item.percentHistory
            }
        });
    }


    public onNearestBarX = (_value, {index}) => {
        const profitGraphData: any = path(['data', 'bot', 'profitGraph'])(this.props) || [];

        const profits: any = this.getChartData(profitGraphData);
        this.crosshairValues = [profits[index]];
    };

    public formatCrosshairTitle = values => ({
        title: 'Date',
        value: new Date(values[0].x).toLocaleDateString()
    });

    public formatCrosshairItems = values => {
        return values.map((_v, _i) => ({
            title: "USD",
            value: Number(values[0].y).toFixed(2)
        }));
    };

    public onMouseLeave = () => {
        this.crosshairValues = [];
    };
}