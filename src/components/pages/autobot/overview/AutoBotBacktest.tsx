import {MPanel} from "@/components/common/MPanel";
import * as React from "react";
import {descend, path, prop, sortWith} from "ramda";
import {graphql, withA<PERSON>lo} from "react-apollo";
import {AutobotBacktestQueryGraphql} from "@/graphql/autobot/AutobotBacktestQuery.graphql";
import * as shortid from "shortid";
import {Panel, Table} from "react-bootstrap";
import {AutobotBacktestRecalcMutationGraphql} from "@/graphql/autobot/AutobotBacktestRecalcMutation.graphql";
import {error, success} from "@/utils/Toast";

// @ts-ignore
@withApollo
// @ts-ignore
@graphql(AutobotBacktestQueryGraphql)
export class AutoBotBacktest extends React.Component<any> {
    public render() {
        return <MPanel filled className="">
            <Panel.Heading>
                Whitelist Strategy Backtest

                <span className={"pull-right text-muted small"} style={{marginTop: '3px'}}>from 01.01.2023
                </span>

                <span className="pull-right">
                <button className="badge btn btn-success m-r-xs" onClick={this.onRecalculate}><i
                    className={"fa fa-refresh"}></i></button>
                </span>
            </Panel.Heading>

            <Panel.Body>
                <Table className="table table-condensed small">
                    <thead>
                    <tr>
                        <th>Strategy</th>
                        <th>Interval</th>
                        <th>Avg Profit</th>
                    </tr>
                    </thead>
                    <tbody>
                    {this.renderStrategies()}
                    </tbody>
                </Table>
            </Panel.Body>
        </MPanel>
    }

    private renderStrategies() {
        const data: any = path(['strategy', 'getAllStrategyTests'])(this.props.data) || [];
        const sortedData = sortWith([descend(prop('avgProfit'))])(data)

        return sortedData.slice(0, 10).map((r, idx) => {
            let rowClass = (idx === 0 ? ' text-accent ' : '')

            if (r.status === "Progress") {
                rowClass += " text-primary font-extra-bold "
            }

            return <tr key={shortid.generate()} className={rowClass}>
                <td>{r.strategy}</td>
                <td>{r.interval}</td>
                <td>{Number(r.avgProfit).toFixed(2)}%</td>
                {/*<td>{Number(r.bestTrade).toFixed(2)}%</td>*/}
                {/*<td>{Number(r.avgTrade).toFixed(2)}%</td>*/}
                {/*<td>{Number(r.worstTrade).toFixed(2)}%</td>*/}
                {/*<td>{(Number(r.avgHoldMinutes) / 60).toFixed(2)}h</td>*/}
            </tr>
        })
    }

    private onRecalculate = async () => {
        const {client} = this.props

        try {
            await client.mutate({
                mutation: AutobotBacktestRecalcMutationGraphql,
            })
            success(`Recalculating backtest..`);
        } catch (e) {
            error(e);
        }
    }
}