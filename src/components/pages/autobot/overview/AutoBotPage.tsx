import {PageHeaderWithLinks} from "@/components/common/PageHeaderWithLinks";
import {AutoBotProfit} from "@/components/pages/autobot/overview/AutoBotProfit";
import {AutoBotProfitGraph} from "@/components/pages/autobot/overview/AutoBotProfitGraph";
import {AutoBotStatistic} from "@/components/pages/autobot/overview/AutoBotStatistic";
import {AutoBotStatus} from "@/components/pages/autobot/overview/AutoBotStatus";
import {AutobotQuery} from "@/graphql/autobot/AutobotQuery.graphql";
import * as React from 'react';
import {graphql} from "react-apollo";
import {Col, Row} from "react-bootstrap";
import {AutoBotBacktest} from "@/components/pages/autobot/overview/AutoBotBacktest";

// @ts-ignore
@graphql(AutobotQuery, {
    options: {
        pollInterval: 10000,
        fetchPolicy: 'network-only'
    },
})
export class AutoBotPage extends React.Component<any> {

    public render() {
        const {data} = this.props;

        return <div className="container-fluid">

            <PageHeaderWithLinks links={[
                {
                    title: 'Overview',
                    url: '/autobot',
                    active: true,
                }, {
                    title: 'Whitelist',
                    url: '/autobot_whitelist',
                },
                {
                    title: 'Strategy View',
                    url: '/autobot_strategy',
                },
                {
                    title: 'Suggests',
                    url: '/autobot_suggest',
                }, {
                    title: 'Active Symbols',
                    url: '/autobot_assets',
                }, {
                    title: 'Trades',
                    url: '/autobot_trades',
                }, {
                    title: 'Settings',
                    url: '/autobot_settings',
                }]}/>

            <Row>
                <Col lg={4} md={12}>
                    <AutoBotStatus data={data}/>
                </Col>
                <Col lg={4} md={12}>
                    <AutoBotProfit data={data}/>
                    <AutoBotStatistic data={data}/>
                </Col>
                <Col lg={4} md={12}>
                    <Row>
                        <Col lg={12}>
                            <AutoBotProfitGraph usd={true} data={data}/>
                        </Col>
                    </Row>
                    <Row>
                        <Col lg={12}>
                            <AutoBotBacktest usd={true} data={data}/>
                        </Col>
                    </Row>
                </Col>
            </Row>
        </div>
    }
}
