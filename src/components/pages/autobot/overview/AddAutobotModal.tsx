import {AutobotQuery} from "@/graphql/autobot/AutobotQuery.graphql";
import gql from "graphql-tag";
import {action, observable} from 'mobx';
import {observer} from 'mobx-react';
import {path} from 'ramda';
import * as React from 'react';
import {graphql, Mutation} from 'react-apollo';
import {Col, ControlLabel, FormControl, FormGroup, Modal, Row} from 'react-bootstrap';
import {error, success} from "@/utils/Toast";

const GetStrategiesQuery = gql`
    query GetStrategiesQuery {
        bot {
            formInfo {
                strategies {
                    name
                }
            }
        }
    }
`;

const AddAutoBotMutation = gql`
    mutation AddAutoBotMutation($strategy: String!, $usdPerCoin: Float!) {
        createAutoBot(strategy: $strategy, usdPerCoin: $usdPerCoin) {
            ok
        }
    }
`;

// @ts-ignore
@graphql(GetStrategiesQuery)
@observer
export class AddAutobotModal extends React.Component<any> {

    @observable public selection = {
        strategy: 'nesterov',
        usdPerCoin: '90',
    };

    @action public onSelect = (key, value) => {
        this.selection[key] = value;
    };

    public onAdd = async mutation => {
        const {strategy, usdPerCoin} = this.selection;
        const result = await mutation({
            variables: {
                strategy,
                usdPerCoin: Number(usdPerCoin),
            },
            refetchQueries: [{query: AutobotQuery}]
        });

        if (result.data.createAutoBot.ok) {
            success('Autobot added');
            this.props.closeModal();
        } else {
            error(result.data.createAutoBot.error);
        }
    };

    public render() {
        return (
            <Modal show={this.props.showModal} onHide={this.props.closeModal}>
                <Modal.Header closeButton>
                    Add new AutoTrader
                </Modal.Header>
                <Modal.Body>
                    <Row>
                        <Col lg={12}>
                            {this.renderStrategies()}
                        </Col>
                    </Row>

                </Modal.Body>

                <Modal.Footer className="panel-footer">
                    <button className="btn btn-default btn-w-sm text-left"
                            onClick={this.props.closeModal}>
                        Close
                    </button>
                    <Mutation mutation={AddAutoBotMutation}>
                        {mutation => {
                            return <button className="btn btn-success btn-w-sm pull-right"
                                           onClick={() => this.onAdd(mutation)}>
                                Add
                            </button>
                        }}
                    </Mutation>
                </Modal.Footer>
            </Modal>
        );
    }

    public renderStrategies() {
        const strategies: any = path(['data', 'bot', 'formInfo', 'strategies'])(this.props) || [];

        return (
            <div>
                <FormGroup controlId="strategy">
                    <ControlLabel>
                        Strategy
                    </ControlLabel>
                    <FormControl componentClass="select"
                                 onChange={e => this.onSelect('strategy', e.target.value)}
                                 value={this.selection.strategy}>
                        {strategies
                            ? [
                                strategies.map(item => (
                                    <option key={item.name} value={item.name}>
                                        {item.name}
                                    </option>
                                ))]
                            : null}
                    </FormControl>
                </FormGroup>

                <FormGroup controlId="usdPerCoin">
                    <ControlLabel>
                        USD Per Symbol
                    </ControlLabel>
                    <FormControl type="number"
                                 onChange={e => this.onSelect('usdPerCoin', e.target.value)}
                                 value={this.selection.usdPerCoin}>
                    </FormControl>
                </FormGroup>
            </div>
        );
    }
}
