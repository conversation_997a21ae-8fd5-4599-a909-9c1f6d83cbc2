import {MPanel} from "@/components/common/MPanel";
import {AutobotLastOrdersQueryGraphql} from "@/graphql/autobot/AutobotLastOrdersQuery.graphql";
import PriceCalculation from "@/utils/PriceCalculation";
import {inject} from "mobx-react";
import {path} from "ramda";
import * as React from "react";
import {graphql} from "react-apollo";
import {Link} from "react-router-dom";

// @ts-ignore
@graphql(AutobotLastOrdersQueryGraphql, {
    options: {
        fetchPolicy: 'network-only',
        pollInterval: 30000,
    },
})
@inject("exchangeStore")
export class AutoBotLastTrades extends React.Component<any, any> {

    public render() {
        return <div className="row">
            <div className="col-xs-12 col-lg-12">
                <MPanel filled loading={this.props.loading}>
                    <div className="panel-heading">
                        Trades
                    </div>

                    <div className="panel-body" style={{overflowX: 'auto'}}>
                        <table className="table table-condensed small">
                            <thead>
                            <tr>
                                <th>Symbol</th>
                                <th>Order Date</th>
                                <th className={"visible-lg visible-md"}>Price</th>
                                <th className={"visible-lg visible-md"}>Side</th>
                                <th>Value USD</th>
                                <th>Profit %</th>
                                <th>Profit USD</th>
                            </tr>
                            </thead>
                            <tbody>
                            {this.getOrders()}
                            </tbody>
                        </table>
                    </div>
                </MPanel>
            </div>
        </div>
    }

    private getOrders() {
        const {exchangeStore} = this.props
        const lastOrders = path(['bot', 'lastBotOrders'], this.props.data) || [] as any;

        if (lastOrders.length === 0) {
            return (<tr>
                <td colSpan={7}>
                    No orders
                </td>
            </tr>);
        }

        return lastOrders.map(order => {
            const price = PriceCalculation.getFixedPriceWithLength(Number(order.price), exchangeStore.exchangeInfo, order.asset)
            const tradeDate = new Date(order.tradeDate).toLocaleString()
            const profitPercent = Number(order.profit).toFixed(2)
            const profitUsd = Number(order.profitUsd).toFixed(2)

            return <tr key={order.tradeDate}>
                <td>
                    <Link to={'/coin/' + order.asset}>
                        {order.asset}
                    </Link>
                </td>
                <td>{tradeDate}</td>
                <td className={"visible-lg visible-md"}>
                    <span className="">{price}</span>
                </td>
                <td className={"visible-lg visible-md"}><b>{order.position}</b></td>
                <td>{order.valueInUsd}</td>
                <td>
                    <span className={PriceCalculation.toClassName(Number(order.profit).toFixed(2))}>{profitPercent}</span>
                </td>
                <td>
                    <span className={PriceCalculation.toClassName(Number(order.profitUsd).toFixed(2))}><b>{profitUsd}</b></span>
                </td>
            </tr>
        })
    }
}