import PageHeader from "@/components/common/PageHeader";
import {AddNewWhiteListMutation} from "@/graphql/autobot/AddNewWhiteListMutation.graphql";
import {WhiteListCoins} from "@/graphql/autobot/WhiteListCoins.graphql";
import {AddNewFavoriteInfo} from "@/graphql/noticedCoins/AddNewFavoriteInfo.graphql";
import {observer} from "mobx-react";
import {path} from "ramda";
import * as React from 'react';
import {compose, graphql} from "react-apollo";
import {Col, Row, Table} from "react-bootstrap";
import {Link} from "react-router-dom";
import * as shortid from "shortid";
import {error, success} from "@/utils/Toast";

// @ts-ignore
@compose(graphql(AddNewFavoriteInfo, {
    options: {
        fetchPolicy: 'network-only'
    }
}), graphql(AddNewWhiteListMutation))
@observer
export class AutoBotSuggestList extends React.Component<any> {

    public onAdd = async (symbol: string) => {
        try {
            const result = await this.props.mutate({
                variables: {
                    symbol,
                },
                refetchQueries: [
                    {
                        query: WhiteListCoins,
                    }],
            });

            if (result.data.saveWhiteListCoinMutation.ok) {
                success('Whitelist Coin added');
                this.props.closeModal();
            } else {
                error(result.data.saveWhiteListCoinMutation.error);
            }
        } catch (e) {
            console.error(e)
        }
    };

    public render() {
        const coins: any = path(['bot', 'suggestList'])(this.props.data) || [];

        const count = coins.length;

        return (
            <div className="container-fluid">
                <PageHeader title="Binance Suggestions" section="Binance Suggestions"/>

                <Row>
                    <Col lg={12} md={12}>
                        <div className="panel panel-filled">
                            <div className="panel-heading">
                                Binance Suggestions

                                <span className={"m-r-xs pull-right"}>{count} coins</span>
                            </div>

                            <div className="panel-body" style={{overflowX: 'auto'}}>
                                <Table className="table-condensed small">
                                    <thead>
                                    <tr>
                                        <th>Symbol</th>
                                        <th>Total Count</th>
                                        <th className={"visible-lg visible-md"}>Neural Count</th>
                                        <th>Long Count</th>
                                        <th>Short Count</th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {this.getWhiteList()}
                                    </tbody>
                                </Table>
                            </div>
                        </div>
                    </Col>
                </Row>
            </div>);
    }

    public getWhiteList = () => {
        const toRender = []
        const coins: any = path(['bot', 'suggestList'])(this.props.data) || [];
        const whitelist: any[] = path(['bot', 'whitelist'])(this.props.data) || [];

        coins.forEach(coin => {
            const onWhiteList: any = whitelist.filter(x => x.symbol === coin.symbol).length > 0

            toRender.push(<tr key={shortid.generate()}>
                <td><b><Link to={"/coin/" + coin.symbol}>{coin.symbol}</Link></b></td>
                <td>{coin.totalCount}</td>
                <td className={"visible-lg visible-md"}>{coin.neuralCount}</td>
                <td>{coin.longCount}</td>
                <td>{coin.shortCount}</td>
                <td style={{height: '31px'}}>
                    {onWhiteList ? null : <button className="badge badge-danger btn btn-danger btn-xs"
                                                  onClick={() => this.onAdd(coin.symbol)}>
                        <i className="fa fa-plus"/>
                    </button>}
                </td>
            </tr>);
        });

        return toRender;
    };

}
