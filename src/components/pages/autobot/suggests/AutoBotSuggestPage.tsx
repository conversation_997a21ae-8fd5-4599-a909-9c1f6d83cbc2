import {PageHeaderWithLinks} from "@/components/common/PageHeaderWithLinks";
import {AutoBotSuggestList} from "@/components/pages/autobot/suggests/AutoBotSuggestList";
import * as React from 'react';
import {Col, Row} from "react-bootstrap";

export class AutoBotSuggestPage extends React.Component<any> {

    public render() {
        const {data} = this.props;

        return <div className="container-fluid">
            <PageHeaderWithLinks links={[
                {
                    title: 'Overview',
                    url: '/autobot',
                }, {
                    title: 'Whitelist',
                    url: '/autobot_whitelist',
                },
                {
                    title: 'Strategy View',
                    url: '/autobot_strategy',
                },
                {
                    title: 'Suggests',
                    url: '/autobot_suggest',
                    active: true
                },
                {
                    title: 'Active Symbols',
                    url: '/autobot_assets',
                }, {
                    title: 'Trades',
                    url: '/autobot_trades',
                }, {
                    title: 'Settings',
                    url: '/autobot_settings',
                }]}/>

            <Row>
                <Col lg={12} md={12}>
                    <AutoBotSuggestList data={data}/>
                </Col>
            </Row>
        </div>
    }
}
