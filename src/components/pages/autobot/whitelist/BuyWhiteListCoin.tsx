import {WhiteListCoins} from "@/graphql/autobot/WhiteListCoins.graphql";
import * as React from "react";
import {graphql} from "react-apollo";
import gql from "graphql-tag";
import {error, success} from "@/utils/Toast";

const BuyWhiteListCoinMutation = gql`mutation ($symbol: String!) {
    buyWhiteListCoinMutation(symbol: $symbol) {
        ok
        error
    }
}`

// @ts-ignore
@graphql(BuyWhiteListCoinMutation)
export class BuyWhiteListCoin extends React.Component<any> {
    constructor(props) {
        super(props);

        this.state = {
            loading: false
        }
    }

    public buyWhiteListCoin = async () => {
        const {symbol} = this.props.coin;

        this.setState({
            loading: true
        })

        const result = await this.props.mutate({
            variables: {
                symbol,
            },
            refetchQueries: [
                {
                    query: WhiteListCoins,
                }],
        });

        if (result.data.buyWhiteListCoinMutation.ok) {
            success('Whitelist Coin bought');
            this.props.closeModal();
        } else {
            error(result.data.buyWhiteListCoinMutation.error);
        }

        this.setState({
            loading: false
        })
    };

    public render() {
        const disabled = this.props.isAlreadyActive ? "disabled opacity-0" : ""

        return <button
            className={"badge badge-danger btn btn-success btn-xs m-r-sm " + disabled}
            disabled={this.props.isAlreadyActive || this.state.loading}
            onClick={this.buyWhiteListCoin}>
            <i className="fa fa-cart-plus"/>
        </button>;
    }
}
