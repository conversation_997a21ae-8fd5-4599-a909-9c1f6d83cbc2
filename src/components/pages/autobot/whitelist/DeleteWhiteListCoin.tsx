import {DeleteWhiteListCoinMutation} from "@/graphql/autobot/DeleteWhiteListCoinMutation";
import {WhiteListCoins} from "@/graphql/autobot/WhiteListCoins.graphql";
import * as React from "react";
import {graphql} from "react-apollo";
import {error, success} from "@/utils/Toast";

// @ts-ignore
@graphql(DeleteWhiteListCoinMutation)
export class DeleteWhiteListCoin extends React.Component<any> {

    public deleteWhiteListCoin = async () => {
        const {symbol} = this.props.coin;

        const result = await this.props.mutate({
            variables: {
                symbol,
            },
            refetchQueries: [
                {
                    query: WhiteListCoins,
                }],
        });

        if (result.data.deleteWhiteListCoinMutation.ok) {
            success('Whitelist coin deleted');
            this.props.closeModal();
        } else {
            error(result.data.deleteWhiteListCoinMutation.error);
        }
    };

    public render() {
        return <button
            className="badge badge-danger btn btn-danger btn-xs"
            onClick={this.deleteWhiteListCoin}>
            <i className="fa fa-minus"/>
        </button>;
    }
}
