import PageHeader from "@/components/common/PageHeader";
import {AddNewWhiteListModal} from "@/components/pages/autobot/whitelist/AddNewWhiteListModal";
import {DeleteWhiteListCoin} from "@/components/pages/autobot/whitelist/DeleteWhiteListCoin";
import {WhiteListCoins} from "@/graphql/autobot/WhiteListCoins.graphql";
import {observable} from "mobx";
import {observer} from "mobx-react";
import {path, prop, sortBy} from "ramda";
import * as React from 'react';
import {graphql} from "react-apollo";
import {Col, Row, Table} from "react-bootstrap";
import {Link} from "react-router-dom";
import * as shortid from "shortid";
import {BuyWhiteListCoin} from "@/components/pages/autobot/whitelist/BuyWhiteListCoin";
import {DisableWhiteListCoin} from "@/components/pages/autobot/whitelist/DisableWhiteListCoin";

// @ts-ignore
@graphql(WhiteListCoins, {options: {fetchPolicy: 'network-only'}})
@observer
export class AutoBotWhiteList extends React.Component<any> {

    @observable public showAddModal = false;

    public onAddWhiteList = () => {
        this.showAddModal = true;
    };

    public closeModal = () => {
        this.showAddModal = false;
    };

    public render() {
        const lst: any = path(['bot', 'whitelist'])(this.props.data) || [];
        const count = lst.length;
        const long = lst.filter(x => x.indicator && x.indicator.action === "long").length;
        const short = lst.filter(x => x.indicator && x.indicator.action === "short").length;

        return (
            <div className="container-fluid">

                <PageHeader title="Whitelist Coins" section="Whitelist Coins"/>

                <Row>
                    <Col lg={12} md={12}>
                        <div className="panel panel-filled">
                            <div className="panel-heading">
                                Whitelist

                                <span className="m-l-sm btn btn-success btn-xs m-r-xs pull-right"
                                      onClick={this.onAddWhiteList}>
                                    Add coin
                                </span>
                                <div className={"m-l-xs visible-lg visible-md visible-sm pull-right"}>{count} coins
                                    ({long} long / {short} short)
                                </div>
                            </div>

                            <div className="panel-body" style={{overflowX: 'auto'}}>
                                <Table className="table-condensed small">
                                    <thead>
                                    <tr>
                                        <th>Symbol</th>
                                        <th className={"visible-md visible-lg visible-sm"}>Added</th>
                                        <th>Indicator</th>
                                        <th className={"visible-lg visible-md visible-sm"}>AutoAdded</th>
                                        <th>Actions</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {this.getWhiteList()}
                                    </tbody>
                                </Table>
                            </div>
                        </div>
                    </Col>
                </Row>

                <AddNewWhiteListModal showModal={this.showAddModal} closeModal={this.closeModal}/>
            </div>);
    }

    public getWhiteList = () => {
        const suggestList: any = path(['bot', 'suggestList'])(this.props.data) || [];
        let whitelist: any = path(['bot', 'whitelist'])(this.props.data) || [];
        whitelist = sortBy(prop('symbol'))(whitelist);
        const toRender = [];

        if (whitelist.length === 0) {
            return (<tr>
                <td colSpan={3}>
                    No coins
                </td>
            </tr>);
        }

        const activeMarkets = path(['bot', 'autobot', 'marketBotsBig'], this.props.data) || [] as any;

        whitelist.forEach(coin => {
            const {indicator} = coin
            const onSuggestList: any = suggestList.map(x => x.symbol).includes(coin.symbol)
            const isAlreadyActive = activeMarkets.map(x => x.symbol).includes(coin.symbol)

            let indicatorClass = ""

            if (indicator) {
                if (indicator.action === "long") {
                    indicatorClass = "fa fa-arrow-up text-success"
                } else if (indicator.action === "short") {
                    indicatorClass = "fa fa-arrow-down text-danger"
                }
            }

            let coinClass = onSuggestList ? null : 'text-danger'
            if (coin.disabled) {
                coinClass = "text-muted"
                indicatorClass = "text-muted"
            }

            toRender.push(<tr key={shortid.generate()}>
                <td><b><Link to={"/coin/" + coin.symbol}
                             className={coinClass}>{coin.symbol}</Link></b></td>
                <td className={"visible-lg visible-md visible-sm"}>{new Date(Number(coin.timestamp)).toLocaleString()}</td>
                <td><i className={indicatorClass}/></td>
                <td className={"visible-md visible-lg visible-sm"}>{String(coin.autoAdded)}</td>
                <td>
                    <DisableWhiteListCoin coin={coin} disabled={coin.disabled}/>
                    <span className={"pull-right"}>
                    <BuyWhiteListCoin coin={coin} isAlreadyActive={isAlreadyActive}/>
                    <DeleteWhiteListCoin coin={coin}/>
                    </span>
                </td>
            </tr>);
        });

        return toRender;
    };

}
