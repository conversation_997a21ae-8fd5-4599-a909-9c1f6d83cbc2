import {PageHeaderWithLinks} from "@/components/common/PageHeaderWithLinks";
import {AutoBotWhiteList} from "@/components/pages/autobot/whitelist/AutoBotWhiteList";
import * as React from 'react';
import {Col, Row} from "react-bootstrap";

export class AutoBotWhiteListPage extends React.Component<any> {

    public render() {
        const {data} = this.props;

        return <div className="container-fluid">
            <PageHeaderWithLinks links={[
                {
                    title: 'Overview',
                    url: '/autobot',
                }, {
                    title: 'Whitelist',
                    url: '/autobot_whitelist',
                    active: true
                },
                {
                    title: 'Strategy View',
                    url: '/autobot_strategy',
                },
                {
                    title: 'Suggests',
                    url: '/autobot_suggest',
                }, {
                    title: 'Active Symbols',
                    url: '/autobot_assets',
                }, {
                    title: 'Trades',
                    url: '/autobot_trades',
                }, {
                    title: 'Settings',
                    url: '/autobot_settings',
                }]}/>

            <Row>
                <Col lg={12} md={12}>
                    <AutoBotWhiteList data={data}/>
                </Col>
            </Row>
        </div>
    }
}
