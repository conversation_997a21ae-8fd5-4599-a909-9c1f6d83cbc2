import {MPanel} from "@/components/common/MPanel";
import gql from "graphql-tag";
import {observable} from "mobx";
import {observer} from "mobx-react";
import {path, prop, sortBy} from "ramda";
import * as React from "react";
import {graphql} from "react-apollo";
import {<PERSON><PERSON>} from "react-bootstrap";
import {Crosshair, HorizontalGridLines, LineSeries, makeWidthFlexible, XAxis, XYPlot, YAxis,} from "react-vis";

export const DashboardSaldoQuery = gql`query DashboardSaldoQuery ($limit: Int) {
    user {
        saldo(limit: $limit) {
            saldos {
                usd
                btc
                timestamp
            }
        }
    }
}`

const FlexibleXYPlot = makeWidthFlexible(XYPlot);

// @ts-ignore
@graphql(DashboardSaldoQuery, {
    options: {
        pollInterval: 30000,
        variables: {
            limit: 31,
            symbol: "BTCUSDT"
        },
    },
})
@observer
export class DashboardBalanceUsd extends React.Component<any> {

    @observable public crosshairValues = [];
    @observable public showBTC = false;

    public getChartData() {
        const saldos: any = path(["user", "saldo", "saldos"])(this.props.data) || [];
        return sortBy(prop("timestamp"))(saldos).map((saldo: any, _idx) => {
            // const d = new Date(Number(saldo.timestamp));
            return {
                x: Number(saldo.timestamp),
                y: this.showBTC ? Number(saldo.btc) : Number(saldo.usd),
            }
        });
    }

    public setBTC = () => {
        this.showBTC = !this.showBTC;
    };

    public render() {
        if (this.props.data.loading) {
            return null;
        }

        return (<MPanel filled loading={this.props.data.loading}>
            <div className="panel-heading">
                Balance {this.showBTC ? 'BTC' : 'USD'} (30 trades)

                <Button className={"pull-right btn-xs badge " + (this.showBTC ? 'badge-success' : '')}
                        onClick={this.setBTC}>
                    <small>BTC</small>
                </Button>
            </div>

            <div className="panel-body">
                <div className="row">
                    <div className="col-xs-12 col-lg-12">
                        <FlexibleXYPlot
                            onMouseLeave={this.onMouseLeave}
                            animation
                            margin={{bottom: 10, left: 45}}
                            height={120}>
                            <XAxis tickLabelAngle={-60}
                                   hideTicks
                                   tickFormat={_v => {
                                       return "";

                                   }}/>
                            <YAxis title={this.showBTC ? 'BTC' : 'USD'}/>
                            <HorizontalGridLines style={{stroke: 'rgba(173, 173, 173, 0.37)'}}/>

                            <LineSeries data={this.getChartData()}
                                        onNearestX={this.onNearestBarX}
                                        fill={"rgba(246,168,33,0.05)"}
                                        curve="curveMonotoneX" color={"#4fb0cd"}/>
                            <Crosshair values={this.crosshairValues}
                                       titleFormat={this.formatCrosshairTitle}
                                       itemsFormat={this.formatCrosshairItems}
                            />

                        </FlexibleXYPlot>
                    </div>
                </div>
            </div>
        </MPanel>);
    }

    public onNearestBarX = (_value, {index}) => {
        const profits: any = this.getChartData();
        this.crosshairValues = [profits[index]];
    };

    public formatCrosshairTitle = values => ({
        title: 'Date',
        value: new Date(values[0].x).toLocaleString()
    });

    public formatCrosshairItems = values => {
        return values.map((_v, _i) => ({
            title: this.showBTC ? "BTC" : "USD",
            value: this.showBTC ? Number(values[0].y).toFixed(8) : Number(values[0].y).toFixed(0)
        }));
    };

    public onMouseLeave = () => {
        this.crosshairValues = [];
    };
}
