import {MPanel} from "@/components/common/MPanel";
import gql from "graphql-tag";
import {observable} from "mobx";
import {observer} from "mobx-react";
import {path, prop, sortBy} from "ramda";
import * as React from "react";
import {graphql} from "react-apollo";
import {But<PERSON>} from "react-bootstrap";
import {Crosshair, HorizontalGridLines, LineSeries, makeWidthFlexible, XAxis, XYPlot, YAxis} from "react-vis";

export const DashboardAssetWorthQuery = gql`query DashboardAssetWorthQuery ($days: Int) {
    user {
        saldoUsd(days: $days) {
            saldos {
                usd
                btc
                timestamp
            }
        }
    }
}`;

const FlexibleXYPlot = makeWidthFlexible(XYPlot);

// @ts-ignore
@graphql(DashboardAssetWorthQuery, {
    options: {
        pollInterval: 30000,
        variables: {
            days: 31
        },
    },
})
@observer
export class DashboardBalance30d extends React.Component<any> {

    @observable public crosshairValues = [];
    @observable public showBTC = false;

    public getChartData() {
        const saldos: any = path(["user", "saldoUsd", "saldos"])(this.props.data) || [];
        return sortBy(prop("timestamp"))(saldos).map((saldo: any, _idx) => {
            // const d = new Date(Number(saldo.timestamp));
            return {
                x: Number(saldo.timestamp),
                y: this.showBTC ? saldo.btc : saldo.usd,
            }
        });
    }

    public setBTC = () => {
        this.showBTC = !this.showBTC;
    };

    public render() {
        if (this.props.data.loading) {
            return null;
        }

        return (<MPanel filled loading={this.props.data.loading}>
            <div className="panel-heading">
                Balance ({this.showBTC ? 'BTC' : 'USD'}) (30D)

                <Button className={"pull-right btn-xs badge " + (this.showBTC ? 'badge-success' : '')}
                        onClick={this.setBTC}>
                    <small>BTC</small>
                </Button>
            </div>

            <div className="panel-body">
                <div className="row">
                    <div className="col-xs-12 col-lg-12">
                        <FlexibleXYPlot
                            animation
                            onMouseLeave={this.onMouseLeave}
                            margin={{bottom: 10, left: 45}}
                            height={120}>
                            <XAxis hideTicks/>
                            <YAxis title={this.showBTC ? 'BTC' : 'USD'}/>
                            <HorizontalGridLines style={{stroke: 'rgba(173, 173, 173, 0.37)'}}/>

                            <LineSeries data={this.getChartData()}
                                        fill={"rgba(246,168,33,0.05)"}
                                        onNearestX={this.onNearestBarX}
                                        curve="curveMonotoneX" color={"#f6a821"}/>
                            <Crosshair values={this.crosshairValues}
                                       titleFormat={this.formatCrosshairTitle}
                                       itemsFormat={this.formatCrosshairItems}/>
                        </FlexibleXYPlot>
                    </div>
                </div>
            </div>
        </MPanel>);
    }


    public onNearestBarX = (_value, {index}) => {
        const profits: any = this.getChartData();
        this.crosshairValues = [profits[index]];
    };

    public formatCrosshairTitle = values => ({
        title: 'Date',
        value: new Date(values[0].x).toLocaleDateString()
    });

    public formatCrosshairItems = values => {
        return values.map((_v, _i) => ({
            title: this.showBTC ? "BTC" : "USD",
            value: this.showBTC ? Number(values[0].y).toFixed(8) : Number(values[0].y).toFixed(0)
        }));
    };

    public onMouseLeave = () => {
        this.crosshairValues = [];
    };
}
