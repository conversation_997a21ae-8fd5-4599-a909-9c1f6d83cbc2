import {MPanel} from "@/components/common/MPanel";
import gql from "graphql-tag";
import {observable} from "mobx";
import {observer} from "mobx-react";
import * as React from "react";
import {with<PERSON><PERSON><PERSON>} from "react-apollo";
import {<PERSON><PERSON>} from "react-bootstrap";
import {
    Crosshair,
    HorizontalGridLines,
    LineSeries,
    makeWidthFlexible,
    MarkSeries,
    XAxis,
    XYPlot,
    YAxis,
} from "react-vis";
import {last} from "ramda";

export const DashboardBTCMacdQuery = gql`query DashboardBTCMacdQuery ($symbol: String!, $interval: String) {
    indicators {
        macds(symbol: $symbol, interval: $interval) {
            signal
            openTime
            MACD
            histogram
        }
    }
}`;

const FlexibleXYPlot = makeWidthFlexible(XYPlot);

// @ts-ignore
@withApollo
@observer
export class DashboardAOIndicator extends React.Component<any> {

    @observable public crosshairValues = [];
    @observable public interval = "1d";
    @observable public data = []

    public timer = null;

    public fetch = async () => {
        const result = await this.props.client.query({
            query: DashboardBTCMacdQuery,
            variables: {
                symbol: "BTCUSDT",
                interval: this.interval
            },
            fetchPolicy: 'network-only'
        })

        this.data = result.data.indicators.macds
    }

    public componentDidMount() {
        this.interval = localStorage.getItem('dashboard.ao.interval');

        this.fetch()

        this.timer = setInterval(async () => {
            await this.fetch()
        }, 30000)
    }

    public render() {
        const chartData = this.getChartData();

        let lastSignal = 0;

        if (chartData.length > 0) {
            lastSignal = (last(chartData) as any).y;
        }

        const toIntervalClass =interval => {
            return this.interval == interval ? "badge-success" : ""
        }

        return (<MPanel filled className="dashboard-btc-ao">
            <div className="panel-heading">
                AO BTC

                <span className="pull-right">
                    {lastSignal >= 0
                        ? <i className="fa fa-check text-success"/>
                        : <i className="fa fa-exclamation-triangle text-danger"/>
                    }
                </span>

                <span className="pull-right m-r-xs">
                    {this.getLastAction() === "buy"
                        ? <i className="fa fa-arrow-up text-success"/>
                        : <i className="fa fa-arrow-down text-danger"/>
                    }
                </span>

                <Button className={"pull-right m-r-xs btn-xs badge " + toIntervalClass("1d")}
                        onClick={() => this.setChart('1d')}>
                    <small>1d</small>
                </Button>

                <Button className={"pull-right m-r-xs btn-xs badge " + toIntervalClass("240")}
                        onClick={() => this.setChart('240')}>
                    <small>4h</small>
                </Button>

                <Button className={"pull-right m-r-xs btn-xs badge " + toIntervalClass("120")}
                        onClick={() => this.setChart('120')}>
                    <small>2h</small>
                </Button>

                <Button className={"pull-right m-r-xs btn-xs badge " + toIntervalClass("60")}
                        onClick={() => this.setChart('60')}>
                    <small>1h</small>
                </Button>

                <Button className={"pull-right m-r-xs btn-xs badge " + toIntervalClass("15")}
                        onClick={() => this.setChart('15')}>
                    <small>15m</small>
                </Button>
            </div>

            <div className="panel-body">
                <div className="row">
                    <div className="col-xs-12 col-lg-12">
                        <FlexibleXYPlot
                            onMouseLeave={this.onMouseLeave}
                            animation
                            margin={{bottom: 10, left: 45}}
                            height={120}>
                            <XAxis on0={true} hideTicks/>
                            <YAxis title={'AO'}/>

                            <HorizontalGridLines style={{stroke: 'rgba(173, 173, 173, 0.37)'}}/>

                            <Crosshair values={this.crosshairValues}
                                       titleFormat={this.formatCrosshairTitle}
                                       itemsFormat={this.formatCrosshairItems}/>

                            <LineSeries data={chartData}
                                        onNearestX={this.onNearestBarX}
                                        colorType="literal"
                                        getColor={d => {
                                            return d.y < 0 ? '#a54242' : '#479f47';
                                        }}
                                        curve="curveMonotoneX"
                                        color={"#ccc"}/>
                            <MarkSeries
                                sizeRange={[1, 3]}
                                colorType="literal"
                                getColor={({action}) =>
                                    action === "buy" ? '#479f47' : '#a54242'
                                }
                                data={this.getMarkValues()}
                            >
                            </MarkSeries>
                            <MarkSeries
                                sizeRange={[1, 3]}
                                colorType="literal"
                                stroke="#000000cc"
                                getColor={({action}) =>
                                    action === "buy" ? '#479f47' : '#a54242'
                                }
                                data={this.getCrossValues()}
                            />
                        </FlexibleXYPlot>
                    </div>
                </div>
            </div>
        </MPanel>);
    }

    private getChartData() {
        const macds: any = this.data || [];
        return macds.map(macd => {
            return {
                x: Number(macd.openTime),
                y: macd.signal,
            }
        });
    }

    private getMarkValues() {
        const markValues = []
        const macds: any = this.data || [];
        for (let i = 0; i < macds.length; i++) {
            if (i > 1) {
                const first = macds[i - 2].signal
                const second = macds[i - 1].signal
                const third = macds[i].signal

                // Sell
                if (second > first && third < second) {
                    markValues.push({
                        x: Number(macds[i].openTime),
                        y: third,
                        action: "sell",
                        size: 2
                    })
                }

                // Buy
                if (second < first && third > second) {
                    markValues.push({
                        x: Number(macds[i].openTime),
                        y: third,
                        action: "buy",
                        size: 2
                    })
                }
            }
        }
        return markValues
    }

    private getCrossValues = () => {
        const markValues = []
        const macds: any = this.data || [];
        for (let i = 0; i < macds.length; i++) {
            if (i > 1) {
                const second = macds[i - 1].signal
                const third = macds[i].signal

                // Sell
                if (second > 0 && third < 0) {
                    markValues.push({
                        x: Number(macds[i].openTime),
                        y: third,
                        action: "sell",
                        size: 2
                    })
                }

                // Buy
                if (second < 0 && third > 0) {
                    markValues.push({
                        x: Number(macds[i].openTime),
                        y: third,
                        action: "buy",
                        size: 2
                    })
                }
            }
        }
        return markValues
    }

    private getLastAction() {
        let lastAction = ""
        const macds: any = this.data || [];
        for (let i = 0; i < macds.length; i++) {
            if (i > 1) {
                const first = macds[i - 2].signal
                const second = macds[i - 1].signal
                const third = macds[i].signal

                // Sell
                if (second > first && third < second) {
                    lastAction = "sell"
                }

                // Buy
                if (second < first && third > second) {
                    lastAction = "buy"
                }
            }
        }

        return lastAction
    }

    private onNearestBarX = (_value, {index}) => {
        const profits: any = this.getChartData();
        this.crosshairValues = [profits[index]];
    };

    private formatCrosshairTitle = values => ({
        title: "Date",
        value: this.interval === "240" || this.interval === "120" ? new Date(values[0].x).toLocaleString() : new Date(values[0].x).toLocaleDateString()
    });

    private formatCrosshairItems = values => {
        return values.map((_v, _i) => ({
            title: "AO",
            value: Number(values[0].y).toFixed(0)
        }));
    };

    private onMouseLeave = () => {
        this.crosshairValues = [];
    };

    private setChart = async interval => {
        localStorage.setItem('dashboard.ao.interval', interval);
        this.interval = interval

        clearInterval(this.interval)
        await this.fetch()

        this.timer = setInterval(async () => {
            await this.fetch()
        }, 30000)
    };
}
