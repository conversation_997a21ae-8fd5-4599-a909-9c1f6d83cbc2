import {MPanel} from "@/components/common/MPanel";
import gql from "graphql-tag";
import {observable} from "mobx";
import {observer} from "mobx-react";
import {path} from "ramda";
import * as React from "react";
import {graphql} from "react-apollo";
import {Crosshair, HorizontalGridLines, makeWidthFlexible, VerticalBarSeries, XAxis, XYPlot, YAxis} from "react-vis";

export const DashboardAutobotIncomeQuery = gql`query DashboardAutobotIncomeQuery {
    bot {
        profitGraph {
            percentHistory
            usdHistory
            timestamp
        }
    }
}`;

const FlexibleXYPlot = makeWidthFlexible(XYPlot);

// @ts-ignore
@graphql(DashboardAutobotIncomeQuery, {
    options: {
        pollInterval: 35000
    }
})
@observer
export class DashboardAutobotIncome extends React.Component<any> {

    @observable public crosshairValues = [];

    public getChartData() {
        const profitGraphData: any = path(['data', 'bot', 'profitGraph'])(this.props) || [];
        const map = profitGraphData.map(item => {
            return {
                x: new Date(item.timestamp).getTime(),
                y: item.usdHistory
            }
        });

        return map;
    }

    public render() {
        if (this.props.data.loading) {
            return null;
        }

        const chartData = this.getChartData();
        const sum = chartData.reduce((prev, cur) => prev + cur.y, 0);
        const sumRender = <h6 style={{marginTop: '6px'}}>
            <span className={sum > 0 ? 'text-success' : 'text-danger'}> {sum >= 0
                ? '+'
                : ''}{sum.toFixed(0)} USD</span></h6>;

        return (<MPanel filled loading={this.props.data.loading}>
            <div className="panel-heading">
                Autobot Income 30D (USD)

                <span className="pull-right">{sumRender}</span>
            </div>

            <div className="panel-body">
                <div className="row">
                    <div className="col-xs-12 col-lg-12">
                        <FlexibleXYPlot
                            onMouseLeave={this.onMouseLeave}
                            animation
                            margin={{bottom: 10, left: 45}}
                            xType="time"
                            height={120}>
                            <XAxis hideTicks/>
                            <YAxis title={'USD'}/>
                            <HorizontalGridLines style={{stroke: 'rgba(173, 173, 173, 0.37)'}}/>

                            <VerticalBarSeries data={chartData}
                                               onNearestX={this.onNearestBarX}
                                               colorType="literal"
                                               getColor={d => {
                                                   return d.y < 0 ? '#a54242' : '#479f47';
                                               }}
                                               color={"#f6a821"}/>
                            <Crosshair values={this.crosshairValues}
                                       titleFormat={this.formatCrosshairTitle}
                                       itemsFormat={this.formatCrosshairItems}/>
                        </FlexibleXYPlot>

                    </div>
                </div>
            </div>
        </MPanel>);
    }

    private onNearestBarX = (_value, {index}) => {
        const profits: any = this.getChartData();
        this.crosshairValues = [profits[index]];
    };

    private formatCrosshairTitle = values => {
        return {
            title: 'Date',
            value: new Date(values[0].x).toLocaleDateString()
        };
    };

    private formatCrosshairItems = values => {
        return values.map((_v, _i) => ({
            title: "USD",
            value: Number(values[0].y).toFixed(2)
        }));
    };

    private onMouseLeave = () => {
        this.crosshairValues = [];
    };
}
