import {iconList} from "@/components/pages/dashboard/IconList";
import {contains} from 'ramda';
import * as React from 'react';
import {Link} from 'react-router-dom';

export class DashboardItemTR extends React.Component<any> {
    public render() {

        const {balance, ao, openOrdersTP, openOrdersSL, lastTrade, lastPrice, url} = this.props;
        const assetClass = balance.asset === 'BNB' ? "text-primary" : null;

        const icon = contains(balance.asset.toLowerCase(), iconList)
            ? '/assets/img/' + balance.asset.toLowerCase() + '.png'
            : '/assets/img/gen.png';

        const buyTimeRender = lastTrade.transactTime ? new Date(lastTrade.transactTime).toLocaleString() : null
        const balanceRender = (balance.locked + balance.free).toFixed(8)
        const lastTradeRender =  lastTrade ? Number(lastTrade.price).toFixed(8) : '-'

        return (<tr key={balance.asset}>
            <td>
                {url
                    ? <Link to={url}>
                        <i className="pe page-header-icon">
                            <img src={icon}
                                 height={18}
                                 style={{marginRight: "10px"}}
                                 width={18}/></i>
                        <b>
                            <span className={assetClass}>{balance.asset}</span>
                        </b>
                    </Link>
                    : <a>
                        <i className="pe page-header-icon">
                            <img src={icon}
                                 height={18}
                                 style={{marginRight: "10px"}}
                                 width={18}/></i>
                        <b>
                            <span className={assetClass}>{balance.asset}</span>
                        </b>
                    </a>}
            </td>
            <td>
                {this.renderUsd(balance)}
            </td>

            <td>
                {this.renderAO(ao)}
            </td>

            <td>
                {this.renderCurrent(balance, lastTrade, lastPrice)}
            </td>
            <td>
                <div style={{
                    textOverflow: "ellipsis",
                    whiteSpace: "nowrap",
                    overflow: "hidden",
                    maxWidth: "120px",
                    width: "120px"
                }} className="text-nowrap">{buyTimeRender}</div>
            </td>
            {/*<td className="hidden-xs">*/}
            {/*    {this.renderLastTrade(balance, lastTrade)}*/}

            {/*    <span className="panel-c-white small open-orders-text-tr-tp">*/}
            {/*    {openOrdersTP && <span className="open-orders-icon" title="Open orders"/>}*/}
            {/*        {openOrdersTP && this.renderOpenOrderType(openOrdersTP.type)}*/}
            {/*    </span>*/}
            {/*    <span className="panel-c-white small open-orders-text-tr-sl">*/}
            {/*    {openOrdersSL && <span className="open-orders-icon" title="Open orders"/>}*/}
            {/*        {openOrdersSL && this.renderOpenOrderType(openOrdersSL.type)}*/}
            {/*    </span>*/}
            {/*</td>*/}
            {/*{this.render24H(priceChangePercent)}*/}

        </tr>);
    }

    public renderOpenOrderType(type) {
        switch (type) {
            case 'BOT_TAKE_PROFIT':
                return 'TP';
            case 'BOT_STOP_LOSS':
                return 'SL';
            case 'BOT_TP_SL':
                return 'TPSL';
            case 'AUTO_STOP_LOSS':
                return 'ASL'
            case 'AUTO_TAKE_PROFIT':
                return 'ATP'
            default:
                return type;
        }
    }

    public renderAO(ao) {
        // const val = Number(ao)
        //     .toFixed(6);
        let className = '';
        // let text = '';

        if (ao > 0) {
            className = 'fa fa-check text-success';
            // text = "";
        } else if (ao < 0) {
            className = 'text-danger';
            // text = "";
        } else if (ao === 0) {
            className = 'text-muted';
            // text = "";
        }

        return <span className={className}/>;
    }

    private renderUsd(balance) {
        return balance.usdValue.toFixed(2)
    }

    private renderCurrent(_balance, lastTrade: any, lastPrice: any) {
        return <div>
            {/*<span className="m-r-sm">*/}
            {/*    {lastTrade*/}
            {/*        ? this.renderCurrentPrice(lastPrice, lastTrade.price)*/}
            {/*        : this.renderCurrentPrice(lastPrice, lastPrice)}*/}
            {/*</span>*/}
            <span>{lastTrade && lastTrade.price !== 0
                ? this.toPercents(((lastPrice / lastTrade.price) - 1) * 100)
                : null}
            </span>
        </div>
    }

    private render24H(priceChangePercent) {
        return (
            <tr>
                <td>
                    <div className="c-white">
                        <h6>Price 24h</h6>
                    </div>
                </td>

                <td colSpan={2} className="text-right">
                    <div className="c-white">
                        <h6><span>
              {this.toPercents(priceChangePercent)}
            </span></h6>
                    </div>
                </td>
            </tr>);
    }

    public toPercents(value) {
        const percent = Number(value)
            .toFixed(2);
        let className = '';
        let text = '';

        if (value > 0) {
            className = 'text-success';
            text = `+${percent}%`;
        } else if (value < 0) {
            className = 'text-danger';
            text = `${percent}%`;
        } else if (value === 0) {
            className = 'text-muted';
            text = `${percent}%`;
        }

        return <span className={className}>{text}</span>;
    }

    private renderCurrentPrice(lastPrice, buyPrice) {
        const price = Number(lastPrice)
            .toFixed(8);
        let className = '';

        if (buyPrice !== null) {
            if (buyPrice < lastPrice) {
                className = 'text-success';
            } else if (buyPrice > lastPrice) {
                className = 'text-danger';
            } else if (lastPrice === buyPrice) {
                className = 'text-muted';
            }

            return <span className={className}>{price}</span>;
        }

        return null;
    }
}
