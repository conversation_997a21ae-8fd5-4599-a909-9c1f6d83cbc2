import {observable} from "mobx";
import {inject, observer} from "mobx-react";
import {find, path, prop, propEq, reverse, sortBy} from "ramda";
import * as React from "react";
import {graphql, withA<PERSON>lo} from "react-apollo";
import {Link} from "react-router-dom";

import {SaveAsFavoriteButton} from "@/components/pages/dashboard/SaveAsFavoriteButton";
import {AllTickerVolumes} from "@/graphql/binance/ws/AllTickerVolumes.graphql";
import * as localforage from "localforage";
import {MPanel} from "@/components/common/MPanel";
import {NoticesCoins} from "@/graphql/noticedCoins/NoticedCoins.graphql";

const STORAGE_KEY_COL = "dashboard-sorting-col";
const STORAGE_KEY_DIR = "dashboard-sorting-dir";

// @ts-ignore
@withApollo
@graphql(NoticesCoins)
@inject('exchangeStore')
@observer
export class DashboardVolume extends React.Component<any, any> {

    @observable public sortCol = "percentVolume4h";
    @observable public sortDir = false;
    @observable public searchCoin = "";
    @observable public selectedRow = {
        symbol: null,
    };

    private subscriptionObserver = null;

    public componentWillMount() {
        const self = this;
        const {exchangeStore} = this.props;

        this.subscriptionObserver = this.props.client.subscribe({
            query: AllTickerVolumes,
            variables: {
                base: "USDT",
            },
        })
            .subscribe({
                next(result) {
                    if (result.data.allTickers) {
                        exchangeStore.topVolumes = sortBy(prop(self.sortCol))(result.data.allTickers);

                        if (self.sortDir) {
                            exchangeStore.topVolumes  = reverse(exchangeStore.topVolumes.slice());
                        }
                    }
                },
                error(_err) {
                    console.log(_err);
                },
            });
    }

    public async componentDidMount() {
        try {
            const col: any = await localforage.getItem(STORAGE_KEY_COL);
            const dir: any = await localforage.getItem(STORAGE_KEY_DIR);
            if (col) {
                this.sortCol = col;
                this.sortDir = dir;
            }
        } catch (e) {
            console.log(e);
        }
    }

    public componentWillUnmount() {
        if (this.subscriptionObserver) {
            this.subscriptionObserver.unsubscribe();
        }
    }

    public onSearch = e => {
        this.searchCoin = e.target.value;
    }

    public setSort = (sortCol: string) => {
        const {exchangeStore} = this.props;

        if (this.sortCol === sortCol) {
            this.sortDir = !this.sortDir;
        }

        this.sortCol = sortCol;
        exchangeStore.topVolumes = sortBy(prop(sortCol))(exchangeStore.topVolumes);

        if (this.sortDir) {
            exchangeStore.topVolumes = reverse(exchangeStore.topVolumes);
        }

        localforage.setItem(STORAGE_KEY_COL, sortCol);
        localforage.setItem(STORAGE_KEY_DIR, this.sortDir);
    };

    public selectRow = (vol: any) => {
        if (this.selectedRow.symbol === vol.symbol) {
            this.selectedRow = {
                symbol: null
            };
        } else {
            this.selectedRow = vol;
        }
    };

    public render() {
        const {sortCol, searchCoin, selectedRow} = this;
        const {topVolumes} = this.props.exchangeStore;
        let volumes = [...topVolumes];

        if (searchCoin) {
            volumes = topVolumes.filter(v => v.symbol.toLowerCase()
                .indexOf(searchCoin.toLocaleLowerCase()) !== -1);
        }

        return (<MPanel filled>
            <div className="panel-heading">
                Top Volume

                <span className={"btn btn-xs btn-default pull-right " +
                (this.sortCol === "advice" ? "text-accent" : "")}
                      onClick={() => this.setSort("advice")}>
          <i className="fa fa-angle-up"/>
        </span>

                <SaveAsFavoriteButton volumes={volumes}
                                      searchCoin={searchCoin}
                                      onSearch={this.onSearch}
                                      selectedRow={selectedRow}/>

            </div>

            <div className="panel-body">
                <table className="table table-condensed small">
                    <thead>
                    <tr>
                        <th/>
                        <th className={sortCol === "symbol" ? "text-info" : null}
                            onClick={() => this.setSort("symbol")}>
                            Symbol
                        </th>
                        <th className={`text-right ${sortCol === "volumeQuoteBTC" ? "text-info" : null}`}
                            onClick={() => this.setSort("volumeQuoteBTC")}>
                            Vol24h
                        </th>
                        <th className={`text-right ${sortCol === "percentVolume4h" ? "text-info" : null}`}
                            onClick={() => this.setSort("percentVolume4h")}>
                            Vol4h
                        </th>
                        <th className={`text-right ${sortCol === "priceChangePercent" ? "text-info" : null}`}
                            onClick={() => this.setSort("priceChangePercent")}>
                            P24h
                        </th>
                        <th className={`text-right ${sortCol === "percent4h" ? "text-info" : null}`}
                            onClick={() => this.setSort("percent4h")}>
                            P4h
                        </th>

                    </tr>
                    </thead>

                    <tbody>
                    {volumes.slice(0, 25).map((vol, idx) => this.renderElement(vol, idx))}
                    </tbody>
                    <tfoot/>
                </table>
            </div>
        </MPanel>);
    }

    public renderElement(vol, idx) {
        const {advice, symbol, firstTradeId, volumeQuoteBTC, percent4h, percentVolume4h, priceChangePercent} = vol;
        const favorites: any = path(["user", "noticedCoins"])(this.props.data) || [];
        let className = idx < 10 ? "text-accent" : "text-muted";

        if (firstTradeId === 0) {
            className = "text-info";
        }

        let volClass = "";
        if (percent4h > 3) {
            volClass = "text-accent";
        } else if (percent4h > 0) {
            volClass = "text-success";
        } else if (percent4h < 0) {
            volClass = "text-danger";
        }

        const showBasket = find(propEq("asset", symbol.replace("USDT", "")))(this.props.balances) ?
            <i className="fa fa-shopping-cart basket-icon m-r-xs"/> : null;

        const rocketIcon = !showBasket && volumeQuoteBTC > 1000 && percentVolume4h > 15 &&
        percent4h > 0
            ? <i className="fa fa-rocket rocket-icon m-r-xs"/> : null;

        const fav = favorites.find(propEq("symbol", symbol));
        const favoriteIcon = fav
            ? <i className={"fa fa-star vol-fav-icon m-r-xs " + (fav.isActiveCall ? " text-accent " : "vol-fav-icon-normal")}/>
            : null;

        const adviceIcon = advice === "long"
            ? <i className="fa fa-angle-up advice-icon m-r-xs"/>
            : null;

        return (<tr key={symbol}
                    onClick={() => this.selectRow(vol)}
                    className={this.selectedRow.symbol === symbol ? "success" : null}>
            <td style={{minWidth: "12px", width: "12px"}}>
                {showBasket}
                {rocketIcon}
                {favoriteIcon}
                {adviceIcon}
            </td>
            <td>
                <Link to={`/coin/${symbol}`}
                      className={className}>{symbol}</Link></td>
            <td className="text-right">{volumeQuoteBTC ? volumeQuoteBTC.toFixed(0) : 0} BTC</td>
            <td className={`text-right ${percentVolume4h > 0 ? "text-success" : "text-danger"}`}>
                {percentVolume4h}</td>
            <td className={`text-right ${priceChangePercent > 0 ? "text-success" : "text-danger"}`}>
                {priceChangePercent.toFixed(1)}</td>
            <td className={`text-right ${volClass}`}>
                {percent4h}</td>

        </tr>);
    }
}
