// @ts-ignore
import {FavoriteCoins} from "@/graphql/binance/FavoriteCoins.graphql";
import {SaveFavoriteCoin} from "@/graphql/binance/FavoriteMutation.graphql";
import {graphql} from "react-apollo";
import * as React from "react";
import {success} from "@/utils/Toast";

// @ts-ignore
@graphql(SaveFavoriteCoin)
export class SaveAsFavoriteButton extends React.Component<any> {
    public saveAsFavorite = async () => {

        if (this.props.selectedRow && this.props.selectedRow.symbol) {
            const result = await this.props.mutate({
                variables: {
                    symbol: this.props.selectedRow.symbol,
                },
                refetchQueries: [
                    {
                        query: FavoriteCoins,
                    },
                ],
            });

            if (result.data.saveFavoriteCoin) {
                success("Selected symbol saved as favorite");
            }
        }
    }

    public render() {
        return (<span className="trade-signal-search trade-signal-search-market pull-right">
        <input type="text"
               placeholder="Search..."
               className="input-sm form-control m-r-xs"
               value={this.props.searchCoin}
               name="searchCoin"
               maxLength={10}
               onChange={this.props.onSearch}/>

        {/*<div className="btn btn-xs btn-default m-r-xs"*/}
             {/*disabled={!this.props.selectedRow.symbol}*/}
             {/*onClick={this.saveAsFavorite}>*/}
        {/*<i className="fa fa-star-o"/></div>*/}
            </span>);
    }
}

