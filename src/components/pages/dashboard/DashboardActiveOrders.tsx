import {observer} from "mobx-react";
import * as React from "react";
import {graphql, Mutation} from "react-apollo";

import {MPanel} from "@/components/common/MPanel";
import gql from "graphql-tag";
import {path} from "ramda";
import {Panel, Table} from "react-bootstrap";
import {Link} from "react-router-dom";
import {error, success} from "@/utils/Toast";

const BotOrders = gql`
    query {
        bot {
            botOrders {
                _id
                orderId
                symbol
                type
                origQty
                price
                time
                executedQty
                status
            }
        }
    }
`;

const CancelBotOrder = gql`
    mutation($symbol: String!, $orderId: String!, $type: String!) {
        cancelOrderMutation(orderId: $orderId, symbol: $symbol, type: $type) {
            ok
        }
    }
`;

// @ts-ignore
@graphql(BotOrders)
@observer
export class DashboardActiveOrders extends React.Component<any, any> {

    public cancelOrder = async (mutation, botOrder) => {
        const result = await mutation({
            variables: {
                orderId: botOrder.orderId,
                symbol: botOrder.symbol,
                type: botOrder.type
            },
            refetchQueries: [{query: BotOrders}]
        });

        if (result.data.cancelOrderMutation.ok) {
            success('Order ' + botOrder.orderId + ' canceled');
        } else {
            error(result.error);
        }
    };

    public render() {
        const botOrders: any = path(['bot', 'botOrders'])(this.props.data) || [];

        const renderBotOrder = (botOrder => {
            const isAutoSLOrder = botOrder.type === 'AUTO_STOP_LOSS'

            return <tr key={botOrder._id}>
                <td><Link to={'/coin/' + botOrder.symbol}
                          target={"_blank"}>
                    {botOrder.symbol}
                </Link></td>
                <td>{this.renderType(botOrder.type)}</td>
                <td>{botOrder.origQty}</td>
                <td>{botOrder.price}</td>
                <td>{botOrder.status}</td>
                <td>
                    <Mutation mutation={CancelBotOrder}>
                        {(mutation, {_data}) => {
                            return <button className="btn btn-xs btn-danger"
                                           onClick={() => this.cancelOrder(mutation, botOrder)}>
                                cancel
                            </button>
                        }}
                    </Mutation>
                </td>
            </tr>
        });

        return (<MPanel filled className="panel-c-primary">
            <Panel.Heading>
                Active orders
            </Panel.Heading>
            <Panel.Body>
                {botOrders.length === 0 ?
                    <div className="text-center">No active orders</div>
                    :
                    <Table className="table-condensed small table-no-bottom-margin">
                        <tbody>
                        {botOrders.map(renderBotOrder)}
                        </tbody>
                        <tfoot/>
                    </Table>
                }
            </Panel.Body>
        </MPanel>);
    }

    public renderType(type) {
        switch (type) {
            case 'BOT_TAKE_PROFIT':
                return 'TP';
            case 'BOT_STOP_LOSS':
                return 'SL';
            case 'BOT_TP_SL':
                return 'TPSL';
            case 'AUTO_STOP_LOSS':
                return 'ASL'
            case 'AUTO_TAKE_PROFIT':
                return 'ATP'
            default:
                return type;
        }
    }
}
