import * as React from 'react';
import {DashboardItems} from "@/components/pages/dashboard/DashboardItems";
import {Loading} from '@/components/common/Loading';

export class DashboardPage extends React.Component<any> {
    public state = {
        renderWrappedComponent: false,
    };

    public componentDidMount() {
        setTimeout(() => {
            this.setState({
                renderWrappedComponent: true,
            });
        }, 0);
    }

    public render() {
        const {renderWrappedComponent} = this.state;

        return renderWrappedComponent ?
            <DashboardItems {...this.props} /> :
            <Loading />;
    }
}
