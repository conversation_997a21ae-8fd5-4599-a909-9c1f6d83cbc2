import gql from "graphql-tag";
import {path} from 'ramda';
import * as React from 'react';
import {graphql} from 'react-apollo';

const DashboardTopBotProfitTodayQuery = gql`query DashboardTopBotProfitTodayQuery {
    bot {
        autobot {
            profitToday
            profitTodayUsd
            profitTodayRelative
        }
    }
}`

// @ts-ignore
@graphql(DashboardTopBotProfitTodayQuery, {
    options: {
        pollInterval: 30000,
    },
})
export class DashboardTopBotProfitToday extends React.Component<any, any> {

    public render() {
        const autobot: any = path(['data', 'bot', 'autobot'])(this.props) || {};
        const profit = autobot ? autobot.profitTodayUsd : null

        if (!autobot) {
            return <div className="col-lg-2 col-md-3 col-xs-6">
                <h5>Bot Profit Today</h5>
                <h5>
                    <div className="loader-spin"/>
                </h5>
            </div>
        }

        if (profit === null) {
            return null
        }

        const autobotProfitRender = autobot.profitTodayRelative ? (<small>
            <span className={autobot.profitTodayRelative > 0
                ? 'text-muted m-r-xs'
                : 'text-danger m-r-xs'}> {autobot.profitTodayRelative >= 0
                ? '+'
                : ''}{autobot.profitTodayRelative.toFixed(2)}%</span>
        </small>) : null;

        const colorClass = autobot.profitTodayUsd > 0
            ? 'text-success'
            : 'text-danger';

        return <div className="col-lg-2 col-md-3 col-xs-6">
            <h5>Bot Profit Today</h5>
            <h5>
                <span className={colorClass}>{autobot.profitTodayUsd
                    ? `$${autobot.profitTodayUsd.toFixed(2)}`
                    : null}</span>
                {autobotProfitRender}
            </h5>
        </div>;
    }
}
