import {MPanel} from "@/components/common/MPanel";
import gql from "graphql-tag";
import {observable} from "mobx";
import {observer} from "mobx-react";
import {path} from "ramda";
import * as React from 'react';
import {with<PERSON><PERSON><PERSON>} from "react-apollo";

const DashboardAltIndexQuery = gql`query DashboardAltIndexQuery {
    bot {
        fearAltIndex {
            fearIndex
            fearText
            altIndex
            altText
        }
    }
}`

// @ts-ignore
@withApollo
@observer
export class DashboardFearIndex extends React.Component<any, any> {

    @observable private fearIndex = 0
    @observable private altIndex = 0
    @observable private fearText = ""
    @observable private altText = ""

    private interval;

    public componentDidMount() {

        this.fetchData();

        this.interval = setInterval(async () => {
            await this.fetchData();
        }, 10000);
    }

    public componentWillUnmount() {
        clearInterval(this.interval);
    }

    public render() {
        const {fearIndex, fearText} = this
        let cssToAdd = "";
        let altcssToAdd = "";

        if (fearIndex <= 25) {
            cssToAdd = "text-danger";
        } else if (fearIndex > 25 && fearIndex < 40) {
            cssToAdd = "text-warning";
        } else if (fearIndex >= 55) {
            cssToAdd = "text-success";
        }

        if (fearIndex > 75) {
            altcssToAdd = "text-success";
        } else if (fearIndex > 25 && fearIndex < 75) {
            altcssToAdd = "text-warning";
        } else if (fearIndex < 25) {
            altcssToAdd = "text-danger";
        }

        return <MPanel filled>
            <div className="panel-body">
                <div className="row">
                    <div className="col-lg-6 col-md-6 col-xs-12 text-center">
                        <h5>Fear Index</h5>
                        <h5 style={{marginBottom: "3px"}}>
                            <span className="badge">
                                <span className={cssToAdd}
                                      style={{fontSize: 12, marginBottom: -14, marginTop: -8, fontWeight: 'bold'}}
                                      title={String(fearIndex + ' - ' + fearText)}>
                                    {fearIndex} - {fearText}
                                </span>
                            </span>
                        </h5>
                    </div>
                    <div className="col-lg-6 col-md-6 col-xs-12 text-center">
                        <h5>Alt Index</h5>
                        <h5 style={{marginBottom: "3px"}}>
                            <span className="badge">
                                <span className={altcssToAdd}
                                      style={{fontSize: 12, marginBottom: -14, marginTop: -8, fontWeight: 'bold'}}
                                      title={String(this.altIndex + ' - ' + this.altText)}>
                                    {this.altIndex} - {this.altText}
                                </span>
                            </span>
                        </h5>
                    </div>
                </div>
            </div>
        </MPanel>
    }

    private fetchData = async () => {
        const {client} = this.props;

        try {
            const altresult = await client.query({
                query: DashboardAltIndexQuery
            })

            const result: any = path(["bot", "fearAltIndex"])(altresult.data) || {}
            this.altIndex = result.altIndex
            this.altText = result.altText
            this.fearIndex = result.fearIndex
            this.fearText = result.fearText
        } catch (e) {
            console.error(e)
        }
    }
}
