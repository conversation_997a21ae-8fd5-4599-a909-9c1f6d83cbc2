import {MPanel} from "@/components/common/MPanel";
import * as React from 'react';
import {Ticker} from "react-ts-tradingview-widgets";


export class DashboardTradingViewBar extends React.Component<any, any> {

    public render() {
        return (<MPanel filled>
            <div className="panel-body">
                <div className="row text-center">
                    <div className="col-lg-12 col-md-12 col-xs-12">
                        <Ticker colorTheme="dark" isTransparent></Ticker>
                    </div>
                </div>
            </div>
        </MPanel>);
    }
}
