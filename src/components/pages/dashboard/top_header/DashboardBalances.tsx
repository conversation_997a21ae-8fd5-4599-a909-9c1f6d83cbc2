import * as React from 'react';
import {path, prop} from 'ramda';
import {compose, graphql} from 'react-apollo';
import PriceCalculation from "@/utils/PriceCalculation";
import {SaveSaldoMutation} from "@/graphql/stats/SaveSaldoMutation.graphql";
import {MPanel} from "@/components/common/MPanel";
import gql from "graphql-tag";
import {DashboardSaldoQuery} from "@/components/pages/dashboard/DashboardSaldo";
import {success} from "@/utils/Toast";

const DashboardBalanceQuery = gql`query DashboardBalanceQuery ($limit: Int, $symbol: String!, $interval: String!, $days: Int) {
    user {
        saldo(limit: $limit) {
            yesterday {
                usd
                btc
                timestamp
            }
        }
        saldoUsd(days: $days) {
            saldos {
                usdDiff
                timestamp
            }
        }
    }
    exchange {
        lastCandle(symbol: $symbol, interval: $interval) {
            close
        }
        currentPrice(symbol: "BTCUSDT") {
            price
        }
    }
}`

@compose(
    graphql(SaveSaldoMutation),
    graphql(DashboardBalanceQuery, {
        options: {
            pollInterval: 30000,
            variables: {
                limit: 1,
                days: 1,
                interval: '5',
                symbol: 'BTCUSDT'
            },
        },
    })
)
export default class DashboardBalances extends React.Component<any, any> {
    public saveSaldo = async () => {
        const result = await this.props.mutate({
            refetchQueries: [
                {
                    query: DashboardBalanceQuery,
                    variables: {
                        symbol: 'BTCUSDT',
                        interval: '5',
                        limit: 1,
                        days: 1
                    },
                },
                {
                    query: DashboardSaldoQuery,
                    variables: {
                        limit: 110,
                        symbol: "BTCUSDT"
                    },
                }
            ],
        });

        if (result.data.saveCurrentSaldo) {
            success('Account balance saved.');
        }
    };

    public render() {
        const {data, pdata, loadingDone, btcValue, usdValue} = this.props;
        const saldo: Partial<GQL.ISaldoResponse> = path(['user', 'saldo'])(data) || {};
        const saldoUsd: Partial<GQL.ISaldoResponse> = path(['user', 'saldoUsd', 'saldos'])(data) || [];

        const yesterday: any = saldo.yesterday || {};
        const coinPrice = path(['exchange', 'currentPrice', 'price'])(data);

        const yesterdayDiff = saldoUsd.length > 0 ? saldoUsd[0].usdDiff : 0;

        const lastBtc: any = prop('btc', yesterday) || 0;
        const lastUsd: any = prop('usd', yesterday) || 0;

        const usdPercent: any = PriceCalculation.toPercent(lastUsd, usdValue);
        const btcPercent: any = PriceCalculation.toPercent(lastBtc, btcValue);

        const usdDiffRender = yesterdayDiff ? (<span
            className={yesterdayDiff > 0 ? 'text-success m-r-xs' : 'text-danger m-r-xs'}> {yesterdayDiff >= 0
            ? '+'
            : ''}${yesterdayDiff}</span>) : null;
        const usdRender = usdValue ? (<span
            className={usdPercent > 0 ? 'text-success m-r-xs' : 'text-danger m-r-xs'}> {usdPercent >= 0
            ? '+'
            : ''}${(usdValue - lastUsd).toFixed(0)}</span>) : null;
        const btcRender = btcValue ? (<span
            className={btcPercent > 0 ? 'text-success m-r-xs' : 'text-danger m-r-xs'}> {btcPercent >= 0
            ? '+'
            : ''}{btcPercent}%</span>) : null;

        return (<MPanel filled loading={pdata.loading && !loadingDone}>
            <div className="panel-heading">
                Balances
            </div>
            <div className="panel-body">
                <table className="table table-condensed table-no-border">
                    <tbody>

                    <tr className="btcBalance">
                        <td><h6>BTC Price</h6></td>
                        <td className="text-right">
                            <h6><span className="text-muted">{coinPrice
                                ? '$' + Number(coinPrice).toFixed(2)
                                : null}</span></h6>
                        </td>
                    </tr>
                    <tr>
                        <td><h6>Balance BTC</h6></td>
                        <td className="text-right">
                            <h6>
                                <a className="icon-in-text m-r-xs">
                                    <i className="fa fa-download" onClick={this.saveSaldo}/>
                                </a>

                                {btcRender} <span className="text-accent">{btcValue
                                ? btcValue.toFixed(8)
                                : null}</span></h6>
                        </td>
                    </tr>

                    <tr>
                        <td><h6>Balance USDT</h6></td>
                        <td className="text-right"><h6><span>{usdRender} {usdValue
                            ? `$${usdValue.toFixed(2)}`
                            : null}</span></h6></td>
                    </tr>

                    <tr>
                        <td><h6>Income since yesterday USD</h6></td>
                        <td className="text-right"><h6><span>{usdDiffRender}</span></h6></td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </MPanel>);
    }
}
