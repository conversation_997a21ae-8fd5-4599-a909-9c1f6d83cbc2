import {MPanel} from "@/components/common/MPanel";
import {DashboardItem} from "@/components/pages/dashboard/DashboardItem";
// @ts-ignore
import localforage from "localforage";
import {observable} from "mobx";
import {observer} from "mobx-react";
import {filter, find, prop, propEq, reverse, sortBy} from "ramda";
import * as React from "react";
import {Panel, Table} from "react-bootstrap";

const STORAGE_KEY_COL = "dashboard-hold-sorting-col";
const STORAGE_KEY_DIR = "dashboard-hold-sorting-dir";

@observer
export class DashboardActivePortfolio extends React.Component<any, any> {

    @observable public sortCol = "asset";
    @observable public sortDir = false;

    public async componentDidMount() {
        const col: any = await localforage.getItem(STORAGE_KEY_COL);
        const dir: any = await localforage.getItem(STORAGE_KEY_DIR);
        if (col) {
            this.sortCol = col;
            this.sortDir = dir;
        }
    }

    public render() {
        const balances: any = this.props.balances;

        const balanceBTC = find(propEq("asset", "BTC"))(balances);
        const balanceUSDT = find(propEq("asset", "USDT"))(balances);
        const balanceBNB = find(propEq("asset", "BNB"))(balances);

        const toFilter = ["BTC", "USDT", "BNB"];
        const filteredItems = filter(x => toFilter.indexOf((x as any).asset) === -1, balances);

        let balancesWithoutBTC: any = filteredItems;

        switch (this.sortCol) {
            case "usdValue":
            case "asset":
                balancesWithoutBTC = sortBy(prop(this.sortCol))(filteredItems);
                break;
        }

        if (this.sortDir) {
            balancesWithoutBTC = reverse(balancesWithoutBTC)
        }

        return <MPanel filled>
            <Panel.Heading>
                Active portfolio
                <b><span className="pull-right">{balances.length}</span></b>
            </Panel.Heading>
            <Panel.Body>
                <Table className="table table-condensed small">
                    <thead>
                    <tr>
                        <th onClick={() => this.setSort("asset")}>Asset</th>
                        <th onClick={() => this.setSort("usdValue")}>USD</th>
                        <th className="">AO 1d</th>
                        <th className="">Profit</th>
                        <th className="">Last Trade</th>
                    </tr>
                    </thead>
                    <tbody>
                    {balanceBTC && <DashboardItem style={{border: "1px dotted #f0a12778"}}
                                                  asset={balanceBTC.asset}
                                                  balance={balanceBTC}/>}

                    {balanceUSDT && <DashboardItem asset={balanceUSDT.asset}
                                                   balance={balanceUSDT}/>}

                    {balanceBNB && <DashboardItem asset={balanceBNB.asset}
                                                  balance={balanceBNB}/>}

                    {balancesWithoutBTC.map(balance => (<DashboardItem
                        key={balance.idx}
                        asset={balance.asset}
                        balance={balance}/>))}
                    </tbody>
                </Table>
            </Panel.Body>
        </MPanel>
    }

    public setSort = async (sortCol: string) => {
        if (this.sortCol === sortCol) {
            this.sortDir = !this.sortDir;
        }

        this.sortCol = sortCol;

        await localforage.setItem(STORAGE_KEY_COL, sortCol);
        await localforage.setItem(STORAGE_KEY_DIR, this.sortDir);
    };
}