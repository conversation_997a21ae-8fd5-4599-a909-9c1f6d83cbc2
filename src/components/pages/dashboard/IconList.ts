
// Generate with ls -lAh | grep -v '^d' | awk '{print $9}' | sed -E "s/(.*)(\.png)/\"\1\",/"
// or find . -maxdepth 1 -not -type d | sort  | sed -E "s/\.\/(.*)(\.png)/\"\1\",/"
export const iconList = ["$pac", "0xbtc",
    "2give",
    "abt",
    "act",
    "actn",
    "ada",
    "add",
    "adx",
    "ae",
    "aeon",
    "aeur",
    "agi",
    "agrs",
    "aion",
    "algo",
    "amb",
    "amp",
    "ampl",
    "ant",
    "apex",
    "appc",
    "ardr",
    "arg",
    "ark",
    "arn",
    "arnx",
    "ary",
    "ast",
    "atm",
    "atom",
    "audr",
    "auto",
    "aywa",
    "bab",
    "bal",
    "band",
    "bat",
    "bay",
    "bcbc",
    "bcc",
    "bcd",
    "bch",
    "bcio",
    "bcn",
    "bco",
    "bcpt",
    "bdl",
    "beam",
    "bela",
    "bix",
    "blcn",
    "blk",
    "block",
    "blz",
    "bnb",
    "bnt",
    "bnty",
    "booty",
    "bos",
    "bpt",
    "bq",
    "brd",
    "bsd",
    "bsv",
    "btc",
    "btcd",
    "btch",
    "btcp",
    "btcz",
    "btdx",
    "btg",
    "btm",
    "bts",
    "btt",
    "btx",
    "burst",
    "bze",
    "call",
    "cc",
    "cdn",
    "cdt",
    "cenz",
    "chain",
    "chat",
    "chips",
    "cix",
    "clam",
    "cloak",
    "cmm",
    "cmt",
    "cnd",
    "cnx",
    "cny",
    "cob",
    "colx",
    "comp",
    "coqui",
    "cred",
    "crpt",
    "crw",
    "cs",
    "ctr",
    "ctxc",
    "cvc",
    "d",
    "dai",
    "dash",
    "dat",
    "data",
    "dbc",
    "dcn",
    "dcr",
    "deez",
    "dent",
    "dew",
    "dgb",
    "dgd",
    "dlt",
    "dnt",
    "dock",
    "doge",
    "dot",
    "drgn",
    "drop",
    "dta",
    "dth",
    "dtr",
    "ebst",
    "eca",
    "edg",
    "edo",
    "edoge",
    "ela",
    "elec",
    "elf",
    "elix",
    "ella",
    "emc",
    "emc2",
    "eng",
    "enj",
    "entrp",
    "eon",
    "eop",
    "eos",
    "eqli",
    "equa",
    "etc",
    "eth",
    "ethos",
    "etn",
    "etp",
    "eur",
    "evx",
    "exmo",
    "exp",
    "fair",
    "fct",
    "fil",
    "fjc",
    "fldc",
    "flo",
    "fsn",
    "ftc",
    "fuel",
    "fun",
    "game",
    "gas",
    "gbp",
    "gbx",
    "gbyte",
    "generic",
    "gin",
    "glxt",
    "gmr",
    "gno",
    "gnt",
    "gold",
    "grc",
    "grin",
    "grs",
    "gsc",
    "gto",
    "gup",
    "gusd",
    "gvt",
    "gxs",
    "gzr",
    "hight",
    "hodl",
    "hot",
    "hpb",
    "hsr",
    "ht",
    "html",
    "huc",
    "hush",
    "icn",
    "icx",
    "ignis",
    "ilk",
    "ink",
    "ins",
    "ion",
    "iop",
    "iost",
    "iotx",
    "iq",
    "itc",
    "jnt",
    "jpy",
    "kcs",
    "kin",
    "klown",
    "kmd",
    "knc",
    "krb",
    "lbc",
    "lend",
    "leo",
    "link",
    "lkk",
    "loom",
    "lpt",
    "lrc",
    "lsk",
    "ltc",
    "lun",
    "maid",
    "mana",
    "matic",
    "mcap",
    "mco",
    "mda",
    "mds",
    "med",
    "meetone",
    "mft",
    "miota",
    "mith",
    "mkr",
    "mln",
    "mnx",
    "mnz",
    "moac",
    "mod",
    "mona",
    "msr",
    "mth",
    "mtl",
    "music",
    "mzc",
    "nano",
    "nas",
    "nav",
    "ncash",
    "ndz",
    "nebl",
    "neo",
    "neos",
    "neu",
    "nexo",
    "ngc",
    "nio",
    "nlc2",
    "nlg",
    "nmc",
    "nmr",
    "npxs",
    "nuls",
    "nxs",
    "nxt",
    "oax",
    "ok",
    "omg",
    "omni",
    "ong",
    "ont",
    "oot",
    "ost",
    "ox",
    "oxt",
    "part",
    "pasc",
    "pasl",
    "pax",
    "pay",
    "payx",
    "pink",
    "pirl",
    "pivx",
    "plr",
    "poa",
    "poe",
    "polis",
    "poly",
    "pot",
    "powr",
    "ppc",
    "ppp",
    "ppt",
    "pre",
    "prl",
    "pungo",
    "pura",
    "qash",
    "qiwi",
    "qlc",
    "qrl",
    "qsp",
    "qtum",
    "r",
    "rads",
    "rap",
    "rcn",
    "rdd",
    "rdn",
    "ren",
    "rep",
    "repv2",
    "req",
    "rhoc",
    "ric",
    "rise",
    "rlc",
    "rpx",
    "rub",
    "rvn",
    "ryo",
    "safe",
    "sai",
    "salt",
    "san",
    "sbd",
    "sberbank",
    "sc",
    "shift",
    "sib",
    "sin",
    "sky",
    "slr",
    "sls",
    "smart",
    "sngls",
    "snm",
    "snt",
    "soc",
    "spank",
    "sphtx",
    "srn",
    "stak",
    "start",
    "steem",
    "storj",
    "storm",
    "stq",
    "strat",
    "stx",
    "sub",
    "sumo",
    "sys",
    "taas",
    "tau",
    "tbx",
    "tel",
    "ten",
    "tern",
    "tgch",
    "theta",
    "tix",
    "tkn",
    "tks",
    "tnb",
    "tnc",
    "tnt",
    "tomo",
    "tpay",
    "trig",
    "trtl",
    "trx",
    "tusd",
    "tzc",
    "ubq",
    "uma",
    "uni",
    "unity",
    "usd",
    "usdc",
    "usdt",
    "utk",
    "veri",
    "vet",
    "via",
    "vib",
    "vibe",
    "vivo",
    "vrc",
    "vrsc",
    "vtc",
    "vtho",
    "wabi",
    "wan",
    "waves",
    "wax",
    "wbtc",
    "wgr",
    "wicc",
    "wings",
    "wpr",
    "wtc",
    "x",
    "xas",
    "xbc",
    "xbp",
    "xby",
    "xcp",
    "xdn",
    "xem",
    "xin",
    "xlm",
    "xmcc",
    "xmg",
    "xmo",
    "xmr",
    "xmy",
    "xp",
    "xpa",
    "xpm",
    "xrp",
    "xsg",
    "xtz",
    "xuc",
    "xvc",
    "xvg",
    "xzc",
    "yfi",
    "yoyow",
    "zcl",
    "zec",
    "zel",
    "zen",
    "zest",
    "zil",
    "zilla",
    "zrx"]
