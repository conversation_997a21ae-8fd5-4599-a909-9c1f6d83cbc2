// @ts-ignore
import localforage from "localforage";
import * as React from "react";
import {LastOrders} from "@/components/pages/lastOrders/LastOrders";
import {Panel} from "react-bootstrap";
import {MPanel} from "@/components/common/MPanel";

export class DashboardLastOrders extends React.Component<any, any> {

    public render() {
        return <MPanel filled>
            <Panel.Heading>
                Last orders
            </Panel.Heading>
            <Panel.Body>
                <LastOrders limit={5}/>
            </Panel.Body>
        </MPanel>
    }
}