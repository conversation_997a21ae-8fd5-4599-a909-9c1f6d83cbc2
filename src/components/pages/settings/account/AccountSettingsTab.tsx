import {compose, graphql} from 'react-apollo';
import {path} from 'ramda';
import {observable} from 'mobx';
import {observer} from 'mobx-react';
import * as React from "react";
import {AccountSettings} from "@/graphql/settings/AccountSettings.graphql";
import {AccountSettingsMutation} from "@/graphql/settings/AccountSettingsMutation.graphql";
import {MPanel} from "@/components/common/MPanel";
import {error, success} from "@/utils/Toast";

@compose(graphql(AccountSettings, {
    options: {
        fetchPolicy: "network-only"
    }
}), graphql(AccountSettingsMutation))
@observer
export default class AccountSettingsTab extends React.Component<any> {

    @observable public email = '';
    @observable public dataFetched = false;

    public componentDidMount() {
        this.props.data.refetch();
    }

    public componentWillReceiveProps(nextProps) {
        const account: any = path(['data', 'user', 'account'])(nextProps) || {};
        if (account && !this.dataFetched && !nextProps.data.loading) {
            this.email = account.email;
            this.dataFetched = true;
        }
    }

    public onChange = e => {
        const {name, value} = e.target;
        this[name] = value;
    };

    public onSave = async () => {
        const result = await this.props.mutate({
            variables: {
                email: this.email,
            },
        });

        if (result.data.accountSettingsMutation.email) {
            success('Account Settings successfully changed');

            this.email = result.data.accountSettingsMutation.email;
        } else {
            error('Error');
        }
    };

    public render() {
        const {email} = this;
        return (<MPanel filled loading={this.props.data.loading}>
            <div className="panel-body">

                <form id="accountSettings">
                    <div className="form-group">
                        <label htmlFor="email" className="control-label">E-Mail</label>
                        <input
                            name="email"
                            type="text"
                            className="form-control"
                            id="email"
                            onChange={this.onChange}
                            value={email}
                            placeholder="E-Mail"/>
                    </div>
                    <div
                        id="saveAccountSettings"
                        onClick={this.onSave}
                        className="btn btn-accent pull-right">Save
                    </div>
                </form>
            </div>
        </MPanel>);
    }
}
