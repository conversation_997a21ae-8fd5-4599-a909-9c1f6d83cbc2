import PageHeader from '../../common/PageHeader';
import ExchangeSettings from './exchange/ExchangeSettings';
import AccountSettings from './account/AccountSettings';
import * as React from "react";
import {action, observable} from "mobx";
import {observer} from "mobx-react";
import BotSettings from "./bot/BotSettings";
import {TradeSettings} from "@/components/pages/settings/trade/TradeSettings";

@observer
export class SettingsPage extends React.Component<any> {

    @observable public activeTab = 0;

    @action
    public changeTab = tab => {
        this.activeTab = tab;
    };

    public isActiveLeft(tab) {
        return tab === this.activeTab ? 'active' : '';
    }

    public isActive(tab) {
        return tab === this.activeTab ? 'tab-pane active' : 'tab-pane';
    }

    public render() {
        return (
            <div className="container-fluid">

                <PageHeader title="Settings" section="Settings"/>

                <AccountSettings/>
                <TradeSettings/>
                <BotSettings/>
                <ExchangeSettings/>

            </div>);
    }
}
