/**
 * Created by dima on 10.08.2017.
 */

import {compose, graphql} from 'react-apollo';
import {path} from 'ramda';
import * as React from "react";
import {observable} from "mobx";
import {MPanel} from "@/components/common/MPanel";
import {observer} from "mobx-react";
import {AccountBotSettingsMutation} from "@/graphql/settings/AccountBotSetttingsMutation";
import {AccountBotSettings} from "@/graphql/settings/AccountBotSettings";
import {success} from "@/utils/Toast";

@compose(graphql(AccountBotSettingsMutation), graphql(AccountBotSettings, {
    options: {
        fetchPolicy: "network-only"
    }
}))
@observer
export default class BotSettingsTab extends React.Component<any> {

    @observable public pumpbot = false;
    @observable public dataFetched = false;

    public componentWillReceiveProps(nextProps) {
        const account: any = path(['data', 'user', 'account'])(nextProps) || {};
        if (account && !this.dataFetched && !nextProps.data.loading) {
            this.pumpbot = account.notifications ? account.notifications.pumpbot : false;
            this.dataFetched = true;
        }
    }

    public onSave = async () => {
        const result = await this.props.mutate({
            variables: {
                pumpbot: this.pumpbot,
            },
        });

        if (result.data.accountSettingsMutation) {
            success('Bot Settings successfully changed');
        }
    };

    public render() {
        return (<MPanel filled loading={this.props.data.loading}>
            <div className="panel-body">
                <div>
                    <form id="pumpbotform">
                        <div className="form-group">
                            <input
                                type="checkbox"
                                className="font-control checkbox checkbox-inline"
                                id="pumpbot"
                                onChange={() => {
                                    this.pumpbot = !this.pumpbot;
                                }}
                                checked={this.pumpbot}/>
                            <label htmlFor="pumpbot" className="m-l-xs control-label"> Activate Pump Bot
                                notifications</label>
                        </div>

                        <div
                            id="saveBot"
                            onClick={this.onSave}
                            className="btn btn-accent pull-right">Save
                        </div>
                    </form>
                </div>
            </div>
        </MPanel>);
    }
}
