/**
 * Created by dima on 10.08.2017.
 */

import * as React from "react";
import BinanceTab from "@/components/pages/settings/exchange/BinanceTab";
import GateIOTab from "@/components/pages/settings/exchange/GateIOTab";
import SelectExchangeTab from "@/components/pages/settings/exchange/SelectExchangeTab";

export default class ExchangeSettings extends React.Component<any> {

    public render() {
        return <div className="row m-t-lg">
            <div className="col-lg-12">
                <div className="tabs-container">
                    <div className="tabs-left">
                        <ul className="nav nav-tabs">
                            <li className="active">
                                <a>Select Exchange</a></li>
                        </ul>
                        <div className="tab-content">
                            <div id="tab-3" className="tab-pane active">
                                <SelectExchangeTab/>
                            </div>

                            <div id="tab-3" className="tab-pane active">
                                <BinanceTab/>
                            </div>

                            <div id="tab-3" className="tab-pane active">
                                <GateIOTab/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>;
    }
}
