import {compose, graphql} from 'react-apollo';
import {path} from 'ramda';
import * as React from "react";
import {action, observable} from "mobx";
import {SaveApiKeyMutation} from "@/graphql/settings/SaveApiKeyMutation.graphql";
import {ApiKey} from "@/graphql/settings/ApiKeyQuery.graphql";
import {MPanel} from "@/components/common/MPanel";
import {observer} from "mobx-react";
import {success} from "@/utils/Toast";

@compose(graphql(SaveApiKeyMutation), graphql(ApiKey, {
    options: {
        fetchPolicy: "network-only",
        variables: {
            exchange: 'pionex'
        }
    }
}))
@observer
export default class PionexTab extends React.Component<any> {

    @observable public apiKey = '';
    @observable public apiSecret = '';
    @observable public activated = false;
    @observable public dataFetched = false;

    public componentWillReceiveProps(nextProps) {
        const api: any = path(['data', 'user', 'exchangeKeys'])(nextProps) || {};
        if (api && !this.dataFetched && !nextProps.data.loading) {
            this.apiKey = api.key;
            this.apiSecret = api.secret;
            this.activated = api.activated;
            this.dataFetched = true;
        }
    }

    @action
    public onKeyChange = event => {
        this.apiKey = event.target.value;
    };

    @action
    public onSecretChange = event => {
        this.apiSecret = event.target.value;
    };

    public onSave = async () => {
        const result = await this.props.mutate({
            variables: {
                key: this.apiKey,
                secret: this.apiSecret,
                exchange: "pionex"
            },
        });

        if (result.data.saveApiKeyMutation) {
            success('API Key successfully changed');
        }

        this.activated = true;
    };

    public render() {
        return (<MPanel filled loading={this.props.data.loading}>
            <div className="panel-body">
                <div className="pull-right">
                    {this.activated ? <span className="label label-success">active</span> :
                        <span className="label label-danger">inactive</span>
                    }
                </div>

                <div>
                    <p>Pionex: Please go to Settings / Manage API Keys. Note, you MUST have 2FA enabled to
                        create an API key for your own
                        safety.
                        <br/>
                        Enable flags Read Info, Trade Limit and Trade Market to allow
                        automatic trading.
                    </p> <br/>

                    <form id="pionexForm">
                        <div className="form-group">
                            <label htmlFor="apiKey" className="control-label">API Key</label>
                            <input
                                type="text"
                                className="form-control"
                                id="apiKey"
                                onChange={this.onKeyChange}
                                value={this.apiKey}
                                placeholder="API Key"/>
                        </div>
                        <div className="form-group"><label htmlFor="apiSecret" className="control-label">API
                            Secret</label>
                            <input
                                type="text"
                                className="form-control"
                                id="apiSecret"
                                value={this.apiSecret}
                                onChange={this.onSecretChange}
                                placeholder="API Secret"/></div>
                        <div
                            id="saveBittrexApiKey"
                            onClick={this.onSave}
                            className="btn btn-accent pull-right">Save
                        </div>
                    </form>
                </div>
            </div>
        </MPanel>);
    }
}
