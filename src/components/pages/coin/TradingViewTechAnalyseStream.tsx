/* eslint-disable no-undef,new-cap,no-new */
import {observer} from "mobx-react";
import * as React from "react";
import {observable} from "mobx";
import {MPanel} from "@/components/common/MPanel";
import {TechnicalAnalysis} from "react-ts-tradingview-widgets";
import {path} from "ramda";
import {graphql} from "react-apollo";
import {UserAccountQuery} from "@/graphql/user/UserAccountQuery";

interface IProps {
    symbol: string;
}

// @ts-ignore
@graphql(UserAccountQuery)
@observer
export class TradingViewTechAnalyseStream extends React.Component<IProps> {
    public static defaultProps = {
        symbol: '',
    };

    @observable public showChart;
    @observable public interval = "1h";

    componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<{}>, snapshot?: any): void {
        if (this.showChart && (this.props.data.loading !== prevProps.data.loading)) {
            this.drawChart();
        }

        if (this.showChart && (this.props.symbol !== prevProps.symbol)) {
            this.drawChart();
        }
    }

    public componentDidMount() {
        this.interval = localStorage.getItem('binance.chart.techanalyse.interval');

        const display = localStorage.getItem('binance.chart.techanalyse.display');
        this.showChart = JSON.parse(display) !== false;
    }

    public toggleChart = () => {
        const display = localStorage.getItem('binance.chart.techanalyse.display');
        const newState: boolean = !JSON.parse(display);
        localStorage.setItem('binance.chart.techanalyse.display', String(newState));

        this.showChart = newState;
    };

    public drawChart() {
        let exchange = path(["data", "user", "account", "exchange"], this.props)
        if (exchange === "binance") {
            exchange = "BINANCE"
        } else if (exchange === "gateio") {
            exchange = "GATEIO"
        }
        return <TechnicalAnalysis colorTheme="dark"
                                  height={360}
                                  isTransparent={false}
                                  autosize={false}
                                  showIntervalTabs={false}
                                  interval={this.interval ? this.interval : "2h"}
                                  symbol={`${exchange}:` + this.props.symbol}
                                  width="100%"></TechnicalAnalysis>
    }

    public render() {

        const toIntervalClass = (interval) => {
            return this.interval == interval ? "btn-success" : "btn btn-default"
        }

        const toDisplayClass = () => {
            return this.showChart ? "btn-default" : "btn-default"
        }

        return (<MPanel filled>
            <div className="panel-heading">
                Tech Analysis

                <span className={"m-l-sm btn-xs m-r-xs " + toIntervalClass("5m")}
                      onClick={() => this.setChart('5m')}>5m</span>
                <span className={"btn-xs m-r-xs " + toIntervalClass("1h")}
                      onClick={() => this.setChart('1h')}>1h</span>
                <span className={"btn-xs m-r-xs " + toIntervalClass("2h")}
                      onClick={() => this.setChart('2h')}>2h</span>
                <span className={"btn-xs m-r-xs " + toIntervalClass("4h")}
                      onClick={() => this.setChart('4h')}>4h</span>
                <span className={"btn-xs m-r-xs " + toIntervalClass("1D")}
                      onClick={() => this.setChart('1D')}>1d</span>

                <span className={"btn-xs m-r-xs pull-right " + toDisplayClass()} onClick={this.toggleChart}>
          <i className={this.showChart ? 'fa fa-eye-slash' : 'fa fa-eye'}/></span>
            </div>

            <div className="panel-body">
                <div className="row">
                    {this.showChart ? this.drawChart() : null}
                </div>
            </div>
        </MPanel>);
    }

    public setChart = interval => {
        localStorage.setItem('binance.chart.techanalyse.interval', interval);
        this.interval = interval
    };
}
