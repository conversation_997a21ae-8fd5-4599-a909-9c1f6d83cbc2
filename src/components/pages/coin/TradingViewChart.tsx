/* eslint-disable no-undef,new-cap,no-new */
import {MPanel} from "@/components/common/MPanel";
import {observable} from "mobx";
import {observer} from "mobx-react";
import * as React from "react";
import {graphql} from "react-apollo";
import {UserAccountQuery} from "@/graphql/user/UserAccountQuery";
import {path} from "ramda";

interface IProps {
    symbol: string;
}

// @ts-ignore
@graphql(UserAccountQuery)
@observer
export class TradingViewChart extends React.Component<IProps> {
    public static defaultProps = {
        symbol: '',
    };

    @observable public showChart;
    @observable public interval = "5"

    constructor(props) {
        super(props);

        const display = localStorage.getItem('binance.chart.display');
        this.interval = localStorage.getItem('binance.chart.interval');
        this.showChart = JSON.parse(display) !== false;
    }

    public componentDidMount() {
        if (this.showChart) {
            this.drawChart();
        }
    }

    componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<{}>, snapshot?: any): void {
        if (this.showChart && (this.props.data.loading !== prevProps.data.loading)) {
            this.drawChart();
        }

        if (this.showChart && (this.props.symbol !== prevProps.symbol)) {
            this.drawChart();
        }
    }

    public setChart = interval => {
        this.interval = interval
        localStorage.setItem('binance.chart.interval', interval);

        if (this.showChart) {
            this.drawChart();
        }
    };

    public toggleChart = () => {
        const display = localStorage.getItem('binance.chart.display');
        const newState: boolean = !JSON.parse(display);
        localStorage.setItem('binance.chart.display', String(newState));

        this.showChart = newState;
        if (newState) {
            this.drawChart();
        }
    };

    public drawChart = () => {
        let exchange = path(["data", "user", "account", "exchange"], this.props)
        if (exchange === "binance") {
            exchange = "BINANCE"
        } else if (exchange === "gateio") {
            exchange = "GATEIO"
        }

        if (window.TradingView !== undefined) {
            new window.TradingView.widget(
                {
                    autosize: true,
                    symbol: `${exchange}:${this.props.symbol}`,
                    interval: this.interval !== null ? this.interval : '5',
                    timezone: 'Europe/Berlin',
                    theme: 'Dark',
                    style: '1',
                    locale: 'en',
                    toolbar_bg: '#f1f3f6',
                    enable_publishing: false,
                    hide_top_toolbar: true,
                    hide_legend: true,
                    hide_ideas: true,
                    hide_side_toolbar: true,
                    allow_symbol_change: false,
                    hide_volume: false,
                    show_popup_button: false,
                    save_image: false,
                    calendar: false,
                    symboledit: false,
                    hideideas: true,
                    container_id: 'tradingview_e4340',
                    referral_id: '10238',
                },
            );
        }
    }

    public render() {
        const lsInterval = this.interval
        const toIntervalClass = (interval) => {
            return this.interval == interval ? "" : "btn btn-default"
        }
        return (<MPanel filled>
            <div className="panel-heading">

                Chart {lsInterval !== null ? lsInterval : '5'}

                <span className={"m-l-sm btn-xs m-r-xs " + toIntervalClass("5")}
                      onClick={() => this.setChart('5')}>5m</span>
                <span className={"btn-xs m-r-xs " + toIntervalClass("15")}
                      onClick={() => this.setChart('15')}>15m</span>
                <span className={"btn-xs m-r-xs " + toIntervalClass("60")}
                      onClick={() => this.setChart('60')}>1h</span>
                <span className={"btn-xs m-r-xs " + toIntervalClass("120")}
                      onClick={() => this.setChart('120')}>2h</span>
                <span className={"btn-xs m-r-xs " + toIntervalClass("240")}
                      onClick={() => this.setChart('240')}>4h</span>
                <span className={"btn-xs m-r-xs " + toIntervalClass("D")}
                      onClick={() => this.setChart('D')}>1d</span>

                <span className={"btn btn-default btn-xs m-r-xs"} onClick={this.toggleChart}>
          <i className={this.showChart ? 'fa fa-eye-slash' : 'fa fa-eye'}/></span>
                <a
                    href={`https://www.tradingview.com/chart/?symbol=BINANCE:${this.props.symbol}`}
                    rel="noopener"
                    target="_blank">
                    <span className="blue-text pull-right">Full</span>
                </a>
            </div>

            <div className="panel-body">
                <div className="row">
                    <div className="tradingview-widget-container">
                        {this.showChart ?
                            <div id="tradingview_e4340"/> : null}
                    </div>
                </div>
            </div>
        </MPanel>);
    }
}
