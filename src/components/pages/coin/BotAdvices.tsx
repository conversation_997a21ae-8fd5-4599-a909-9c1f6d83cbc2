/**
 * Created by dima on 11.08.2017.
 */

import {BotSignalsDistinctWithoutStrategy} from "@/graphql/bot/BotSignalsDistinctWithoutStrategy.graphql";
import PriceCalculation from "@/utils/PriceCalculation";
import {observable} from 'mobx';
import {inject, observer} from 'mobx-react';

import {path} from 'ramda';
import * as React from "react";
import {graphql} from 'react-apollo';
import {Link} from 'react-router-dom';

// @ts-ignore
@graphql(BotSignalsDistinctWithoutStrategy, {
    options: props => ({
        fetchPolicy: 'network-only',
        variables: {
            indicator: 'nesterov',
            interval: '15',
            symbol: props.symbol,
        },
    }),
})
@inject("exchangeStore")
@observer
class BotAdvices extends React.Component<any> {
    public static defaultProps = {
        symbol: '',
    };

    // @observable public showAddBotModal = false;
    @observable public selectedStrategy = 'nesterov';
    @observable public advice = 'short';
    @observable public longPrice = 0;

    // public addNewBot = async signal => {
    //     const lastOrders: GQL.INewOrder[] = path(['data', 'exchange', 'lastOrders'])(this.props) as GQL.INewOrder[] || [];
    //     const {advice, id} = signal;
    //
    //     this.selectedStrategy = id;
    //     this.advice = advice;
    //     this.longPrice = head(lastOrders) ? head(lastOrders).price : 0;
    //
    //     this.showAddBotModal = true;
    // };

    public renderBotAdvices() {
        const {exchangeInfo} = this.props.exchangeStore

        if (this.props.loading) {
            return <div className="loader">
                <div className="loader-spin"/>
            </div>
        }

        const signals: any = path(['data', 'bot', 'getIndicators'])(this.props) || [];
        const filteredSignals = signals.filter(x => x.indicator === "nesterov")

        // let marketBots: any = path(['data', 'bot', 'autobot', 'marketBotsBig'])(this.props) || [];
        // marketBots = marketBots.filter(bot => bot.active && bot.symbol === this.props.symbol);

        const renderSignals = filteredSignals.map(signal => {
            const price = signal.price ? PriceCalculation.getFixedPrice(signal.price, exchangeInfo, this.props.symbol) : null

            return <tr key={this.props.symbol + signal.id + new Date().getTime()}>
                <td>
                    <Link className="normal-text-color"
                          to={{pathname: `/tradingbot/signals/${signal.id}/${signal.interval}/${this.props.symbol}`}}>{signal.id}</Link>
                </td>
                <td>
                    {signal.interval}
                </td>
                <td>
                    <span className={signal.action === 'long'
                        ? 'text-success'
                        : 'text-danger'}>{signal.action}</span>
                </td>
                <td>{price}</td>
                <td>
                    {/*{marketBots.length === 0 ? (*/}
                    {/*    <div className="m-l-sm btn btn-success btn-xs m-r-xs"*/}
                    {/*         onClick={_e => this.addNewBot(signal)}>*/}
                    {/*        add*/}
                    {/*    </div>*/}
                    {/*) : <DeleteBotBtn botId={marketBots[0].id} symbol={this.props.symbol}/>}*/}
                </td>
            </tr>
        })

        return (
            <table className="table table-condensed small">
                <thead>
                <tr>
                    <th>
                        Strategy
                    </th>
                    <th>
                        Interval
                    </th>
                    <th>
                        Advice
                    </th>
                    <th>
                        Price
                    </th>
                    <th>
                        Action
                    </th>
                </tr>
                </thead>
                <tbody>
                {renderSignals}
                </tbody>
            </table>
        );
    }

    public render() {
        let marketBots: any = path(['data', 'bot', 'autobot', 'marketBotsBig'])(this.props) || [];
        marketBots = marketBots.filter(bot => bot.active && bot.symbol === this.props.symbol);

        return (
            <div className="panel panel-filled panel-c-warning">
                <div className="panel-heading">
                    Bot advices {marketBots.length !== 0 ?
                    <div className="pull-right label label-success">Bot active</div> : null}
                </div>

                <div className="panel-body" style={{overflowX: 'auto'}}>
                    {this.renderBotAdvices()}
                </div>

                {/*<SetupBotModal asset={this.props.symbol}*/}
                {/*               advice={this.advice}*/}
                {/*               strategy={this.selectedStrategy}*/}
                {/*               longPrice={this.longPrice}*/}
                {/*               showModal={this.showAddBotModal}*/}
                {/*               closeModal={() => {*/}
                {/*                   this.showAddBotModal = false;*/}
                {/*               }}/>*/}
            </div>
        );
    }
}


export default BotAdvices;
