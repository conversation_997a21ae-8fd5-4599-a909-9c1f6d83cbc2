/**
 * Created by dima on 11.08.2017.
 */

import {MPanel} from "@/components/common/MPanel";
import {observable} from 'mobx';
import {inject, observer} from 'mobx-react';
import {find, head, path, propEq} from 'ramda';
import * as React from "react";
import {graphql} from "react-apollo";
import {CurrentPriceQuery} from "@/graphql/pages/CurrentPriceQuery.graphql";

// @ts-ignore
@graphql(CurrentPriceQuery, {
    options: props => ({
        pollInterval: 15000,
        variables: {
            symbol: props.symbol,
            limit: 30,
            status: 'FILLED,PARTIALLY_FILLED',
            interval: '1d'
        },
    }),
})
@inject("coinFormStore", "coinStore")
@observer
export class Balances extends React.Component<any> {

    public static defaultProps = {
        pageData: {},
        asset: '',
        symbol: '',
        sendAmountToInput: null,
    };

    @observable public loadingDone = false;

    public componentWillReceiveProps(nextProps) {
        if (!nextProps.pageData.loading && !this.loadingDone) {
            this.loadingDone = true;
        }
    }

    public sendAmountToInput = amount => {
        this.props.sendAmountToInput(amount);
    };

    public render() {
        const {pageData, data, coinStore, coinFormStore} = this.props;

        if (this.props.loading) {
            return <div className="loader">
                <div className="loader-spin"/>
            </div>
        }

        const balances = path(['pageData', 'exchange', 'accountInfo', 'balances'])(this.props) || [];
        const balance = find(propEq('asset', this.props.asset))(balances) || {};
        const total = balance.free ? coinFormStore.getFixedAmountWithLength(Number(balance.free + balance.locked)) : 0;
        const available = balance.free
            ? coinFormStore.getFixedAmountWithLength(balance.free)
            : null
        const valueBtc = balance.btcValue
            ? Number(balance.btcValue).toFixed(8)
            : null
        const valueUsd = balance.usdValue
            ? Number(balance.usdValue).toFixed(2)
            : null

        const lastOrders: any = path(['exchange', 'lastOrders'])(data) || [];
        const lastTrade: any = head(lastOrders) || {};
        const lastPrice = coinStore.currentCoinPrice ? coinFormStore.getFixedPrice(coinStore.currentCoinPrice) : null
        const profit = total ? Number(Number(lastPrice * lastTrade.executedQty)
            - Number(lastTrade.price * lastTrade.executedQty)).toFixed(2) : null

        return (
            <MPanel className="panel-c-primary" filled loading={pageData.loading && !this.loadingDone}>
                <div className="panel-heading">
                    Balance <u>{this.props.symbol}</u>
                </div>
                <div className="panel-body">
                    <table className="table table-condensed table-no-border">
                        <tbody>
                        <tr>
                            <td><h6>Balance</h6></td>
                            <td className="text-right"><h6>
                                <a className="no-color cursor-pointer">
                                    {total}
                                </a>
                            </h6></td>
                        </tr>

                        <tr>
                            <td><h6>Available</h6></td>
                            <td className="text-right"><h6>
                                <a className="no-color cursor-pointer">
                                    {available}
                                </a>
                            </h6>
                            </td>
                        </tr>

                        <tr>
                            <td><h6>Value BTC</h6></td>
                            <td className="text-right">
                                <h6><span className="no-color">{valueBtc}</span></h6></td>
                        </tr>

                        <tr>
                            <td><h6>Value $</h6></td>
                            <td className="text-right">
                                <h6><span className="no-color">${valueUsd}</span></h6></td>
                        </tr>

                        {profit && <tr>
                            <td><h6>Current profit</h6></td>
                            <td className="text-right">
                                <h6>
                                <span
                                    className="no-color">{this.toProfit(profit)}</span>
                                </h6></td>
                        </tr>}
                        </tbody>
                    </table>
                </div>
            </MPanel>);
    }

    public toProfit(value) {
        const profit = Number(value)
            .toFixed(2);
        let className = '';
        let text = '';

        if (value > 0) {
            className = 'text-success';
            text = `+${profit}$`;
        } else if (value < 0) {
            className = 'text-danger';
            text = `${profit}$`;
        } else if (value === 0) {
            className = 'text-muted';
            text = `${profit}$`;
        }

        return <span className={className}>{text}</span>;
    }
}
