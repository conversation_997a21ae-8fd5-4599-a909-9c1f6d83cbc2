/* eslint-disable no-undef,new-cap,no-new */
import {observer} from "mobx-react";
import * as React from "react";
import {observable} from "mobx";
import {MPanel} from "@/components/common/MPanel";

interface IProps {
    symbol: string;
}

@observer
export class TradingViewIdeaStream extends React.Component<IProps> {
    public static defaultProps = {
        symbol: '',
    };

    @observable public showChart;

    constructor(props) {
        super(props);

        const display = localStorage.getItem('binance.chart.ideastream.display');
        this.showChart = JSON.parse(display) !== false;
    }

    public componentDidMount() {
        if (this.showChart) {
            this.drawChart();
        }
    }

    componentDidUpdate(prevProps: Readonly<IProps>, prevState: Readonly<{}>, snapshot?: any): void {
        if (this.showChart && (this.props.symbol !== prevProps.symbol)) {
            this.drawChart();
        }
    }

    public toggleChart = () => {
        const display = localStorage.getItem('binance.chart.ideastream.display');
        const newState: boolean = !JSON.parse(display);
        localStorage.setItem('binance.chart.ideastream.display', String(newState));

        this.showChart = newState;

        setTimeout(() => {
            if (newState) {
                this.drawChart();
            }
        }, 100)
    };

    public drawChart() {
        if (window.TradingView !== undefined) {
            new window.TradingView.IdeasStreamWidget({
                "container_id": "tv-ideas-stream-8251a",
                "startingCount": 1,
                "width": "100%",
                "height": 300,
                "mode": "integrate",
                "bgColor": "rgb(55, 57, 65)",
                "headerColor": "rgb(55, 57, 65)",
                "borderColor": "rgb(55, 57, 65)",
                "locale": "en",
                "sort": "trending",
                "time": "week",
                "interval": "all",
                "stream": "bitcoin",
                "symbol": "BINANCE:" + this.props.symbol
            });
        }
    }

    public render() {
        return (<MPanel filled>
            <div className="panel-heading">

                Idea Stream

                <span className={"btn btn-default btn-xs m-r-xs pull-right"} onClick={this.toggleChart}>
          <i className={this.showChart ? 'fa fa-eye-slash' : 'fa fa-eye'}/></span>
            </div>

            <div className="panel-body">
                <div className="row">
                    {this.showChart && <div className="tradingview-widget-container">
                        <div id="tv-ideas-stream-8251a"/>
                    </div>}
                </div>
            </div>
        </MPanel>);
    }
}
