/**
 * Created by dima on 11.08.2017.
 */

import {descend, head, prop, sortWith} from 'ramda';
import {withA<PERSON>lo} from 'react-apollo';
import {observable} from "mobx";
import * as React from "react";
import {TradesSubscription} from "@/graphql/binance/ws/TradesSubscription.graphql";
import {inject, observer} from "mobx-react";
import * as shortid from "shortid";

@withApollo
@inject('coinStore', 'coinFormStore')
@observer
export default class MarketHistory extends React.Component<any> {
    private subscriptionObserver = null;

    @observable public loadingDone = false;
    @observable public trades: any = [];

    public componentWillMount() {
        this.subscribe();
    }

    public componentWillUnmount() {
        if (this.subscriptionObserver) {
            this.subscriptionObserver.unsubscribe();
        }
    }

    public subscribe() {
        const self = this;
        this.subscriptionObserver = this.props.client.subscribe({
            query: TradesSubscription,
            variables: {
                symbol: this.props.symbol,
            },
        })
            .subscribe({
                next(result) {
                    self.trades = result.data.marketHistory;

                    const trade = head(sortWith([descend(prop('time'))])(self.trades)) as GQL.ITrade;
                    if (trade && trade.price) {
                        self.props.coinStore.currentCoinPrice = trade.price;

                        if (!SERVER && this.props && this.props.symbol) {
                            document.title = `${trade.price} ${this.props.symbol}`;
                        }
                    }

                },
                error(err) {
                    console.log(err);
                },
            });
    }

    componentDidUpdate(prevProps: Readonly<any>, prevState: Readonly<{}>, snapshot?: any): void {
        if (this.props.symbol !== prevProps.symbol) {
            this.subscribe();
        }
    }

    /**
     *
     */
    public getOrder = () => {
        let {trades} = this;
        const {coinFormStore} = this.props
        trades = sortWith([descend(prop('time'))])(trades);

        return trades.map(order => {
            const price = coinFormStore.getFixedPriceWithLength(order.price)
            const qty = coinFormStore.getFixedAmountWithLength(order.qty)
            const date = new Date(order.time).toLocaleTimeString()
            const total = Number(order.price * order.qty).toFixed(2)

            return <tr key={order.rowId + shortid.generate()}>
                <td>{date}</td>
                <td>{!order.isBuyerMaker ? <span className="text-success">Buy</span> :
                    <span className="text-danger">Sell</span>}</td>
                <td>
                    <span className="text-accent">{price}</span>
                </td>
                <td>{total}</td>
            </tr>
        })
    };

    public render() {
        return (<div className="panel panel-filled">
            <div className="panel-heading">
                Market History
            </div>

            <div className="panel-body" style={{overflowX: 'auto'}}>
                <table className="table table-condensed small">
                    <thead>
                    <tr>
                        <th style={{width: '100px'}}>Order Date</th>
                        <th style={{width: '50px'}}>Type</th>
                        <th style={{width: '90px'}}>Price $</th>
                        <th>Total $</th>
                    </tr>
                    </thead>
                    <tbody>
                    {this.getOrder()}
                    </tbody>
                </table>
            </div>
        </div>);
    }
}
