/**
 * Created by dima on 11.08.2017.
 */

import {compose, graphql} from 'react-apollo';
import {path} from 'ramda';

import {OpenOrdersQuery} from "@/graphql/binance/OpenOrderQuery.graphql";
import {CancelOrderMutation} from "@/graphql/binance/CancelOrderMutation.graphql";
import * as React from "react";
import {CoinPageQuery} from "@/graphql/pages/CoinPageQuery.graphql";
import {inject, observer} from "mobx-react";
import {observable} from "mobx";
import PriceCalculation from "@/utils/PriceCalculation";
import IOpenOrder = GQL.IOpenOrder;
import {error, success} from "@/utils/Toast";

@compose(
    graphql(CancelOrderMutation),
    graphql(OpenOrdersQuery, {
        options: props => ({
            pollInterval: 15000,
            // @ts-ignore
            skip: !props.symbol,
            variables: {
                // @ts-ignore
                symbol: props.symbol,
                withBinance: true
            },
        }),
    }),
)
@inject("coinFormStore")
@observer
class OpenedOrders extends React.Component<any> {

    @observable public orders = {};

    /**
     *
     * @param e
     * @param order
     */
    public cancelOrder = async (e, order) => {
        this.orders[order.orderId] = true;

        const symbol = this.props.symbol;

        const result = await this.props.mutate({
            variables: {
                type: order.type,
                symbol,
                orderId: order.orderId,
                recvWindow: 10000
            },
            refetchQueries: [
                {
                    query: CoinPageQuery,
                    variables: {
                        symbol: this.props.symbol,
                        assets: ['BTC', 'USDT', this.props.asset],
                    },
                },
                {
                    query: OpenOrdersQuery,
                    variables: {
                        symbol,
                        withBinance: true
                    },
                }],
        });

        this.orders[order.orderId] = false;

        if (result.data.cancelOrderMutation.ok) {
            success('Order canceled');
        } else {
            error(result.error);
        }
    };

    public getOrders() {
        const {coinFormStore} = this.props
        const orders = [];

        if (this.props.loading) {
            return <div className="loader">
                <div className="loader-spin"/>
            </div>
        }

        const openedOrders: IOpenOrder[] = path(['exchange', 'openOrders'])(this.props.data) as IOpenOrder[] || [] as IOpenOrder[];

        if (openedOrders.length === 0) {
            return (<tr>
                <td colSpan={6}>
                    No opened orders
                </td>
            </tr>);
        }

        openedOrders.forEach(order => {
            const price = coinFormStore.getFixedPrice(Number(order.price))
            const total = Number(order.origQty * price).toFixed(2)
            const qty = PriceCalculation.getFixedAmountWithLength(order.origQty,
                coinFormStore.exchangeInfo,
                this.props.symbol)
            const isAutoSLOrder = order.type === 'AUTO_STOP_LOSS'

            orders.push(<tr key={order.orderId}>
                <td>{new Date(order.time).toLocaleString()}</td>
                <td>{this.renderType(order.type)}</td>
                <td><span className="text-accent">{price}</span></td>
                <td>{total}</td>
                <td>{qty}</td>
                <td>
                    <button className="btn-danger btn btn-xs"
                            disabled={this.orders[order.orderId]}
                            onClick={e => this.cancelOrder(e, order)}>Cancel
                    </button>
                </td>
            </tr>);
        });

        return orders;
    }

    public render() {
        return (<div className="panel panel-filled panel-c-warning">
            <div className="panel-heading">
                Opened Orders
            </div>

            <div className="panel-body" style={{overflowX: 'auto'}}>
                <table className="table table-condensed small">
                    <thead>
                    <tr>
                        <th style={{width: '136px'}}>Order Date</th>
                        <th style={{width: '50px'}}>Type</th>
                        <th style={{width: '90px'}}>Price</th>
                        <th>Total $</th>
                        <th>Qty</th>
                        <th>Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    {this.getOrders()}
                    </tbody>
                </table>
            </div>
        </div>);
    }

    public renderType(type) {
        switch (type) {
            case 'BOT_TAKE_PROFIT':
                return 'TP';
            case 'BOT_STOP_LOSS':
                return 'SL';
            case 'BOT_TP_SL':
                return 'TPSL';
            case 'AUTO_STOP_LOSS':
                return 'ASL'
            case 'AUTO_TAKE_PROFIT':
                return 'ATP'
            default:
                return type;
        }
    }
}

export default OpenedOrders;
