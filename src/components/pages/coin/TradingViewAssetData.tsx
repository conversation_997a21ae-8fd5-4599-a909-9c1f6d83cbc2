import {MPanel} from "@/components/common/MPanel";
import {observable} from "mobx";
import {observer} from "mobx-react";
import * as React from "react";
import {FundamentalData} from "react-ts-tradingview-widgets";

interface IProps {
    symbol: string;
}

@observer
export class TradingViewAssetData extends React.Component<IProps> {
    public static defaultProps = {
        symbol: '',
    };

    @observable public showChart;
    @observable public interval = "1h";

    public componentDidMount() {
        const display = localStorage.getItem('binance.chart.assetdata.display');
        this.showChart = JSON.parse(display) !== false;
    }

    public toggleChart = () => {
        const display = localStorage.getItem('binance.chart.assetdata.display');
        const newState: boolean = !JSON.parse(display);
        localStorage.setItem('binance.chart.assetdata.display', String(newState));

        this.showChart = newState;
    };

    public drawChart() {
        return <FundamentalData colorTheme="dark"
                                  height={360}
                                  isTransparent={false}
                                  autosize={false}
                                  symbol={"BINANCE:" + this.props.symbol}
                                  width="100%"></FundamentalData>
    }

    public render() {
        return (<MPanel filled>
            <div className="panel-heading">
                Asset Data
                <span className={"btn btn-default btn-xs m-r-xs pull-right"} onClick={this.toggleChart}>
          <i className={this.showChart ? 'fa fa-eye-slash' : 'fa fa-eye'}/></span>
            </div>

            <div className="panel-body">
                <div className="row">
                    {this.showChart ? this.drawChart() : null}
                </div>
            </div>
        </MPanel>);
    }
}
