import {BotDeleteMutation} from "@/graphql/bot/BotDeleteMutation.graphql";
import {BotSignalsDistinctWithoutStrategy} from "@/graphql/bot/BotSignalsDistinctWithoutStrategy.graphql";
import * as React from "react";
import {graphql} from "react-apollo";
import {error, success} from "@/utils/Toast";

// @ts-ignore
@graphql(BotDeleteMutation)
export class DeleteBotBtn extends React.Component<any> {
    public deleteBot = async () => {
        const {botId} = this.props;

        const result = await this.props.mutate({
            variables: {
                id: botId,
            },
            refetchQueries: [
                {
                    query: BotSignalsDistinctWithoutStrategy,
                    variables: {
                        symbol: this.props.symbol,
                    },
                }],
        });

        const data = result.data.deleteBotMutation;

        if (data.ok) {
            success('Bot removed');
        } else {
            error(data.error);
        }
    };

    public render() {
        return (
            <div className="m-l-sm btn btn-danger btn-xs m-r-xs"
                 onClick={this.deleteBot}>delete
            </div>
        );
    }
}
