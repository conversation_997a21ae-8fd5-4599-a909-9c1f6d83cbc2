import {action} from "mobx";
import {inject, observer} from "mobx-react";
import {find, path, propEq} from "ramda";
import * as React from "react";
import {error} from "@/utils/Toast";

@inject('coinFormStore')
@inject('coinStore')
@observer
export class Amount extends React.Component<any> {
    public render() {
        const {disableMaxBuy, disableMaxSell, coinFormStore} = this.props;

        return (<div className="form-group">
            <label htmlFor="limitAmount">Amount USDT</label>
            {!disableMaxSell && <span className="badge badge-danger pull-right btn btn-danger m-l-xs"
                                      onClick={this.setMaxAmountSell}>max sell</span>}
            {!disableMaxBuy && <span className="badge badge-success pull-right btn btn-success"
                                     onClick={this.setMaxAmountBuy}>max buy</span>}

            <span className="pull-right no-color input-price">{coinFormStore.usdEquivalent}</span>
            <input type="number"
                   className="form-control"
                   id="limitAmount"
                   onChange={this.handleChange}
                   value={coinFormStore.amount}/>
        </div>);
    }

    @action.bound
    public handleChange = e => {
        const {coinFormStore, coinPrice} = this.props;
        let value = e.target.value;
        const regex = /(\d.*([.,])?(\d.+)?)?/

        if (regex.test(value)) {
            value = value.replace(",", ".")

            coinFormStore.qty = coinFormStore.getFixedAmount(Number(value) / Number(coinPrice))
            coinFormStore.amount = value;
        }
    };

    @action
    public setMaxAmountSell = () => {
        const {coinPrice, coinFormStore} = this.props;

        const balances: any = path(['parentData', 'exchange', 'accountInfo', 'balances'])(this.props);
        const balance = find(propEq('asset', this.props.coinFormStore.asset))(balances)

        if (balance) {
            const maxSellAmountAsset = coinFormStore.getFixedAmount(balance ? balance.free : 0);
            coinFormStore.qty = maxSellAmountAsset;
            coinFormStore.amount = coinFormStore.getFixedAmount(Number(maxSellAmountAsset) * Number(coinPrice));
        } else {
            coinFormStore.amount = 0
            coinFormStore.qty = 0;
        }
    };

    @action.bound
    public setMaxAmountBuy() {
        const {coinFormStore, coinPrice} = this.props;

        const balances: any = path(['parentData', 'exchange', 'accountInfo', 'balances'])(this.props);
        const balanceUsdt = find(propEq('asset', 'USDT'))(balances) || {};
        const buyAmountUsdt = (balanceUsdt.free * 0.985).toFixed(2)

        if (Number(buyAmountUsdt)=== 0) {
            error('Not enough USDT');
        }

        coinFormStore.amount = coinFormStore.getFixedAmount(buyAmountUsdt);
        coinFormStore.qty = Math.floor(Number(buyAmountUsdt) / Number(coinPrice))
    }
}