import {inject, observer} from "mobx-react";
import * as React from "react";

@inject('coinFormStore', 'coinStore')
@observer
export class BuyStopCheckbox extends React.Component<any> {

    public handleCheck = () => {
        const {coinFormStore} = this.props;
        coinFormStore.buyStopActive = !coinFormStore.buyStopActive
    };

    public render() {
        const {coinFormStore, coinStore} = this.props;
        const stopPercent = coinFormStore.stopPercent
        const tpPercent = coinFormStore.tpPercent
        const stopPrice = coinFormStore.getFixedPrice(Number(this.props.currentCoinPrice) * ((100 - stopPercent) / 100))
        const tpPrice = coinFormStore.getFixedPrice(Number(this.props.currentCoinPrice) * ((100 + tpPercent) / 100))
        const alwaysSetStoploss = coinStore.alwaysSetStoploss
        const alwaysSetTP = coinStore.alwaysSetTP

        return <div className="row m-b-md">
            <div className="col-xs-6 col-md-6 col-lg-6">
                <input type="checkbox"
                       checked={alwaysSetStoploss || coinFormStore.buyStopActive}
                       id="setStoploss"
                       className="checkbox checkbox-inline"
                       disabled={alwaysSetStoploss}
                       onChange={this.handleCheck}/>
                <label className="m-l-xs small" htmlFor="setStoploss">SL -${stopPercent}% @{stopPrice}</label>
            </div>
            <div className="col-xs-6 col-md-6 col-lg-6">
                <input type="checkbox"
                       checked={alwaysSetTP || coinFormStore.buyTPActive}
                       id="setTP"
                       className="checkbox checkbox-inline"
                       disabled={alwaysSetStoploss}
                       onChange={this.handleCheck}/>
                <label className="m-l-xs small" htmlFor="setStoploss">TP +${tpPercent}% @{tpPrice}</label>
            </div>
        </div>
    }
}