import {action} from "mobx";
import {inject, observer} from "mobx-react";
import * as React from "react";

interface IProps {
    parentData: any;
    coinFormStore?: any;
    coinStore?: any;

    minus?: boolean
}

@inject('coinFormStore', 'coinStore')
@observer
export class Price extends React.Component<IProps> {

    public handleChange = e => {
        const {coinFormStore} = this.props;
        let value = e.target.value;
        const regex = /(\d.*([.,])?(\d.+)?)?/

        if (!(regex.test(value))) {
            return;
        }

        value = value.replace(",", ".")
        coinFormStore.price = value;
    };

    public render(): React.ReactNode {
        const {coinFormStore, minus} = this.props;

        return (<div className="form-group">

            <label htmlFor="limitPrice">Price</label>

            {minus
                ? <span className="badge badge-danger pull-right btn btn-default"
                        onClick={() => this.setStopPrice(-5)}>-5%</span>
                : <span>
                    <span className="badge badge-danger pull-right btn btn-default"
                          onClick={() => this.setStopPrice(20)}>+20%</span>
                    <span className="badge badge-danger m-r-xs pull-right btn btn-default"
                          onClick={() => this.setStopPrice(10)}>+10%</span>
                </span>
            }


            <input type="number"
                   className="form-control"
                   id="limitPrice"
                   onChange={this.handleChange}
                   value={coinFormStore.price}/>
        </div>);

    }

    @action
    public setStopPrice = mult => {
        const {coinFormStore, coinStore} = this.props;

        const coinPrice: any = coinStore.currentCoinPrice
        coinFormStore.price = coinFormStore.getFixedPrice(coinPrice * (mult / 100 + 1))
    };
}