import {inject, observer} from "mobx-react";
import * as React from "react";

@inject('coinFormStore')
@observer
export class StopPrice extends React.Component<any> {

    public handleChange = e => {
        const {coinFormStore} = this.props;
        let value = e.target.value;
        const regex = /(\d.*([.,])?(\d.+)?)?/

        if (!(regex.test(value))) {
            return;
        }

        value = value.replace(",", ".")
        coinFormStore.stopPrice = value;
    };

    public render() {
        const {coinFormStore} = this.props;

        return (<div className="form-group">
            <label htmlFor="limitPrice">Stop Price</label>
            <input type="number"
                   className="form-control"
                   id="stopPrice"
                   onChange={this.handleChange}
                   value={coinFormStore.stopPrice}/>
        </div>);
    }
}