import {action} from "mobx";
import {inject, observer} from "mobx-react";
import {path} from "ramda";
import * as React from "react";

@inject('coinFormStore')
@observer
export class StopPriceAnd20 extends React.Component<any> {

    public handleChange = e => {
        const {coinFormStore} = this.props;
        let value = e.target.value;
        const regex = /(\d.*([.,])?(\d.+)?)?/

        if (!(regex.test(value))) {
            return;
        }

        value = value.replace(",", ".")
        coinFormStore.stopPrice = value;
    };

    public render() {
        const {coinFormStore, minus} = this.props;

        return (<div className="form-group">
            <label htmlFor="limitPrice">Stop Price</label>

            {minus ?
                <span className="badge badge-danger pull-right btn btn-default"
                      onClick={() => this.setStopPrice(-5)}>-5%</span>
                : <span>
                    <span className="badge badge-danger pull-right btn btn-default"
                          onClick={() => this.setStopPrice(20)}>+20%</span>
                    <span className="badge badge-danger m-r-xs pull-right btn btn-default"
                          onClick={() => this.setStopPrice(10)}>+10%</span>
                    <span className="badge badge-danger m-r-xs pull-right btn btn-default"
                          onClick={() => this.setStopPrice(5)}>+5%</span>
                </span>
            }

            <input type="number"
                   className="form-control"
                   id="stopPrice"
                   onChange={this.handleChange}
                   value={coinFormStore.stopPrice}/>
        </div>);
    }

    @action
    public setStopPrice = mult => {
        const {coinFormStore} = this.props;
        const coinPrice: any = path(['parentData', 'exchange', 'dailyStats', 'lastPrice'])(this.props);

        coinFormStore.stopPrice = coinFormStore.getFixedPrice(coinPrice * (mult / 100 + 1))
    };
}