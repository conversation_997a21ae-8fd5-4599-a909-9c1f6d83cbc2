/**
 * Created by dima on 11.08.2017.
 */

import PriceCalculation from "@/utils/PriceCalculation";
import {inject, observer} from 'mobx-react';

import {path} from 'ramda';
import * as React from "react";
import {graphql} from 'react-apollo';
import {IndicatorsForSymbolQueryGraphql} from "@/graphql/indicators/IndicatorsForSymbolQuery.graphql";
import {observable} from "mobx";
import {Button} from "react-bootstrap";

// @ts-ignore
@graphql(IndicatorsForSymbolQueryGraphql, {
    options: props => ({
        fetchPolicy: 'network-only',
        variables: {
            symbol: props.symbol,
        },
    }),
})

@inject("exchangeStore")
@observer
export class Indicators extends React.Component<any> {
    public static defaultProps = {
        symbol: '',
    };

    @observable public interval = "120"

    componentDidMount() {
        this.interval = localStorage.getItem('coin.indicator.interval') || this.interval;
    }

    public renderIndicators() {
        const indicators: any = path(['data', 'bot', 'getIndicators'])(this.props) || [];
        const {exchangeInfo} = this.props.exchangeStore

        if (this.props.loading) {
            return <div className="loader">
                <div className="loader-spin"/>
            </div>
        }

        const renderIndicators = indicators
            .filter(x => x.interval === this.interval)
            .map(indicator => {
                const price = indicator.price ? PriceCalculation.getFixedPrice(indicator.price, exchangeInfo, this.props.symbol) : null

                return <tr key={indicator.id}>
                    <td>
                        {indicator.indicator}
                    </td>
                    <td>
                        {indicator.interval}
                    </td>
                    <td>
                    <span className={indicator.action === 'long'
                        ? 'text-success'
                        : 'text-danger'}>{indicator.action}</span>
                    </td>
                    <td>{price}</td>
                    <td>
                    </td>
                </tr>
            })

        return (
            <table className="table table-condensed small">
                <thead>
                <tr>
                    <th>
                        Indicator
                    </th>
                    <th>
                        Interval
                    </th>
                    <th>
                        Advice
                    </th>
                    <th>
                        Price
                    </th>
                </tr>
                </thead>
                <tbody>
                {renderIndicators}
                </tbody>
            </table>
        );
    }

    public render() {
        const toIntervalClass = (interval) => {
            return this.interval == interval ? "badge-success" : ""
        }

        return (
            <div className="panel panel-filled panel-c-warning">
                <div className="panel-heading">
                    Indicators

                    <Button className={"pull-right m-r-xs btn-xs badge " + toIntervalClass("1d")}
                            onClick={() => this.setChart('1d')}>
                        <small>1d</small>
                    </Button>

                    <Button className={"pull-right m-r-xs btn-xs badge " + toIntervalClass("240")}
                            onClick={() => this.setChart('240')}>
                        <small>4h</small>
                    </Button>

                    <Button className={"pull-right m-r-xs btn-xs badge " + toIntervalClass("120")}
                            onClick={() => this.setChart('120')}>
                        <small>2h</small>
                    </Button>

                    <Button className={"pull-right m-r-xs btn-xs badge " + toIntervalClass("60")}
                            onClick={() => this.setChart('60')}>
                        <small>1h</small>
                    </Button>

                    <Button className={"pull-right m-r-xs btn-xs badge " + toIntervalClass("15")}
                            onClick={() => this.setChart('15')}>
                        <small>15m</small>
                    </Button>
                </div>

                <div className="panel-body" style={{overflowX: 'auto'}}>
                    {this.renderIndicators()}
                </div>
            </div>
        );
    }

    private setChart = interval => {
        this.interval = interval
        localStorage.setItem('coin.indicator.interval', interval);
    };
}


