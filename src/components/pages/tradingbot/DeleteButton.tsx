import * as React from "react";
import {graphql} from "react-apollo";
import {BotDeleteMutation} from "@/graphql/bot/BotDeleteMutation.graphql";
import {MarketBots} from "@/graphql/bot/MarketBots.graphql";
import {error, success} from "@/utils/Toast";

// @ts-ignore
@graphql(BotDeleteMutation)
export class DeleteButton extends React.Component<any> {
    public onDeleteBot = async () => {
        const {bot} = this.props;
        const result = await this.props.mutate({
            variables: {
                id: bot.id,
            },
            refetchQueries: [
                {
                    query: MarketBots,
                }],
        });

        const data = result.data.deleteBotMutation;

        if (data.ok) {
            success(`Bot ${bot.symbol} removed`);
        } else {
            error(data.error);
        }
    };

    public render() {
        return (<button className="btn btn-danger label label-default m-r-sm tradebot-btn-delete"
                        onClick={this.onDeleteBot}>Delete</button>);
    }
}
