import {graphql, Mutation} from 'react-apollo';
import {<PERSON>} from 'react-router-dom';
import * as React from "react";
import {BotStartStopMutation} from "@/graphql/bot/BotStartStopMutation.graphql";
import {DeleteButton} from "@/components/pages/tradingbot/DeleteButton";
import PriceCalculation from "@/utils/PriceCalculation";
import {MarketBots} from "@/graphql/bot/MarketBots.graphql";
import gql from "graphql-tag";
import {error, success, warning} from "@/utils/Toast";

const ToAutoBotMutation = gql`
    mutation ToAutoBotMutation($id: String!) {
        transformToAutoBotMutation(id: $id) {
            ok
        }
    }
`;

// @ts-ignore
@graphql(BotStartStopMutation)
export class TradingBotElement extends React.Component<any> {

    public onBotToggle = async () => {
        const {bot} = this.props;

        const result = await this.props.mutate({
            variables: {
                id: bot.id,
                action: bot.active ? 'STOP' : 'START',
            },
            refetchQueries: [
                {
                    query: MarketBots,
                }],
        });

        const data = result.data.startStopBotMutation;

        if (data.ok) {
            if (!bot.active) {
                success(`Bot ${bot.symbol} started`);
            } else {
                warning(`Bot ${bot.symbol} stopped`);
            }
        } else {
            error(data.error);
        }
    };

    public addAutoBot = async (mutation, bot) => {
        const result = await mutation({
            variables: {
                id: bot.id
            },
            refetchQueries: [
                {
                    query: MarketBots,
                }],
        });

        const data = result.data.transformToAutoBotMutation;

        if (data.ok) {
            success(`Bot ${bot.symbol} transformed`);
        } else {
            error(data.error);
        }
    };

    public render() {
        const {bot} = this.props;
        return (<div className="col-lg-3 col-md-4 col-sm-6 col-xs-12 p-lr-none">
            <div className="">
                <div className="panel-body">
                    <div className="panel panel-filled">
                        <div className="panel-body">
                            <h2 className="m-b-none text-accent">

                                <Link
                                    to={{
                                        state: {
                                            bot,
                                        },
                                        pathname: `/tradingbot/advices/${bot.id}`,
                                    }}>{bot.symbol}</Link>

                                <button className="btn btn-rounded btn-default pull-right"
                                        onClick={this.onBotToggle}>{bot.active
                                    ? 'Stop'
                                    : 'Start'}</button>

                            </h2>

                            {this.renderState(bot)}

                            <table>
                                <tbody>
                                <tr>
                                    <td>
                                        <div className="slight m-t-sm m-r-sm">
                                            <i className="fa fa-road min-width-16"/> Strategy:
                                        </div>
                                    </td>
                                    <td>
                                        <div className="slight m-t-sm">
                                            <span className="c-white time"><strong>{bot.strategy}</strong></span>
                                        </div>
                                    </td>
                                </tr>

                                <tr>
                                    <td>
                                        <div className="slight m-t-xs m-r-sm">
                                            <i className="fa fa-line-chart min-width-16"/> Invested:
                                        </div>
                                    </td>
                                    <td>
                                        <div className="slight m-t-xs">
                        <span className="c-white"><strong>{bot.amountUsdtInvested
                            ? `${Number(bot.amountUsdtInvested)
                                .toFixed(2)} USDT`
                            : 0}</strong></span>
                                        </div>
                                    </td>
                                </tr>

                                <tr>
                                    <td>
                                        <div className="slight m-t-xs m-r-sm">
                                            <i className="fa fa-dollar min-width-16"/> Profit:
                                        </div>
                                    </td>
                                    <td>
                                        <div className="slight m-t-xs">
                        <span
                            className={PriceCalculation.toClassName(bot.profit)}><strong>{bot.profit
                            ? `${Number(bot.profit)
                                .toFixed(2)}%`
                            : 0}</strong></span>
                                        </div>
                                    </td>
                                </tr>

                                <tr>
                                    <td>
                                        <div className="slight m-t-xs m-r-sm">
                                            <i className="fa fa-clock-o min-width-16"/> Runtime:
                                        </div>
                                    </td>
                                    <td>
                                        <div className="slight m-t-xs">
                        <span
                            className="c-white time text-success"><strong>{this.renderRuntime(
                            bot,
                        )}</strong></span>
                                        </div>
                                    </td>
                                </tr>

                                <tr>
                                    <td>
                                        <div className="slight m-t-xs m-r-sm">
                                            <i className="fa fa-thumbs-o-up min-width-16"/> Trades:
                                        </div>
                                    </td>
                                    <td>
                      <span className="slight m-t-xs">
                        <span className="c-white time"><strong>{bot.trades.length}</strong></span>
                      </span>

                                    </td>
                                </tr>
                                <tr>
                                    <td className="text-right" colSpan={2}>
                                        <Mutation mutation={ToAutoBotMutation}>
                                            {mutation => <span className="btn btn-success label label-default m-r-sm"
                                                               onClick={() => this.addAutoBot(mutation, bot)}>+autotrader</span>
                                            }
                                        </Mutation>
                                        <DeleteButton bot={bot}/>
                                    </td>
                                </tr>
                                </tbody>
                            </table>

                        </div>
                    </div>
                </div>
            </div>
        </div>);
    }

    public renderState(bot) {
        const simulation = bot.simulation ? 'simulation | ' : null;
        if (bot.active) {
            return <div className="small text-success">{simulation}active</div>;
        }
        return <div className="small">{simulation}stopped</div>;
    }

    public renderRuntime(bot) {
        let duration = '';
        let hours = 0;
        let minutes = 0;

        if (!bot.active && bot.stopDate) {
            duration = ((new Date(bot.stopDate).getTime() - new Date(bot.startDate).getTime()) / 1000 /
                60).toFixed(0);
        } else if (bot.active && bot.startDate) {
            duration = ((new Date().getTime() - new Date(bot.startDate).getTime()) / 1000 / 60).toFixed(
                0,
            );
        }

        hours = Math.floor(duration / 60);
        minutes = (duration % 60);

        return `${hours} hours, ${minutes} minutes`;
    }
}

