import {graphql} from 'react-apollo';
import {path} from 'ramda';
import {Crosshair, FlexibleWidthXYPlot, LineSeries, VerticalBarSeries, XAxis, YAxis,} from 'react-vis';
import * as React from "react";
import {observable} from "mobx";
import {BotProfitPerMonth} from "@/graphql/bot/BotProfitPerMonth.graphql";
import {observer} from "mobx-react";

// @ts-ignore
@graphql(BotProfitPerMonth, {
    options: props => ({
        variables: {
            months: 12,
            strategy: props.strategy,
            interval: props.interval,
        },
    }),
})
@observer
export class TradingBotOverviewMonthlyChart extends React.Component<any> {
    @observable public crosshairValues = [];

    public render() {
        return (<div>
            {this.renderVerticalBars()}
        </div>);
    }

    private renderVerticalBars() {
        const profits: any = this.getLineSeries() ? this.getLineSeries() : [];
        if (profits.length <= 0) {
            return 'Not enough data to display';
        }

        return <FlexibleWidthXYPlot
            onMouseLeave={this.onMouseLeave}
            animation
            height={200}
            xType="time">

            <XAxis title="Day"
                   tickFormat={time => time.getDate() + '.' + (time.getMonth() + 1)}/>
            <YAxis title="Profit in %"/>
            <VerticalBarSeries data={profits} onNearestX={this.onNearestBarX}/>
            <LineSeries curve="curveMonotoneX"
                        opacity={0.5}
                        data={profits}/>
            <Crosshair values={this.crosshairValues}
                       titleFormat={this.formatCrosshairTitle}
                       itemsFormat={this.formatCrosshairPriceItems}/>
        </FlexibleWidthXYPlot>
    }

    private formatCrosshairTitle = values => ({
        title: 'Date',
        value: new Date(values[0].x).toLocaleString("de-DE")
    });

    private formatCrosshairPriceItems = values => {
        return values.map((_v, _i) => {
            return {
                title: "Profit",
                value: values[0].y
            }
        })
    };
    private onNearestBarX = (_value, {index}) => {
        const profits: any = this.getLineSeries();
        this.crosshairValues = [profits[index]]
    };

    private onMouseLeave = () => {
        this.crosshairValues = [];
    };

    private getLineSeries() {
        const dailyProfits: any = path(["data", "bot", "profitPerMonth", "monthlyProfits"])(this.props) || []

        return dailyProfits.map(n => ({
            x: new Date(n.month).valueOf(),
            y: Math.floor(n.profit),
        }))
    }
}
