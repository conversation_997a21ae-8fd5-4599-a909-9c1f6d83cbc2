import {observable} from 'mobx';
import * as React from "react";
import {
    TradingBotOverviewMonthlyChart
} from "@/components/pages/tradingbot/overview/charts/TradingBotOverviewMonthlyChart";
import {MPanel} from "@/components/common/MPanel";
import {TradingBotOverviewDailyBars} from "@/components/pages/tradingbot/overview/charts/TradingBotOverviewDailyBars";
import {graphql} from "react-apollo";
import {StrategyProfits} from "@/graphql/bot/StrategyProfits.graphql";
import PriceCalculation from "@/utils/PriceCalculation";
import {path} from "ramda";
import {observer} from "mobx-react";

// @ts-ignore
@graphql(StrategyProfits, {
    options: props => ({
        variables: {
            profitProgress: false,
            days: 30,
        },
    }),
})
@observer
export class TradingBotOverviewStatistics extends React.Component<any> {

    @observable public selectedChart = 'dailyBars';
    @observable public selectedStrategy = 'ao-cross';
    @observable public selectedInterval = '1d'

    public onChangeChart = e => {
        this.selectedChart = e.target.value;
    };

    public render() {

        return (
            <div className="row">
                <div className="col-lg-4 col-xl-6 col-sm-12 col-xs-12">
                    <MPanel filled>
                        <div className="panel-heading">
                            Trading Bot Statistics
                            <span className="pull-right">
                                <select className="form-control input-sm input-xs"
                                        value={this.selectedChart}
                                        onChange={this.onChangeChart}>
                                  <option value="dailyBars">Daily Bars</option>
                                  <option value="monthly">Monthly</option>
                                </select>
                          </span>
                        </div>

                        <div className="panel-body" style={{overflowX: 'auto'}}>
                            <table className="table table-condensed small">
                                <thead>
                                <tr>
                                    <th>Strategies</th>
                                    <th>Profit Total</th>
                                </tr>
                                </thead>
                                <tbody>
                                {this.renderStrategies()}
                                </tbody>
                            </table>
                        </div>
                    </MPanel>
                </div>

                <div className="col-lg-8 col-xl-6 col-sm-12 col-xs-12">
                    <MPanel filled>
                        <div className="panel-body">
                            {this.renderChart()}
                        </div>
                    </MPanel>
                </div>
            </div>)
    }

    private renderStrategies() {
        const profits: any = path(['bot', 'strategyProfits'])(this.props.data) || [];

        return profits.sort((a, b) => (Number(b.totalProfit) + Number(b.currentDiff)) - (Number(a.totalProfit) + Number(a.currentDiff)))
            .map(x =>
                <tr key={x.strategy + x.interval} onClick={() => this.selectStrategy(x.strategy, x.interval)}>
                    <td>
                        <a href={"#"}>{x.strategy} {x.interval}</a>
                    </td>
                    <td>
                    <span className={'time ' + PriceCalculation.toClassName(Number(x.totalProfit))}>
                       {Number(x.totalProfit).toFixed(0)}%
                    </span>
                        {x.currentDiff ?
                            <span> ({(Number(x.totalProfit) + Number(x.currentDiff)).toFixed(0)}% now)</span>
                            : null}
                    </td>
                </tr>)
    }

    private selectStrategy = (strategy: string, interval: string) => {
        this.selectedStrategy = strategy
        this.selectedInterval = interval
    }

    private renderChart() {
        const {selectedChart} = this;

        switch (selectedChart) {
            case 'monthly':
                return <TradingBotOverviewMonthlyChart strategy={this.selectedStrategy}
                                                       interval={this.selectedInterval}/>
            case 'dailyBars':
                return <TradingBotOverviewDailyBars strategy={this.selectedStrategy} interval={this.selectedInterval}/>
        }
    }
}
