import {observer, Provider} from 'mobx-react';
import * as React from "react";
import {PageHeaderWithLinks} from "@/components/common/PageHeaderWithLinks";
import {TradingBotOverviewStatistics} from "@/components/pages/tradingbot/overview/TradingBotOverviewStatistics";
import {TradingBotOverviewManualBots} from "@/components/pages/tradingbot/overview/TradingBotOverviewManualBots";
import {TradingBotOverviewStore} from "@/components/pages/tradingbot/overview/TradingBotOverviewStore";
import {Col, Row} from "react-bootstrap";
// import {TradingBotOverviewTopCoins} from "@/components/pages/tradingbot/overview/TradingBotOverviewTopCoins";

@observer
export class TradingBotOverviewPage extends React.Component<any> {
    public render() {
        return (<Provider overviewStore={new TradingBotOverviewStore()}>
            <div className="container-fluid">

                <PageHeaderWithLinks links={[
                    {
                        title: 'Overview',
                        url: '/tradingbot',
                        active: true,
                    }, {
                        title: 'Last Signals',
                        url: '/tradingbot/signals',
                    }, {
                        title: 'Market Signals',
                        url: '/tradingbot/signalMarkets',
                    }]}/>

                <Row>
                    <Col lg={12} md={12}>
                        <TradingBotOverviewStatistics/>
                    </Col>
                </Row>
                <Row>
                    <Col lg={12} md={12}>
                        <TradingBotOverviewManualBots/>
                    </Col>
                </Row>
            </div>
        </Provider>);
    }
}
