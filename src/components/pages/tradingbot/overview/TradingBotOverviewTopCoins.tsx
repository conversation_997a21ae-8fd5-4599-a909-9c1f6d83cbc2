import {graphql} from 'react-apollo';
import {path} from 'ramda';
import {inject, observer} from 'mobx-react';
import * as React from "react";
import {MPanel} from "@/components/common/MPanel";
import gql from "graphql-tag";
import {Col, Row} from "react-bootstrap";

const TopCoins = gql`query {
    bot {
        topCoins {
            symbol
            profit
            strategy
        }
    }
}`

// @ts-ignore
@graphql(TopCoins, {})
@inject('overviewStore')
@observer
export class TradingBotOverviewTopCoins extends React.Component<any> {
    public render() {
        const result:any = path(['bot', 'topCoins'])(this.props.data) || [];

        return (
            <MPanel className="panel-c-primary" filled>
                <div className="panel-heading">
                    Top 10 Coins (Nesterov 15)
                </div>

                <div className="panel-body">
                    <Row>
                        <Col lg={12}>
                            <table className="table table-condensed small">
                                <thead>
                                <tr>
                                    <th>
                                        Symbol
                                    </th>
                                    <th>
                                        Profit
                                    </th>
                                </tr>
                                </thead>
                                <tbody>
                                {result.map(this.renderCoinRow)}
                                </tbody>
                            </table>
                        </Col>
                    </Row>
                </div>
            </MPanel>
        );
    }

    renderCoinRow(entry) {
        return <tr key={entry.symbol}>
            <td><b>{entry.symbol}</b></td>
            <td>{Number(entry.profit).toFixed(2)}%</td>
        </tr>
    }
}
