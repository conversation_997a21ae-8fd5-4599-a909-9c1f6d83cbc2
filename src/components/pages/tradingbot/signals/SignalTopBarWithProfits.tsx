import * as React from "react";
import {MPanel} from "@/components/common/MPanel";
import {path} from "ramda";
import {BotSignalsProfit} from "@/graphql/bot/BotSignalsProfit.graphql";
import {graphql} from "react-apollo";
import PriceCalculation from "@/utils/PriceCalculation";

// @ts-ignore
@graphql(BotSignalsProfit, {
    options: props => ({
        variables: {
            strategy: props.strategy,
            interval: props.interval,
            symbol: props.symbol
        },
    }),
})
export class SignalTopBarWithProfits extends React.Component<any> {
    public render() {
        const botProfit: any = path(['bot', 'profit'])(this.props.data) || {};

        return <div className="row">
            <div className="col-lg-12">
                <MPanel filled>
                    <div className="panel-heading" style={{borderBottom: 'none'}}>
                        <small>
                            <span
                                className={PriceCalculation.toClassName(botProfit.profit) + "  m-r-lg"}>Profit: {Number(botProfit.profit).toFixed(2)}%</span>
                            <span
                                className={"m-r-lg"}>Pos: {Number(botProfit.tradesPos)}</span>
                            <span
                                className={""}>Neg: {Number(botProfit.tradesNeg)}</span>
                        </small>
                    </div>
                </MPanel>
            </div>
        </div>
    }
}
