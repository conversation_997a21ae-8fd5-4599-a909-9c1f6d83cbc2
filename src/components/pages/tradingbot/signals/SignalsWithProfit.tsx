import PriceCalculation from "@/utils/PriceCalculation";
import {inject} from "mobx-react";
import * as React from "react";

@inject("exchangeStore")
export class SignalsWithProfit extends React.Component<any> {
    public render() {
        const {signals, exchangeStore} = this.props;

        if (signals.length <= 0) {
            return (<tr>
                <td>Not enough data to display</td>
            </tr>);
        }

        return signals.map(signal => {
            const signalProfit = this.getSignalProfit(signal);
            const price = PriceCalculation.getFixedPrice(signal.price, exchangeStore.exchangeInfo, signal.symbol)
            return <tr key={signal.id}>
                <td>{signal.symbol}</td>
                <td>
            <span className={signal.advice === 'long'
                ? 'text-success'
                : 'text-danger'}>{signal.advice}</span>
                </td>
                <td className={PriceCalculation.toClassName(signalProfit)}>
                    {signalProfit ? signalProfit + '%' : ''}</td>
                <td>{price}</td>
                <td>{new Date(signal.timestamp).toLocaleString()}</td>
            </tr>;
        });
    }

    public getSignalProfit(signal) {

        if (signal.profit && signal.advice === 'short') {
            return signal.profit;
        }

        // const currentPrices = path(['exchange', 'prices'])(this.props.data) || [];
        // const currentPriceObj = find(propEq('asset', signal.symbol))(currentPrices) || {};
        //
        // const price = currentPriceObj.value || 0;
        //
        // return PriceCalculation.toPercent(signal.price, price);
    }
}


