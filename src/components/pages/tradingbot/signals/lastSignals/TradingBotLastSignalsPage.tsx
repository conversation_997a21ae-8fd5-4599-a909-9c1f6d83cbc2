import {observer} from 'mobx-react';
import {observable} from 'mobx';
import * as React from "react";
import {PageHeaderWithLinks} from "@/components/common/PageHeaderWithLinks";
import {MPanel} from "@/components/common/MPanel";
import {ToggleNotificationButton} from "@/components/pages/tradingbot/signals/ToggleNotificationButton";
import {SignalTopBarWithProfits} from "@/components/pages/tradingbot/signals/SignalTopBarWithProfits";
import {LastSignals} from "@/components/pages/tradingbot/signals/lastSignals/LastSignals";

@observer
export class TradingBotLastSignalsPage extends React.Component {

    @observable public searchText = '';
    @observable public strategy;
    @observable public interval;
    @observable public botProfit = {};

    constructor(props) {
        super(props);

        this.strategy = localStorage.getItem('signalStrategy') || 'ao-cross';
        this.interval = localStorage.getItem('signalInterval') || '1d';
    }

    public onSearch = e => {
        this.searchText = e.target.value;
    };

    public onSelectChange = e => {
        this.strategy = e.target.value;
        localStorage.setItem('signalStrategy', e.target.value);
    };

    public onIntervalChange = e => {
        this.interval = e.target.value
        localStorage.setItem('signalInterval', e.target.value);
    }

    public onSignalsFetch = botProfit => {
        this.botProfit = botProfit;
    };

    public render() {
        return (<div className="container-fluid">

                <PageHeaderWithLinks
                    links={[
                        {
                            title: 'Overview',
                            url: '/tradingbot',
                        }, {
                            title: 'Last Signals',
                            active: true,
                        }, {
                            title: 'Market Signals',
                            url: '/tradingbot/signalMarkets',
                        }]}/>

                <SignalTopBarWithProfits strategy={this.strategy} interval={this.interval}/>

                <div className="row">
                    <div className="col-lg-12">
                        <MPanel filled>
                            <div className="panel-heading">
                                Signals

                                <span className="trade-signal-search">

                  <span className="m-r-sm">
                    <ToggleNotificationButton strategy={this.strategy}/>
                  </span>

                <input type="text"
                       placeholder="Search..."
                       className="input-sm form-control m-r-xs"
                       value={this.searchText}
                       name="searchText"
                       maxLength={10}
                       onChange={this.onSearch}/>

                <select className="form-control input-sm m-r-xs"
                        value={this.strategy}
                        onChange={this.onSelectChange}>
                  <option value="ao-cross">AO Cross</option>
                  <option value="ao-swing">AO Swing</option>
                  <option value="ao-cross-swing">AO Cross Swing</option>
                  <option value="ao-cross-btc-cross">AO Cross + BTC Cross</option>
                  <option value="ao-swing-btc-cross">AO Swing + BTC Cross</option>
                  <option value="ao-cross-swing-btc-cross">AO Cross Swing + BTC Cross</option>
                  <option value="ao-cross-btc-swing">AO Cross + BTC Swing</option>
                  <option value="ao-swing-btc-swing">AO Swing + BTC Swing</option>
                  <option value="ao-cross-swing-btc-swing">AO Cross Swing + BTC Swing</option>
                  <option value="btc-cross">BTC Cross</option>
                  <option value="btc-swing">BTC Swing</option>
                  <option value="btc-cross-swing">BTC Cross Swing</option>
                  <option value="ema+adx">EMA+ADX</option>
                  <option value="ema25+ema50">EMA25+EMA50</option>
                  <option value="ema50+ema100">EMA50+EMA100</option>
                  <option value="ema100+ema200">EMA100+EMA200</option>
                  <option value="nesterov">Nesterov</option>
                </select>

               <select className="form-control input-sm"
                       value={this.interval}
                       onChange={this.onIntervalChange}>
                  <option value="15">15m</option>
                  <option value="60">1h</option>
                  <option value="120">2h</option>
                  <option value="240">4h</option>
                  <option value="1d">1d</option>
                </select>
              </span>
                            </div>
                            <div className="panel-body">
                                <table className="table table-condensed small">
                                    <thead>
                                    <tr>
                                        <th>
                                            Symbol
                                        </th>
                                        <th>
                                            Advice
                                        </th>
                                        <th>
                                            Gain
                                        </th>
                                        <th>
                                            Price $
                                        </th>
                                        <th>
                                            Time
                                        </th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <LastSignals symbol={this.searchText}
                                                 strategy={this.strategy}
                                                 interval={this.interval}
                                                 limit={50}
                                                 onSignalsFetch={this.onSignalsFetch}/>
                                    </tbody>
                                </table>
                            </div>
                        </MPanel>
                    </div>
                </div>
            </div>
        );
    }
}
