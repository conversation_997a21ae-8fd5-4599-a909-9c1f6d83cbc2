import * as React from "react";
import {MPanel} from "@/components/common/MPanel";
import {sortWith, ascend, prop, clone, reverse} from "ramda";
import PriceCalculation from "@/utils/PriceCalculation";

export class Roundtrips extends React.Component<any> {
    render(): React.ReactNode {
        const {signals} = this.props;

        return <MPanel filled className="panel-c-success">
            <div className="panel-heading">
                Roundtrips
            </div>

            <div className="panel-body">
                {this.renderRoundtrips(signals)}
            </div>
        </MPanel>
    }

    public renderRoundtrips(signals) {
        let _signals = clone(signals);

        if (_signals.length <= 0) {
            return 'Not enough data to display';
        }

        _signals = _signals.map(x => ({...x, timestamp: new Date(x.timestamp)}));
        _signals = sortWith([ascend(prop('timestamp'))])(_signals);

        // Build pairs
        const pairs = [];
        let currentLong = null;

        for (let i = 0; i < _signals.length; i++) {
            const signal = _signals[i];
            const {advice} = signal;

            switch (advice) {
                case 'short':
                    if (currentLong !== null) {
                        pairs.push({
                            long: currentLong,
                            short: signal,
                            id: Math.random()
                                .toString(),
                        });
                        currentLong = null;
                    }

                    break;
                case 'long':
                    currentLong = signal;
                    break;
            }
        }

        if (pairs.length <= 0) {
            return 'Not enough data to display';
        }

        return (<table className="table small">
            <thead>
            <tr>
                <th>Entry</th>
                <th>Exit</th>
                <th>Duration</th>
                <th>Profit</th>
            </tr>
            </thead>

            <tbody>

            {reverse(pairs).map(pair => {
                const startDate = pair.long.timestamp;
                const endDate = pair.short.timestamp;

                const duration = Number(((endDate.getTime() -
                    startDate.getTime()) / 1000 /
                    60).toFixed(
                    0,
                ));


                const hours = Math.floor(duration / 60);
                const minutes = (duration % 60);
                const percents = PriceCalculation.toPercent(pair.long.price, pair.short.price);
                const className = PriceCalculation.toClassName(percents);

                return (<tr key={pair.id}>
                    <td>{startDate.toLocaleString()}</td>
                    <td>{endDate.toLocaleString()}</td>
                    <td>{hours}h {minutes}m</td>
                    <td><span className={className}>{`${percents}%`}</span></td>
                </tr>);
            },)}

            </tbody>
        </table>);
    }
}