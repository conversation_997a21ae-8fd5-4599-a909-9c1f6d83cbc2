import {observer} from 'mobx-react';
import {observable} from 'mobx';
import * as React from "react";
import {SignalTable} from "@/components/pages/tradingbot/signals/SignalTable";
import {PageHeaderWithLinks} from "@/components/common/PageHeaderWithLinks";

@observer
export class TradingBotSignalMarketsPage extends React.Component<any> {

    @observable public searchText = '';
    @observable public strategy = 'nesterov';
    @observable public interval;

    constructor(props) {
        super(props);

        this.strategy = localStorage.getItem('signalStrategy') || 'nesterov';
        this.interval = localStorage.getItem('signalInterval') || '1d';
    }

    public onSearch = e => {
        this.searchText = e.target.value;
    };

    public onSelectChange = e => {
        this.strategy = e.target.value;
        localStorage.setItem('signalStrategy', e.target.value);
    };

    public onIntervalChange = e => {
        this.interval = e.target.value
        localStorage.setItem('signalInterval', e.target.value);
    }

    public render() {
        return (<div className="container-fluid">

                <PageHeaderWithLinks
                    links={[
                        {
                            title: 'Overview',
                            url: '/tradingbot',
                        }, {
                            title: 'Last Signals',
                            url: '/tradingbot/signals',
                        }, {
                            title: 'Market Signals',
                            active: true
                        }]}/>

                <div className="row">
                    <div className="col-lg-12">
                        <div className="pull-right m-b-xs">
              <span className="trade-signal-search trade-signal-search-market m-r-xs">
                <input type="text"
                       placeholder="Search..."
                       className="input-sm form-control"
                       value={this.searchText}
                       name="searchText"
                       maxLength={10}
                       onChange={this.onSearch}/>
              </span>

                            <span className="trade-signal-search trade-signal-search-market">
                <select className="form-control input-sm m-r-xs"
                        value={this.strategy}
                        onChange={this.onSelectChange}>
                  <option value="ao-cross">AO Cross</option>
                  <option value="ao-swing">AO Swing</option>
                  <option value="ao-cross-swing">AO Cross Swing</option>
                  <option value="ao-cross-btc-cross">AO Cross + BTC Cross</option>
                  <option value="ao-swing-btc-cross">AO Swing + BTC Cross</option>
                  <option value="ao-cross-swing-btc-cross">AO Cross Swing + BTC Cross</option>
                  <option value="ao-cross-btc-swing">AO Cross + BTC Swing</option>
                  <option value="ao-swing-btc-swing">AO Swing + BTC Swing</option>
                  <option value="ao-cross-swing-btc-swing">AO Cross Swing + BTC Swing</option>
                  <option value="btc-cross">BTC Cross</option>
                  <option value="btc-swing">BTC Swing</option>
                  <option value="btc-cross-swing">BTC Cross Swing</option>
                  <option value="ema+adx">EMA+ADX</option>
                  <option value="ema25+ema50">EMA25+EMA50</option>
                  <option value="ema50+ema100">EMA50+EMA100</option>
                  <option value="ema100+ema200">EMA100+EMA200</option>
                  <option value="nesterov">Nesterov</option>
                </select>
                                         <select className="form-control input-sm"
                                                 value={this.interval}
                                                 onChange={this.onIntervalChange}>
                  <option value="15">15m</option>
                  <option value="60">1h</option>
                  <option value="120">2h</option>
                  <option value="240">4h</option>
                  <option value="1d">1d</option>
                </select>
              </span>
                        </div>
                    </div>
                </div>

                <SignalTable symbol={this.searchText}
                             interval={this.interval}
                             strategy={this.strategy}/>
            </div>
        );
    }
}

