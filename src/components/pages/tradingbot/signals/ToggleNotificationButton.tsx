import * as React from "react";
import {graphql} from "react-apollo";
import {AccountSettingsSignalNotificationMutation} from "@/graphql/settings/AccountSettingsSignalNotificationMutation.graphql";
import {success, warning} from "@/utils/Toast";

// @ts-ignore
@graphql(AccountSettingsSignalNotificationMutation)
export class ToggleNotificationButton extends React.Component<any> {
    public onToggleNotification = async _e => {
        const result = await this.props.mutate({
            variables: {
                strategy: this.props.strategy,
            },
        });

        if (result.data.accountSettingsNotificationSignalMutation.notifications.signals[this.props.strategy]) {
            success('Notification activated');
        } else {
            warning('Notification deactivated');
        }
    };

    public render() {
        return (
            <i className="fa fa-envelope btn-link i-link"
               onClick={this.onToggleNotification}/>);
    }
}
