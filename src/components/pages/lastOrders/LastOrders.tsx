import * as React from "react";
import {path} from "ramda";
import PriceCalculation from "@/utils/PriceCalculation";
import {Link} from "react-router-dom";
import {compose, graphql} from "react-apollo";
import {inject, observer} from "mobx-react";
import {LastOrdersQuery} from "@/graphql/binance/LastOrdersQuery.graphql";


@compose(
    graphql(LastOrdersQuery, {
        options: props => ({
            variables: {
                limit: props.limit,
            },
            pollInterval: 15000,
        }),
    }),
)
@inject("exchangeStore")
@observer
export class LastOrders extends React.Component<any, any> {
    public render() {
        return <table className="table table-condensed small">
            <thead>
            <tr>
                <th>Symbol</th>
                <th>Order Date</th>
                <th>Type</th>
                <th>Price</th>
                <th className="hidden-xs">Status</th>
                <th>Side</th>
                <th>Total $</th>
            </tr>
            </thead>
            <tbody>
            {this.getOrders()}
            </tbody>
        </table>
    }

    public getOrders() {
        const {exchangeStore} = this.props
        const orders = [];
        const lastOrders: any = path(['exchange', 'lastOrders'])(this.props.data) || [];

        if (lastOrders.length === 0) {
            return (<tr>
                <td colSpan={5}>
                    No opened orders
                </td>
            </tr>);
        }

        lastOrders.forEach(order => {
            const symbol = order.symbol

            const executedQty = PriceCalculation.getFixedAmount(order.executedQty, exchangeStore.exchangeInfo, symbol)
            const origQty = PriceCalculation.getFixedAmount(order.origQty, exchangeStore.exchangeInfo, symbol)
            const price = PriceCalculation.getFixedPrice(Number(order.price), exchangeStore.exchangeInfo, symbol)
            const transactTime = new Date(order.transactTime).toLocaleString()
            const orderId = order.orderId
            const type = order.type
            const status = order.status
            const side = order.side
            const total = Number(price * executedQty).toFixed(2)

            orders.push(<tr key={orderId}>
                <td><Link to={'/coin/' + symbol}>{symbol}</Link></td>
                <td>{transactTime}</td>
                <td><strong>{type}</strong></td>
                <td><span className="text-accent">{price}</span></td>
                <td className="hidden-xs">{status}</td>
                <td>{side}</td>
                <td>{total}</td>
            </tr>);
        });

        return orders;
    }
}