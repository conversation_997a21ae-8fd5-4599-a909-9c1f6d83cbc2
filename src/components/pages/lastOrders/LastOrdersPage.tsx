import {inject} from "mobx-react";

import * as React from "react";
import PageHeader from "@/components/common/PageHeader";
import {LastOrders} from "@/components/pages/lastOrders/LastOrders";
import {error, success} from "@/utils/Toast";

@inject("exchangeStore")
export class LastOrdersPage extends React.Component<any> {
    /**
     *
     * @param e
     * @param order
     */
    public cancelOrder = async (e, order) => {
        this.setState({
            [order.orderId]: true,
        });

        const symbol = this.props.symbol;

        const result = await this.props.mutate({
            variables: {
                symbol,
                orderId: order.orderId,
                recvWindow: 10000,
            },
        });

        this.setState({
            [order.orderId]: false,
        });

        if (result.data.cancelOrderMutation.ok) {
            success('Order canceled');
        } else {
            error(result.error);
        }
    };

    public render() {
        return <div className="container-fluid">
            <PageHeader section="Last orders"/>

            <div className="row">
                <div className="col-xs-12 col-lg-12">
                    <div className="panel panel-filled panel-c-warning">
                        <div className="panel-heading">
                            Last Orders
                        </div>

                        <div className="panel-body" style={{overflowX: 'auto'}}>
                            <LastOrders limit={100}/>
                        </div>
                    </div>
                </div>
            </div>
        </div>;
    }
}
