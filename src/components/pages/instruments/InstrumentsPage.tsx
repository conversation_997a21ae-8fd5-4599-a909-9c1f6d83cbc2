import {inject, observer} from "mobx-react";
import {path, prop, sortBy} from 'ramda';
import {with<PERSON><PERSON><PERSON>} from 'react-apollo';

import * as React from "react";
import PageHeader from "@/components/common/PageHeader";
import {CoinIndicatorsQueryGraphql} from "@/graphql/indicators/CoinIndicatorsQuery.graphql";
import {observable} from "mobx";
import {Link} from "react-router-dom";

// @ts-ignore
@withA<PERSON>lo
@inject("exchangeStore")
@observer
export class InstrumentsPage extends React.Component<any> {

    @observable public data: any = []
    @observable public filters: any = {
        interval: '15',
    }

    public render() {
        return <div className="container-fluid">
            <PageHeader section="Last orders"/>

            <div className="row">
                <div className="col-xs-12 col-lg-12">
                    <div className="panel panel-filled panel-c-warning">
                        <div className="panel-heading">
                            Instruments

                            {this.renderFilterButton("15m", "interval", "15")}
                            {this.renderFilterButton("60m", "interval", "60")}
                            {this.renderFilterButton("120m", "interval", "120")}
                            {this.renderFilterButton("240m", "interval", "240")}
                            {this.renderFilterButton("1d", "interval", "1d")}
                            {this.renderFilterButton("AO > 0", "ao", "over")}
                            {this.renderFilterButton("Nesterov", "nesterov", "long")}
                            {this.renderFilterButton("RSI > 30 & < 80", "rsi", "range")}

                            <b>
                                <span className="m-l-sm text-muted">
                                    <span className="">{Number(this.data.length)}</span>
                                </span>
                            </b>
                        </div>

                        <div className="panel-body" style={{overflowX: 'auto'}}>
                            <table className="table table-condensed small">
                                <thead>
                                <tr>
                                    <th>Symbol</th>
                                    <th>AO</th>
                                    <th>RSI</th>
                                    <th>ADX</th>
                                </tr>
                                </thead>
                                <tbody>
                                {this.getInstruments()}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>;
    }

    private renderFilterButton(filterName: string, param: string, value: string) {

        let classes = "btn btn-success btn-xs m-r-xs pull-right"

        if (this.filters[param] === value) {
            classes += " active"
        }

        return <span className={classes}
                     onClick={() => this.onFilterClick(param, value)}>
            {filterName}
        </span>
    }

    private onFilterClick = async (key: string, value: any) => {
        if (this.filters[key]) {
            this.filters[key] = undefined;
        } else {
            this.filters[key] = value;
        }

        try {
            const result = await this.props.client.query({
                query: CoinIndicatorsQueryGraphql,
                variables: {
                    filters: this.filters
                }
            })

            this.data = path(["data", "instruments", "fetch"])(result) || []
        } catch (e) {
            console.error(e.toString());
        }
    }

    private getInstruments() {
        const result = [];
        const instruments: any = sortBy(prop("symbol"))(this.data);

        if (instruments.length === 0) {
            return (<tr>
                <td colSpan={5}>
                    No data
                </td>
            </tr>);
        }

        instruments.forEach(inst => {

            result.push(<tr key={inst.symbol}>
                <td className={"text-accent"} key={inst.symbol}>
                    <Link className="normal-text-color"
                          to={{pathname: `/coin/${this.props.symbol}`}}>{inst.symbol}</Link>
                </td>
                <td>{inst.ao ? inst.ao.toFixed(8) : null}</td>
                <td>{inst.rsi ? inst.rsi.toFixed(8) : null}</td>
                <td>{inst.adx ? inst.adx.toFixed(8) : null}</td>
            </tr>);
        });

        return result
    }
}
