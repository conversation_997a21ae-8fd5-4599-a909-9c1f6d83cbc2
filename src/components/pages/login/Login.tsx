import {printError} from "@/utils/GraphQLErrors";
import {observable} from "mobx";
import {isEmpty} from "ramda";
import * as React from "react";
import {ControlLabel, FormControl, FormGroup, Panel} from 'react-bootstrap';
import {Link} from "react-router-dom";
import {observer} from "mobx-react";
import {graphql} from "react-apollo";
import {SessionQuery} from "@/graphql/login/SessionQuery";
import {LoginMutation} from "@/graphql/login/LoginMutation.graphql";
import {error} from "@/utils/Toast";

// @ts-ignore
@graphql(LoginMutation)
@observer
export default class Login extends React.Component<any, {}> {

    @observable private username = "";
    @observable private password = "";

    public onLogin = async () => {
        const {username, password} = this;

        if (isEmpty(username) || isEmpty(password)) {
            error("Please enter valid username and password");
            return;
        }

        try {
            await this.props.mutate({
                variables: {
                    password,
                    username,
                },
                refetchQueries: [{query: SessionQuery}]
            });

            this.props.history.push("/dashboard");
        } catch (error) {
            printError(error);
        }
    };

    public onPasswordPress = async event => {
        if (event.which === 13) {
            await this.onLogin();
        }
    };

    public onChange = e => {
        const {name, value} = e.target;
        this[name] = value;
    };

    public render() {
        const {username, password} = this;

        return (
            <div className="container-center">
                {this.renderHead()}
                <Panel className="panel-filled">
                    <Panel.Body>
                        <FormGroup>
                            <ControlLabel>Username</ControlLabel>
                            <FormControl
                                type="text"
                                placeholder=""
                                title="Please enter you username"
                                value={username}
                                name="username"
                                id="username"
                                onChange={this.onChange}/>
                        </FormGroup>
                        <FormGroup>
                            <ControlLabel>Password</ControlLabel>
                            <FormControl
                                type="password"
                                title="Please enter your password"
                                placeholder=""
                                value={password}
                                name="password"
                                id="password"
                                onKeyPress={this.onPasswordPress}
                                onChange={this.onChange}/>
                        </FormGroup>
                        Forgot your password? <Link to="/resetPassword">Reset password</Link>
                        <div className="pull-right" style={{marginTop: '30px'}}>
                            <Link to="/register" className="btn btn-default m-r-sm">Register</Link>
                            <button className="btn btn-accent"
                                    onClick={this.onLogin}>Login
                            </button>
                        </div>
                    </Panel.Body>
                </Panel>
            </div>);
    }

    private renderHead() {
        return <div className="view-header">
            <div className="header-icon">
                <i className="fa fa-2x fa-unlock-alt"/>
            </div>
            <div className="header-title">
                <h3>Login</h3>
                <small>
                    Sign in to you account
                </small>
            </div>
        </div>
    }
}
