import {LogQuery} from "@/graphql/logger/LogQuery.graphql";
import {observable} from "mobx";
import {observer} from 'mobx-react';
import {path} from 'ramda';
import * as React from 'react';
import {with<PERSON><PERSON><PERSON>} from 'react-apollo';
import {Col, Row, Table} from "react-bootstrap";
import * as shortid from 'shortid';
import PageHeader from '../../common/PageHeader';
import {Link} from "react-router-dom";

// @ts-ignore
@withApollo
@observer
export class LoggerPage extends React.Component<any, any> {
    @observable public service = null
    @observable public loglevel = null
    @observable public logs = []

    private interval = null

    public async componentDidMount() {
        this.fetchLogs()

        this.interval = setInterval(() => {
            this.fetchLogs()
        }, 5000)
    }

    componentWillUnmount() {
        clearInterval(this.interval)
    }

    public render() {
        const options = ["mb-advices", "mb-bot", "mb-orders", "mb-candles", "mb-pump", "mb-server", "mb-indicators", "mb-tester"]
        const logLevels = ["log", "error", "warn"]

        return (
            <div className="container-fluid">

                <PageHeader title="Logs" section="Logs"/>

                <Row>
                    <Col lg={12} md={12}>
                        <div className="panel panel-filled">
                            <div className="panel-heading">
                                Logs

                                <span className="pull-right">
                                     <select className="form-control input-sm input-xs m-l-xs m-r-xs"
                                             value={this.loglevel}
                                             onChange={this.onLevelSelection}>
                                    {[
                                        <option key="default" value="DEFAULT">Select level</option>,
                                        ...logLevels.map(k =>
                                            (<option key={k} value={k}>{k}
                                            </option>))]
                                    }
                                    </select>
                                </span>
                                <span className="pull-right">
                                      <select className="form-control input-sm input-xs"
                                              value={this.service}
                                              onChange={this.onServiceSelection}>
                                    {[
                                        <option key="default" value="DEFAULT">Select service</option>,
                                        ...options.map(k =>
                                            (<option key={k} value={k}>{k}
                                            </option>))]
                                    }
                                      </select>
                                </span>
                            </div>

                            <div className="panel-body" style={{overflowX: 'auto'}}>
                                <Table className="table-condensed small">
                                    <thead>
                                    <tr>
                                        <th>Timestamp</th>
                                        <th>Level</th>
                                        <th>Service</th>
                                        <th>Message</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {this.getLogs()}
                                    </tbody>
                                </Table>
                            </div>
                        </div>
                    </Col>
                </Row>
            </div>);
    }

    public onServiceSelection = async e => {
        if (e.target.value != null) {
            this.service = e.target.value
            await this.fetchLogs()
        }
    }

    public onLevelSelection = async e => {
        if (e.target.value != null) {
            this.loglevel = e.target.value
            await this.fetchLogs()
        }
    }

    public getLogs = () => {
        const logs: any = this.logs
        const toRender = [];

        if (logs.length === 0) {
            return (<tr>
                <td colSpan={4}>
                    No logs
                </td>
            </tr>);
        }

        logs.forEach(log => {
            const timestamp = new Date(log.timestamp).toLocaleString()
            const msgSplitted = []
            for (const msgPart of log.msg.split(" ")) {
                if (msgPart.includes("USDT") && msgPart.length > 4) {
                    msgSplitted.push(<Link key={Math.random()} to={'/coin/' + msgPart}>{msgPart}</Link>)
                    msgSplitted.push(" ")
                } else {
                    msgSplitted.push(<span key={Math.random()}>{msgPart} </span>)
                }
            }

            let textClass = ''
            if (log.level === 'warn') {
                textClass = 'text-warning'
            } else if (log.level === 'error') {
                textClass = 'text-danger'
            }

            toRender.push(<tr key={shortid.generate()}>
                <td>{timestamp}</td>
                <td className={textClass}>{log.level}</td>
                <td>{log.service}</td>
                <td>{msgSplitted}</td>
            </tr>);
        });

        return toRender;
    };

    private fetchLogs = async () => {
        try {
            const result = await this.props.client.query({
                query: LogQuery,
                fetchPolicy: 'network-only',
                variables: {
                    service: this.service || null,
                    level: this.loglevel || null,
                    limit: 1000
                },
            })

            this.logs = path(['user', 'logs'])(result.data) as any || []
        } catch (e) {
            console.error(e)
        }
    }
}
