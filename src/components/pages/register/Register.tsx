import {RegisterMutation} from "@/graphql/login/RegisterMutation.graphql";
import {observable} from "mobx";
import {observer} from "mobx-react";
import * as React from "react";
import {graphql} from "react-apollo";
import {ControlLabel, Form, FormControl, FormGroup, Panel, Row} from "react-bootstrap";
import {Link} from "react-router-dom";
import {printError} from "@/utils/GraphQLErrors";
import {success} from "@/utils/Toast";

// @ts-ignore
@graphql(RegisterMutation)
@observer
export default class Register extends React.Component<any, any> {
    @observable private username = "";
    @observable private email = "";
    @observable private password = "";
    @observable private passwordRepeat = "";

    public onRegister = async _e => {
        const {username, email, password} = this;

        try {
            await this.props.mutate({
                variables: {
                    email,
                    password,
                    username,
                },
            });

            success("Account created. Please check your email and click on verification link.");
            this.props.history.push("/confirmation");
        } catch (error) {
            printError(error);
        }
    };

    public onChange = e => {
        const {name, value} = e.target;
        this[name] = value;
    };

    public render() {
        const {username, email, password, passwordRepeat} = this;

        return (<div>
            <div className="container-center lg">
                {this.renderHead()}

                <Panel className="panel-filled">
                    <Panel.Body>
                        <p/>
                        <Form id="registerForm">
                            <Row className="row">
                                <FormGroup className="col-lg-12">
                                    <ControlLabel>Username</ControlLabel>
                                    <FormControl
                                        type="text"
                                        value={username}
                                        id="username"
                                        className="form-control"
                                        onChange={this.onChange}
                                        name="username"/>
                                </FormGroup>
                                <FormGroup className="col-lg-6">
                                    <ControlLabel>Password</ControlLabel>
                                    <FormControl
                                        type="password"
                                        value={password}
                                        id="password"
                                        className="form-control"
                                        onChange={this.onChange}
                                        name="password"/>
                                </FormGroup>
                                <FormGroup className="col-lg-6">
                                    <ControlLabel>Repeat Password</ControlLabel>
                                    <FormControl
                                        type="password"
                                        value={passwordRepeat}
                                        id="passwordRepeat"
                                        className="form-control"
                                        onChange={this.onChange}
                                        name="passwordRepeat"/>
                                </FormGroup>
                                <FormGroup className="col-lg-6">
                                    <ControlLabel>Email Address</ControlLabel>
                                    <FormControl
                                        type="email"
                                        value={email}
                                        id="email"
                                        className="form-control"
                                        onChange={this.onChange}
                                        name="email"/>
                                </FormGroup>
                            </Row>
                            <div>
                                <Link to="/login" className="btn btn-default">Back to login</Link>

                                <div className="btn btn-accent pull-right" onClick={this.onRegister}>Register
                                </div>
                            </div>
                        </Form>
                    </Panel.Body>
                </Panel>
            </div>
        </div>);
    }

    private renderHead() {
        return <div className="view-header">
            <div className="header-icon">
                <i className="fa fa-user"/>
            </div>
            <div className="header-title">
                <h3>Register</h3>
                <small>
                    Please enter your data to register.
                </small>
            </div>
        </div>

    }
}
