import * as React from "react";
import {Form, FormGroup, Panel, Row} from "react-bootstrap";
import {<PERSON>} from "react-router-dom";

export default class LinkConfirmed extends React.Component<any, any> {
    public render() {
        return (<div>
            <div className="container-center lg">
                {this.renderHead()}

                <Panel className="panel-filled">
                    <Panel.Body>
                        <p/>
                        <Form id="registerForm">
                            <Row className="row">
                                <FormGroup className="col-lg-12">
                                    <p>Thank you for registering. You can login now.</p>
                                </FormGroup>
                            </Row>
                            <div>
                                <Link to="/login" className="btn btn-default">Back to login</Link>
                            </div>
                        </Form>
                    </Panel.Body>
                </Panel>
            </div>
        </div>);
    }

    private renderHead() {
        return <div className="view-header">
            <div className="header-icon">
                <i className="fa fa-user"/>
            </div>
            <div className="header-title">
                <h3>Register</h3>
            </div>
        </div>

    }
}
