/* eslint-disable no-undef,new-cap,no-new */
import {MPanel} from "@/components/common/MPanel";
import {observer} from "mobx-react";
import * as React from "react";
import Iframe from "react-iframe";
import {graphql} from "react-apollo";
import gql from "graphql-tag";
import {path} from "ramda";

const VolumeQuery = gql`query VolumeQuery($symbol: String!) {
    exchange {
        symbolTicker(symbol: $symbol) {
            volumeQuoteBTC
        }
    }
}`


// @ts-ignore
@graphql(VolumeQuery, {
    options: {
        pollInterval: 30000
    }
})
@observer
export class SingleView extends React.Component<any> {
    public render() {
        const volume = path(["props", "data", "exchange", "symbolTicker", "volumeQuoteBTC"])(this)
        const volumeNumber = volume ? Number(volume).toFixed(2) : 0
        const {symbol, interval, symbols, numScreens} = this.props;

        let height = 500
        switch (Number(numScreens)) {
            case 0:
            case 1:
                height = 750;
                break;
            case 2:
            case 3:
                height = 350;
                break;
            case 4:
            case 5:
                height = 216;
                break;
            case 6:
            case 7:
                height = 150
                break;
            case 8:
                height = 216;
                break;
            case 9:
            case 10:
            case 11:
                height = 150
                break;
        }

        const toIntervalClass = (int: string) => {
            return interval === int ? "" : "btn btn-default"
        }

        return (<MPanel filled className="m-b-none">
            <div className="panel-heading">

                <span className={"text-accent m-r-xs"}>{symbol.slice(0, -4)}</span>

                <span className={"m-l-sm " + toIntervalClass("5") + " btn-xs m-r-xs"}
                      onClick={() => this.setInterval('5')}>5m</span>
                <span className={toIntervalClass("15") + " btn-xs m-r-xs"}
                      onClick={() => this.setInterval('15')}>15m</span>
                <span className={toIntervalClass("60") + " btn-xs m-r-xs"}
                      onClick={() => this.setInterval('60')}>1h</span>
                <span className={toIntervalClass("120") + " btn-xs m-r-xs"}
                      onClick={() => this.setInterval('120')}>2h</span>
                <span className={toIntervalClass("240") + " btn-xs m-r-xs"}
                      onClick={() => this.setInterval('240')}>4h</span>
                <span className={toIntervalClass("D") + " btn-xs m-r-xs"}
                      onClick={() => this.setInterval('D')}>1d</span>

                <span className={"pull-right"}>
                        <a className={"btn btn-xs btn-default m-l-xs"} href={"/coin/" + symbol} target={"_blank"}>
                            <i className={"fa fa-window-restore"}/>
                        </a>
                </span>

                <span className="pull-right">
                <select className="form-control single-view-select"
                        value={symbol}
                        onChange={this.setSymbol}>
                    {symbols
                        ? [
                            <option key="default" value="DEFAULT">Select symbol</option>,
                            ...symbols.map(s =>
                                (<option key={s.symbol} value={s.symbol}>{s.baseAsset}
                                </option>))]
                        : null}
                </select>
                </span>

                <span className="pull-right m-r-sm split-screen-volume text-muted">
                    Vol {volumeNumber}
                </span>
            </div>

            {symbol ?
                <Iframe url={"/split/" + symbol + '/' + interval + "/" + height}
                        width="100%"
                        id={"myId" + symbol}
                        key={"myId" + symbol + numScreens}
                        height={height + "px"}
                        frameBorder={0}
                        className=""/>
                : null}

        </MPanel>);
    }

    public setInterval = interval => {
        if (this.props.saveFrame) {
            this.props.saveFrame(this.props.w, this.props.symbol, interval);
        }
    };

    public setSymbol = e => {
        if (this.props.saveFrame) {
            this.props.saveFrame(this.props.w, e.target.value, this.props.interval);
        }
    };
}

