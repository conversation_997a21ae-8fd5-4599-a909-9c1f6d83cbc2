/* eslint-disable no-undef,new-cap,no-new */
import * as React from "react";
import {withRouter} from "react-router";

// @ts-ignore
@withRouter
export class SingleFrame extends React.Component<any> {

    public componentDidMount() {
        this.drawChart();
    }

    public drawChart() {
        const pathname = this.props.location.pathname;
        const splittedPath = pathname.split('/');
        const symbol = splittedPath[splittedPath.length - 3];
        const interval = splittedPath[splittedPath.length - 2];

        if (window.TradingView !== undefined) {
            new window.TradingView.widget(
                {
                    autosize: true,
                    symbol: `BINANCE:${symbol}`,
                    interval: interval !== null ? interval : '5',
                    timezone: 'Europe/Berlin',
                    theme: 'Dark',
                    style: '1',
                    locale: 'en',
                    toolbar_bg: '#f1f3f6',
                    enable_publishing: false,
                    hide_top_toolbar: true,
                    hide_side_toolbar: true,
                    allow_symbol_change: false,
                    hidevolume: false,
                    show_popup_button: false,
                    save_image: false,
                    symboledit: false,
                    hideideas: true,
                    container_id: 'tradingview_e4340',
                    referral_id: '10238',
                },
            );
        }
    }

    public render() {
        const pathname = this.props.location.pathname;
        const splittedPath = pathname.split('/');
        const height = splittedPath[splittedPath.length - 1];

        return (
            <div className="tradingview-widget-container">
                <div id="tradingview_e4340" style={{"height": (height - 6) + "px"}}/>
            </div>);
    }
}
