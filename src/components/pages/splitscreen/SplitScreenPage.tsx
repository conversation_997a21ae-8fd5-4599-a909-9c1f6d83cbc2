import {SingleView} from "@/components/pages/splitscreen/SingleView";
import {LoadTopVolumes} from "@/graphql/splitscreen/LoadTopVolumes";
import {SaveSplitMutation} from "@/graphql/splitscreen/SaveSplitMutation";
import {SplitPageQuery} from "@/graphql/splitscreen/SplitPageQuery";
import {observable} from "mobx";
import {inject, observer} from "mobx-react";
import {path} from "ramda";
import * as React from "react";
import {graphql, withApollo} from "react-apollo";
import {Col, Row} from "react-bootstrap";
import {LoadPumps} from "@/graphql/splitscreen/LoadPumps";
import {success} from "@/utils/Toast";
import IWSTicker = GQL.IWSTicker;

// @ts-ignore
@withApollo
// @ts-ignore
@graphql(SplitPageQuery, {})
@inject("exchangeStore")
// @ts-ignore
@observer
export class SplitScreenPage extends React.Component<any> {

    @observable public topMarkets = [];
    @observable public topVolumes = []
    @observable public pumps = []
    @observable public numScreens = 1
    @observable public pumpModeEnabled = false

    private pumpPolling = -1;
    private windows = [2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];

    public async componentDidMount() {
        this.numScreens = Number(localStorage.getItem('splitscreen.numscreens'));
        // await this.loadPumps()
    }

    public render() {
        const {exchangeInfo} = this.props.exchangeStore

        let colSpaceLg = 4
        switch (this.numScreens) {
            case 0:
                colSpaceLg = 12;
                break;
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
                colSpaceLg = 6
                break;
            case 8:
            case 9:
            case 10:
            case 11:
                colSpaceLg = 4;
                break;
        }

        return <div>
            <Row style={{minHeight: '26px'}}>
                <Col>
                    <span className="pull-right">
                    <select className="form-control split-screen-number"
                            value={this.numScreens}
                            onChange={this.setNumScreens}>
                        {this.windows
                            ? [1, ...this.windows].map(s =>
                                (<option key={s - 1} value={s - 1}>{s} screens
                                </option>))
                            : null}
                    </select>
                    </span>
                    <span className="pull-right btn btn-default btn-xs m-r-xs"
                          onClick={() => this.setGlobalInterval('D')}>1d</span>
                    <span className="pull-right btn btn-default btn-xs m-r-xs"
                          onClick={() => this.setGlobalInterval('240')}>4h</span>
                    <span className="pull-right btn btn-default btn-xs m-r-xs"
                          onClick={() => this.setGlobalInterval('120')}>2h</span>
                    <span className="pull-right btn btn-default btn-xs m-r-xs"
                          onClick={() => this.setGlobalInterval('60')}>1h</span>
                    <span className="pull-right btn btn-default btn-xs m-r-xs"
                          onClick={() => this.setGlobalInterval('15')}>15m</span>
                    <span className="pull-right btn btn-default btn-xs m-r-xs"
                          onClick={() => this.setGlobalInterval('5')}>5m</span>
                    <span className="pull-right btn btn-xs btn-default m-r-xs"
                          onClick={this.loadTopVolume}>Load top volumes</span>
                    <span
                        className={`pull-right btn btn-xs ${this.pumpModeEnabled ? 'btn-success' : 'btn-default'} m-r-xs`}
                        onClick={async () => {
                            await this.loadPumps();

                            if (this.pumpModeEnabled) {
                                success("Pumps loaded")
                            }
                        }}>Pumps</span>
                </Col>
            </Row>
            <Row>
                <Col className={"col-md-6 col-lg-" + colSpaceLg + " col-xs-12 p-5"}>
                    <SingleView symbols={exchangeInfo}
                                symbol={"BTCUSDT"}
                                w={1}
                                numScreens={this.numScreens}
                                saveFrame={this.saveFrameWithRefetch}
                                interval={this.getSplitView(1) ? this.getSplitView(1).interval : '5'}/>
                </Col>
                <Col>
                    {this.windows.slice(0, this.numScreens).map(x => {
                        const splitview = this.getSplitView(x)
                        if (splitview) {
                            const symbol = splitview.market
                            const interval = splitview.interval

                            return <div className={"col-md-6 col-lg-" + colSpaceLg + " col-xs-12 p-5"}
                                        key={x + symbol + interval}>
                                <SingleView symbols={exchangeInfo}
                                            symbol={symbol}
                                            interval={interval}
                                            numScreens={this.numScreens}
                                            w={x}
                                            saveFrame={this.saveFrameWithRefetch}/>

                            </div>
                        }
                    })
                    }
                </Col>
            </Row>
        </div>
    }

    private loadTopVolume = async () => {
        try {
            const response = await this.props.client.query({
                query: LoadTopVolumes,
                variables: {
                    number: 12
                },
                fetchPolicy: 'network-only'
            });
            const topVolumeCoins = path(['data', 'exchange', 'topVolumeCoins'])(response) as IWSTicker[]
            this.topMarkets = topVolumeCoins.map(x => x.symbol);

            const configs: any = []
            for (const i of this.windows) {
                const splitview = this.getSplitView(i) || {
                    interval: '120'
                }

                configs.push({
                    window: i,
                    market: this.topMarkets[i - 2],
                    interval: splitview.interval
                })
            }

            await this.saveFrameNoRefetch(configs)

            this.pumpModeEnabled = false
            this.props.data.refetch();
            clearInterval(this.pumpPolling)

            success("Top volumes loaded")

        } catch (e) {
            console.error(e)
        }
    };

    private loadPumps = async () => {
        const pollFunction = async () => {
            try {
                const response = await this.props.client.query({
                    query: LoadPumps,
                    fetchPolicy: 'network-only',
                });
                const pumps: any = path(['data', 'pump', 'lastPumps'])(response) || []
                this.pumps = pumps.map(x => x.symbol);

                const configs: any = []
                for (const i of this.windows) {
                    const splitview = this.getSplitView(i) || {
                        interval: '120'
                    }
                    configs.push({
                        window: i,
                        market: this.pumps[i - 2],
                        interval: splitview.interval
                    })
                }

                await this.saveFrameNoRefetch(configs)

                this.props.data.refetch();
            } catch (e) {
                console.error(e)
            }
        }
        if (this.pumpModeEnabled) {
            this.pumpModeEnabled = false
            clearInterval(this.pumpPolling)

            return;
        }

        if (!this.pumpModeEnabled) {
            this.pumpModeEnabled = true
            await pollFunction()

            this.pumpPolling = setInterval(async () => {
                await pollFunction()
            }, 5000)

            return
        }
    }

    private saveFrameWithRefetch = async (window: number, symbol: string, interval: string) => {
        const {client} = this.props;

        try {
            await client.mutate({
                mutation: SaveSplitMutation,
                variables: {
                    configs: [{window, market: symbol, interval}]
                },
                refetchQueries: [
                    {
                        query: SplitPageQuery
                    }
                ]
            })
        } catch (e) {
            console.error(e)
        }
    }

    private saveFrameNoRefetch = async (configs: [{ window: number, market: string, interval: string }]) => {
        const {client} = this.props;

        try {
            await client.mutate({
                mutation: SaveSplitMutation,
                variables: {
                    configs
                },
            })
        } catch (e) {
            console.error(e)
        }
    }

    private setGlobalInterval = async (interval: string) => {
        const configs: any = []
        for (const window of this.windows) {
            const splitview = this.getSplitView(window) || {
                market: 'ADAUSDT'
            }

            configs.push({
                window,
                market: splitview.market,
                interval
            })
        }
        await this.saveFrameNoRefetch(configs)

        this.props.data.refetch();
    }

    private setNumScreens = event => {
        this.numScreens = Number(event.target.value)
        localStorage.setItem('splitscreen.numscreens', event.target.value);
    }

    private getSplitViews = (): any => {
        return path(['data', 'user', 'splitview'])(this.props) || []
    }

    private getSplitView = (window: number) => {
        return this.getSplitViews().find(x => x.window === window)
    }
}