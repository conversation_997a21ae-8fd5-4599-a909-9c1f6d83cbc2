import PriceCalculation from "@/utils/PriceCalculation";
import {inject} from "mobx-react";
import {path} from 'ramda';
import {graphql} from 'react-apollo';

import * as React from "react";
import PageHeader from "@/components/common/PageHeader";
import {Link} from "react-router-dom";
import {AllIndicatorsGraphql} from "@/graphql/indicators/AllIndicators.graphql";

// @ts-ignore
@graphql(AllIndicatorsGraphql, {
    options: {
        pollInterval: 15000,
    }
})

@inject("exchangeStore")
export class IndicatorsPage extends React.Component<any> {
    public getIndicators() {
        const {exchangeStore} = this.props
        const orders = [];
        const indicators: any = path(['bot', 'getAllIndicators'])(this.props.data) || [];

        if (indicators.length === 0) {
            return (<tr>
                <td colSpan={5}>
                    No indicators
                </td>
            </tr>);
        }

        indicators.forEach(indicator => {
            const symbol = indicator.symbol

            const price = PriceCalculation.getFixedPrice(Number(indicator.price), exchangeStore.exchangeInfo, symbol)
            const timestamp = new Date(indicator.timestamp).toLocaleString()

            orders.push(<tr key={timestamp}>
                <td>{timestamp}</td>
                <td><Link to={'/coin/' + symbol}>{symbol}</Link></td>
                <td>{indicator.interval}</td>
                <td>{indicator.indicator}</td>
                <td>{indicator.action == 'long' ?
                    <span className="text-accent">{indicator.action}</span>
                    : indicator.action}</td>
                <td>{price}</td>
            </tr>);
        });

        return orders;
    }

    public render() {
        return <div className="container-fluid">
            <PageHeader section="Indicators"/>

            <div className="row">
                <div className="col-xs-12 col-lg-12">
                    <div className="panel panel-filled panel-c-warning">
                        <div className="panel-heading">
                            Indicators
                        </div>

                        <div className="panel-body" style={{overflowX: 'auto'}}>
                            <table className="table table-condensed small">
                                <thead>
                                <tr>
                                    <th>Timestamp</th>
                                    <th>Symbol</th>
                                    <th>Interval</th>
                                    <th>Indicator</th>
                                    <th>Action</th>
                                    <th>Price $</th>
                                </tr>
                                </thead>
                                <tbody>
                                {this.getIndicators()}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>;
    }
}
