.nav-tabs {
  border: none;
}

.nav-tabs > li.active > a, .nav-tabs > li.active > a:hover, .nav-tabs > li.active > a:focus {
  background-color: rgba(red(@background-light-color), green(@background-light-color), blue(@background-light-color), 0.5);
  color: #ffffff;
  border-width: 0 0 1px 0;
}

.tab-pane .panel-body {
  border-top: none;
  background-color: rgba(red(@background-light-color), green(@background-light-color), blue(@background-light-color), 0.5);
  margin-bottom: 20px;
}

.nav-tabs > li a:hover {
  background: transparent;
  border-color: transparent;
}

.nav-tabs > li > a {
  color: @color-font;
}

.tabs-below > .nav-tabs,
.tabs-right > .nav-tabs,
.tabs-left > .nav-tabs {
  border-bottom: 0;
}

.tabs-left .panel-body {
  position: static;
}

.tabs-left > .nav-tabs, .tabs-right > .nav-tabs {
  width: 20%;
}

.tabs-left .panel-body {
  width: 80%;
  margin-left: 20%;
}

.tabs-right .panel-body {
  width: 80%;
  margin-right: 20%;
}

.tab-content > .tab-pane,
.pill-content > .pill-pane {
  display: none;
}

.tab-content > .active,
.pill-content > .active {
  display: block;
}

.tabs-below > .nav-tabs > li {
  margin-bottom: 0;
}

.tabs-below > .nav-tabs > li > a {
  -webkit-border-radius: 0 0 4px 4px;
  -moz-border-radius: 0 0 4px 4px;
  border-radius: 0 0 4px 4px;
}


.tabs-left > .nav-tabs > li,
.tabs-right > .nav-tabs > li {
  float: none;
}

.tabs-left > .nav-tabs > li > a,
.tabs-right > .nav-tabs > li > a {
  margin-right: 0;
  margin-bottom: 3px;
}

.tabs-left > .nav-tabs {
  float: left;
  margin-right: 19px;
}

.tabs-left > .nav-tabs > li > a {
  -webkit-border-radius: 4px 0 0 4px;
  -moz-border-radius: 4px 0 0 4px;
  border-radius: 4px 0 0 4px;
}

.tabs-right > .nav-tabs {
  float: right;
  margin-left: 19px;
}

.tabs-right > .nav-tabs > li > a {
  -webkit-border-radius: 0 4px 4px 0;
  -moz-border-radius: 0 4px 4px 0;
  border-radius: 0 4px 4px 0;
}

