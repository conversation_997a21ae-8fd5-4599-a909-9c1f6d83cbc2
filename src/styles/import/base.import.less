
.label-accent {
  background-color: @color-accent;
}


body {
  background: @background-color;
   font-family: 'Roboto', sans-serif;
  color: @color-font;
}

.content {
  margin-top: 46px;
  margin-left: @navigation-width;
  position: relative;
  padding: 30px;
  transition: margin-left 0.3s ease-out;
  -webkit-transition: margin-left 0.3s ease-out;

  &:before {
    content: '';
    position: fixed;
    top: 0;
    bottom: 0;
    z-index: -2;
    left: 0;
    right:0;
    background-image: -webkit-radial-gradient(top, circle cover, #393b45 0%, darken(@background-color,1%) 70%);
    background-image: -moz-radial-gradient(center, circle cover, #393b45 0%, darken(@background-color,1%) 70%);
    background-image: -o-radial-gradient(center, circle cover, #393b45 0%, darken(@background-color,1%) 70%);
    background-image: radial-gradient(center, circle cover, #393b45 0%, darken(@background-color,1%) 70%);
    background-position: 50%, 50%;
  }
}


.navigation {
  margin-top: 60px;
  background-color: darken(@background-color, 5%);;
  width: @navigation-width;
  position: absolute;
  left: 0;
  bottom: 0;
  top: 0;
  transition: left 0.3s ease-out;
  -webkit-transition: left 0.3s ease-out;

  nav {
    background-color: @navigation-color;
  }

  &:before {
    transition: left 0.3s ease-out;
    -webkit-transition: left 0.3s ease-out;
    content: '';
    position: fixed;
    top: 0;
    bottom: 0;
    z-index: -1;
    left: 0;
    background-color: darken(@background-color, 5%);
    width: 200px;
  }
}


body.nav-toggle {
  .content {
    margin-left: 0;
  }
  .navigation {
    left: -200px;

    &:before {
      left: -200px;
    }
  }
}

@media (max-width: 767px) {
  .content {
    margin-left: 0;
    padding: 30px 0 10px 0;

  }
  .navigation {
    left: -200px;
    z-index: 10;

    &:before {
      left: -200px;
    }
  }

  body.nav-toggle {
    .content {
      margin-left: 0;
    }
    .navigation {
      left: 0;
      z-index: 10;

      &:before {
        left: 0;
      }
    }
  }
}

.blank {
  margin: 0;
}

.container-center {
  max-width: 400px;
  margin: 10% auto 0;
  padding: 20px;


  &.sm {
    max-width: 200px;
  }

  &.md {
    max-width: 600px;
  }

  &.lg {
    max-width: 800px;
  }

}





/* Various */


a {
  color: @color-accent;
  text-decoration: none;

  &.no-color {
    color: white;
  }

  &:hover, &:focus {
    color: lighten(@color-accent, 10%);
    outline: none;
  }
}

hr {
  margin-top: 0;
  margin-bottom: 10px;
  border: 0;
  border-top: 1px solid lighten(@background-color, 6%);
}

code {
  padding: 2px 4px;
  font-size: 90%;
  color: @color-accent;
  background-color: darken(@background-color, 5%);
  border-radius: 3px;
}

pre {
  //padding: 2px 4px;
  font-size: 90%;
  color: @color-font;
  background-color: darken(@background-color, 5%);
  border-radius: 3px;
  border: none;
}

mark {
  background-color: #e9e599;
  border-radius: 2px;
  padding: .1em .4em;
}

label {
  color: #ffffff;
  font-weight: 500;
}

sup {
  font-size: 12px;
  top: -.8em;
}

.help-block {
  color: darken(@color-font, 15%);
}

.bs-example {
  background-color: @background-color;
  padding: 10px;
  border-radius: 3px;
}

.show-grid [class^="col-"] {
  background-color: @background-color !important;
  border: 1px solid lighten(@background-color, 6%);
  padding-bottom: 10px;
  padding-top: 10px;
}

.slight {
  font-size: 11px;
  font-weight: 300;
}

.show-grid {
  margin: 10px 0 !important;
}

//.row {
//  margin-left: -10px;
//  margin-right: -10px;
//}

/* Icons */

.font-example-box:hover {
  color: #ffffff;

}

.font-example-box {
  text-align: center;
  min-height: 90px;
  margin: 10px 0;
  font-size: 42px;
}

.font-example-box i {
  font-size: 22px;
}

.icon-example-name {
  display: block;
  font-size: 11px;
}

.back-link {
  float: left;
  width: 100%;
  margin: 10px;
}

/* Images */

img {

  &.image-xs {
    width: 16px;
    height: 16px;
  }

  &.image-md {
    width: 32px;
    height: 32px;
  }

  &.image-lg {
    width: 64px;
    height: 64px;
  }

  &.image-xl {
    width: 128px;
    height: 128px;
  }

}

/* UI Sortable */
.ui-sortable .panel-body {
  cursor: move;
}

.ui-sortable-placeholder {
  border: 1px dashed lighten(@border-color, 10%) !important;
  visibility: visible !important;
}

.cursor-pointer {
  cursor: default;
}

.cursor-pointer:hover {
  text-decoration: underline;
}
