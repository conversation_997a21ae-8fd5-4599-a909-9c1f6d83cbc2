body {
  font-family: 'ProximaNovaRegular', sans-serif;
}

.content:before {
  background-image: -webkit-radial-gradient(top, circle cover, #e8e8e8 0, #ffffff 70%);
}

.panel.panel-filled {
  border-color: #cecece;
}

.panel > .panel-heading {
  background-color: #e0e0e0;
  color: #828282;
}

h1,
h2,
h3,
h4,
h5,
h6,
.h1,
.h2,
.h3,
.h4,
.h5,
.h6 {
  color: #949ba2;
}

h1 small,
h2 small,
h3 small,
h4 small,
h5 small,
h6 small,
.h1 small,
.h2 small,
.h3 small,
.h4 small,
.h5 small,
.h6 small,
h1 .small,
h2 .small,
h3 .small,
h4 .small,
h5 .small,
h6 .small,
.h1 .small,
.h2 .small,
.h3 .small,
.h4 .small,
.h5 .small,
.h6 .small {
  color: #777;
}

.navigation, .navigation:before {
  background-color: #e0e0e0;
}