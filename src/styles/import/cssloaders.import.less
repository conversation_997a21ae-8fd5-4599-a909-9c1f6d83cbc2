.ld-loading {

  .panel-body {
    position: relative;

    > * {
      transition: opacity 0.3s;
      opacity: 0.2;
    }

    &:before {
      display: block;
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 50;
    }
  }

  .loader {
    display: block;
  }
}

.loader {
  display: none;
  position: absolute;
  top: 45%;
  left: 0;
  right: 0;
  z-index: 100;

}

// Bar loader

.loader-bar,
.loader-bar:before,
.loader-bar:after {
  background: @color-accent;
  -webkit-animation: load-bar-an 1s infinite ease-in-out;
  animation: load-bar-an 1s infinite ease-in-out;
  width: 0.66em;
  height: 0.66em;
}

.loader-bar {
  color: @color-accent;
  text-indent: -9999em;
  margin: auto auto;
  position: relative;
  font-size: 11px;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;

  &:before,
  &:after {
    position: absolute;
    top: 0;
    content: '';
  }

  &:before {
    left: -1em;
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
  }

  &:after {
    left: 1em;
  }

}

@-webkit-keyframes load-bar-an {
  .load-bar-frames;
}

@keyframes load-bar-an {
  .load-bar-frames;
}

.load-bar-frames() {

  0%,
  80%,
  100% {
    box-shadow: 0 0;
    height: 2em;
  }

  40% {
    box-shadow: 0 -1em;
    height: 2.6em;
  }

}

// Spin loader

.loader-spin,
.loader-spin:after {
  border-radius: 50%;
  width: 3em;
  height: 3em;
}

.loader-spin {
  margin: auto auto;
  font-size: 10px;
  position: relative;
  text-indent: -9999em;
  border-top: 0.8em solid fade(@color-accent, 20%);
  border-right: 0.8em solid fade(@color-accent, 20%);
  border-bottom: 0.8em solid fade(@color-accent, 20%);
  border-left: 0.8em solid fade(@color-accent, 100%);
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation: load-spin-an 1.1s infinite linear;
  animation: load-spin-an 1.1s infinite linear;
}

@-webkit-keyframes load-spin-an {
  .load-spin-frames;
}

@keyframes load-spin-an {
  .load-spin-frames;
}

.load-spin-frames() {

  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }

}

// Dots loader

.loader-dots,
.loader-dots:before,
.loader-dots:after {
  border-radius: 30%;
  width: 1.2em;
  height: 1.2em;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation: load-dots-an 1.8s infinite ease-in-out;
  animation: load-dots-an 1.8s infinite ease-in-out;
}

.loader-dots {
  color: @color-accent;
  font-size: 10px;
  margin: auto auto;
  position: relative;
  text-indent: -9999em;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;

  &:before,
  &:after {
    content: '';
    position: absolute;
    top: 0;
  }

  &:before {
    left: -2em;
    -webkit-animation-delay: -0.32s;
    animation-delay: -0.32s;
  }

  &:after {
    left: 2em;
  }

}

@-webkit-keyframes load-dots-an {
  .load-dots-frames;
}

@keyframes load-dots-an {
  .load-dots-frames;
}

.load-dots-frames() {

  0%,
  80%,
  100% {
    box-shadow: 0 2em 0 -1.3em;
  }

  50% {
    box-shadow: 0 2em 0 0;
  }

}

// For demo purpose only
.loader-example {
  padding: 40px 0;
  height: 120px;
}