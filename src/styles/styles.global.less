@import "~font-awesome/less/font-awesome.less";
@import "fonts/proximanova.css";
@import "import/third/bootstrap.css";
@import "~toastr/toastr.less";
@import "~react-vis/dist/style.css";
@import "import/colors.import.less";
@import "import/mixins/buttons.import.less";
@import "import/mixins/table-row.import.less";
@import "import/buttons.import.less";
@import "import/loaders.import.less";
@import "import/text.import.less";
@import "import/margins.import.less";
@import "import/base.import.less";
@import "import/fonts.import.less";
@import "import/navbar.import.less";
@import "import/nav.import.less";
@import "import/panels.import.less";
@import "import/checkbox.import.less";
@import "import/pagination.import.less";
@import "import/progress.import.less";
@import "import/modals.import.less";
@import "import/timeline.import.less";
@import "import/nestable.import.less";
@import "import/tabs.import.less";
@import "import/tables.import.less";
@import "import/alerts.import.less";
@import "import/forms.import.less";
@import "import/pages/support.import.less";
@import "import/customization/flotcharts.import.less";
@import "import/customization/editor.import.less";
@import "import/cssloaders.import.less";

//@import "import/third/bootstrap-white.less";

@import "import/pages/currentPrice.less";
@import "import/pages/dashboard.less";
@import "import/pages/orderForm.less";
@import "import/pages/tradebot.less";
@import "import/pages/strategytester.less";
@import "import/pages/commons.less";

.jqstooltip {

  padding: 5px !important;
  border: none !important;
  border-radius: 10px;
  background-color: #f6a821 !important;
  width: auto !important;
  height: auto !important;
}


.hoverinfo {
  background-color: @color-accent !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, .5) !important;
  -webkit-box-shadow: 0 5px 15px rgba(0, 0, 0, .5) !important;
  border: none !important;
  border-radius: 4px !important;
  color: #ffffff !important;
  padding: 6px 12px !important;
}

.p-5 {
  padding: 5px;
}

.opacity-0 {
  opacity: 0 !important;
}