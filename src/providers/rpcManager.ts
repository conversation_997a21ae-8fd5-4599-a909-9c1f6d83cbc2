import { ethers } from 'ethers';
import { enhancedLogger } from '../utils/enhancedLogger';

export interface RPCProvider {
  name: string;
  http: string;
  ws: string;
  priority: number;
  rateLimited: boolean;
  maxRequestsPerSecond?: number;
}

export class RPCManager {
  private providers: RPCProvider[] = [];
  private currentProvider: RPCProvider | null = null;
  private httpProvider: ethers.JsonRpcProvider | null = null;
  private wsProvider: ethers.WebSocketProvider | null = null;
  private failedProviders: Set<string> = new Set();
  private requestCounts: Map<string, number> = new Map();
  private lastResetTime: number = Date.now();

  constructor(chainId: number) {
    this.initializeProviders(chainId);
    this.selectBestProvider();
  }

  private initializeProviders(chainId: number): void {
    const isMainnet = chainId === 1;
    const isTestnet = chainId === 11155111; // Sepolia

    // Alchemy (Best for MEV - High rate limits)
    if (process.env.ALCHEMY_API_KEY) {
      this.providers.push({
        name: 'Alchemy',
        http: isMainnet 
          ? `https://eth-mainnet.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`
          : `https://eth-sepolia.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`,
        ws: isMainnet
          ? `wss://eth-mainnet.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`
          : `wss://eth-sepolia.g.alchemy.com/v2/${process.env.ALCHEMY_API_KEY}`,
        priority: 1,
        rateLimited: false,
        maxRequestsPerSecond: 100
      });
    }

    // QuickNode (MEV Optimized - No rate limits on paid plans)
    if (process.env.QUICKNODE_ENDPOINT) {
      this.providers.push({
        name: 'QuickNode',
        http: process.env.QUICKNODE_ENDPOINT,
        ws: process.env.QUICKNODE_ENDPOINT.replace('https://', 'wss://'),
        priority: 2,
        rateLimited: false
      });
    }

    // Ankr (Good free tier)
    if (process.env.ANKR_API_KEY) {
      this.providers.push({
        name: 'Ankr',
        http: isMainnet
          ? `https://rpc.ankr.com/eth/${process.env.ANKR_API_KEY}`
          : `https://rpc.ankr.com/eth_sepolia/${process.env.ANKR_API_KEY}`,
        ws: isMainnet
          ? `wss://rpc.ankr.com/eth/ws/${process.env.ANKR_API_KEY}`
          : `wss://rpc.ankr.com/eth_sepolia/ws/${process.env.ANKR_API_KEY}`,
        priority: 3,
        rateLimited: false,
        maxRequestsPerSecond: 50
      });
    }

    // Local Node (Best option - no limits)
    if (process.env.LOCAL_NODE_URL) {
      this.providers.push({
        name: 'Local Node',
        http: process.env.LOCAL_NODE_URL,
        ws: process.env.LOCAL_NODE_WS || process.env.LOCAL_NODE_URL.replace('http', 'ws'),
        priority: 0, // Highest priority
        rateLimited: false
      });
    }

    // Fallback public RPCs (rate limited but free)
    const publicRpcs = isMainnet ? [
      { name: 'Ethereum Public Node', http: 'https://ethereum.publicnode.com', ws: 'wss://ethereum.publicnode.com' },
      { name: 'Ankr Public', http: 'https://rpc.ankr.com/eth', ws: 'wss://rpc.ankr.com/eth/ws' },
      { name: 'LlamaRPC', http: 'https://eth.llamarpc.com', ws: 'wss://eth.llamarpc.com' },
      { name: 'Cloudflare', http: 'https://cloudflare-eth.com', ws: 'wss://cloudflare-eth.com/ws' }
    ] : [
      { name: 'Ethereum Public Node Sepolia', http: 'https://ethereum-sepolia.publicnode.com', ws: 'wss://ethereum-sepolia.publicnode.com' },
      { name: 'Ankr Public Sepolia', http: 'https://rpc.ankr.com/eth_sepolia', ws: 'wss://rpc.ankr.com/eth_sepolia/ws' }
    ];

    publicRpcs.forEach((rpc, index) => {
      this.providers.push({
        name: rpc.name,
        http: rpc.http,
        ws: rpc.ws,
        priority: 10 + index, // Lower priority
        rateLimited: true,
        maxRequestsPerSecond: 5 // Conservative for public RPCs
      });
    });

    // Sort by priority
    this.providers.sort((a, b) => a.priority - b.priority);
    
    enhancedLogger.systemStatus(`Initialized ${this.providers.length} RPC providers`);
    this.providers.forEach(p => {
      enhancedLogger.systemStatus(`- ${p.name}: ${p.rateLimited ? 'Rate Limited' : 'No Limits'}`);
    });
  }

  private selectBestProvider(): void {
    // Find the best available provider
    const availableProviders = this.providers.filter(p => !this.failedProviders.has(p.name));
    
    if (availableProviders.length === 0) {
      // Reset failed providers if all have failed
      this.failedProviders.clear();
      enhancedLogger.warning('All providers failed, resetting...');
      this.selectBestProvider();
      return;
    }

    const bestProvider = availableProviders[0];
    
    if (this.currentProvider?.name !== bestProvider.name) {
      this.currentProvider = bestProvider;
      this.initializeConnections();
      enhancedLogger.success(`Switched to RPC provider: ${bestProvider.name}`);
    }
  }

  private async initializeConnections(): Promise<void> {
    if (!this.currentProvider) return;

    try {
      // Initialize HTTP provider
      this.httpProvider = new ethers.JsonRpcProvider(this.currentProvider.http);
      
      // Test the connection
      await this.httpProvider.getBlockNumber();
      
      // Initialize WebSocket provider
      try {
        this.wsProvider = new ethers.WebSocketProvider(this.currentProvider.ws);
        enhancedLogger.success(`WebSocket connection established: ${this.currentProvider.name}`);
      } catch (wsError) {
        enhancedLogger.warning(`WebSocket failed for ${this.currentProvider.name}, using HTTP only`);
        this.wsProvider = null;
      }

    } catch (error) {
      enhancedLogger.error(`Failed to connect to ${this.currentProvider.name}`, error);
      this.failedProviders.add(this.currentProvider.name);
      this.selectBestProvider();
    }
  }

  private checkRateLimit(providerName: string): boolean {
    const provider = this.providers.find(p => p.name === providerName);
    if (!provider || !provider.rateLimited || !provider.maxRequestsPerSecond) {
      return true; // No rate limit
    }

    const now = Date.now();
    if (now - this.lastResetTime > 1000) {
      // Reset counters every second
      this.requestCounts.clear();
      this.lastResetTime = now;
    }

    const currentCount = this.requestCounts.get(providerName) || 0;
    if (currentCount >= provider.maxRequestsPerSecond) {
      return false; // Rate limited
    }

    this.requestCounts.set(providerName, currentCount + 1);
    return true;
  }

  async makeRequest(method: string, params: any[] = []): Promise<any> {
    if (!this.httpProvider || !this.currentProvider) {
      throw new Error('No RPC provider available');
    }

    // Check rate limit
    if (!this.checkRateLimit(this.currentProvider.name)) {
      enhancedLogger.warning(`Rate limit hit for ${this.currentProvider.name}, switching provider...`);
      this.failedProviders.add(this.currentProvider.name);
      this.selectBestProvider();
      return this.makeRequest(method, params);
    }

    try {
      return await this.httpProvider.send(method, params);
    } catch (error) {
      enhancedLogger.error(`Request failed for ${this.currentProvider.name}`, error);
      this.failedProviders.add(this.currentProvider.name);
      this.selectBestProvider();
      
      // Retry with new provider
      if (this.httpProvider && this.currentProvider) {
        return this.makeRequest(method, params);
      }
      throw error;
    }
  }

  getHttpProvider(): ethers.JsonRpcProvider {
    if (!this.httpProvider) {
      throw new Error('No HTTP provider available');
    }
    return this.httpProvider;
  }

  getWebSocketProvider(): ethers.WebSocketProvider | null {
    return this.wsProvider;
  }

  getCurrentProviderInfo(): { name: string; rateLimited: boolean } | null {
    if (!this.currentProvider) return null;
    return {
      name: this.currentProvider.name,
      rateLimited: this.currentProvider.rateLimited
    };
  }

  async switchToNextProvider(): Promise<void> {
    if (this.currentProvider) {
      this.failedProviders.add(this.currentProvider.name);
    }
    this.selectBestProvider();
  }

  getProviderStats(): { total: number; available: number; failed: number } {
    return {
      total: this.providers.length,
      available: this.providers.length - this.failedProviders.size,
      failed: this.failedProviders.size
    };
  }
}
