import {find, propEq} from 'ramda';

export default class PriceCalculation {
    public static getFixedAmount(amount: any, symbols: any, symbol: string): number {
        if (symbols && symbols.length > 0) {
            const symbolData = find(propEq("symbol", symbol))(symbols) || {};
            if (!symbolData.filters) {
                return amount
            }

            const filters = symbolData.filters
            const lotSize = find(propEq('filterType', 'LOT_SIZE'))(filters) || {};
            const stepSize = lotSize.stepSize;
            let toFixed = (-Math.floor(
                    (Math.log(stepSize) / Math.log(10)) - 1)
            ) - 1;
            const amountFloat = Number(amount);
            toFixed = toFixed < 0 ? 0 : toFixed;
            return Math.trunc(amountFloat * Math.pow(10, toFixed)) / Math.pow(10, toFixed);
        }

        return amount
    }

    public static getFixedPrice(price, symbols, symbol): number {
        if (symbols && symbols.length > 0) {
            const symbolData = find(propEq("symbol", symbol))(symbols) || {};

            if (!symbolData.filters) {
                return price
            }

            const filters = symbolData.filters
            const priceFilter = find(propEq('filterType', 'PRICE_FILTER'))(filters) || {};
            const tickSize = priceFilter.tickSize;
            let toFixed = (-Math.floor(
                    (Math.log(tickSize) / Math.log(10)) - 1)
            ) - 1;
            const priceFloat = Number(price);
            toFixed = toFixed < 0 ? 0 : toFixed;
            return Math.trunc(priceFloat * Math.pow(10, toFixed)) / Math.pow(10, toFixed);
        }

        return price
    }

    public static getFixedPriceWithLength(price, symbols, symbol) {
        if (symbols && symbols.length > 0) {
            const symbolData = find(propEq("symbol", symbol))(symbols) || {};
            if (!symbolData.filters) {
                return price
            }

            const filters = symbolData.filters
            const priceFilter = find(propEq('filterType', 'PRICE_FILTER'))(filters) || {};
            const tickSize = priceFilter.tickSize;
            const toFixed = (-Math.floor(
                    (Math.log(tickSize) / Math.log(10)) - 1)
            ) - 1;
            return this.getFixedPrice(price, symbols, symbol).toFixed(toFixed)
        }

        return price
    }

    public static getFixedAmountWithLength(amount, symbols, symbol) {
        if (symbols && symbols.length > 0) {
            const symbolData = find(propEq("symbol", symbol))(symbols) || {};
            if (!symbolData.filters) {
                return amount
            }

            const filters = symbolData.filters
            const lotSize = find(propEq('filterType', 'LOT_SIZE'))(filters) || {};
            const stepSize = lotSize.stepSize;
            const toFixed = (-Math.floor(
                    (Math.log(stepSize) / Math.log(10)) - 1)
            ) - 1;

            return this.getFixedAmount(amount, symbols, symbol).toFixed(toFixed)
        }

        return amount
    }

    public static toPercent(prev, now) {
        return (((now / prev) - 1) * 100).toFixed(2)
    }

    public static toSimplePercent(prev, now) {
        return (((now / prev)) * 100).toFixed(2)
    }

    public static toVolume(x) {
        return x.toFixed(0);
    }

    public static toClassName(x) {
        if (x > 0) {
            return 'text-success';
        } else if (x < 0) {
            return 'text-danger';
        } else {
            return 'text-muted';
        }
    }

    public static toClassNamePercent(prev, now) {
        return PriceCalculation.toClassName(PriceCalculation.toPercent(prev, now));
    }

    public static withPlus(num): string {
        if (Number(num) >= 0) {
            return '+' + num;
        }

        return num;
    }

    public static toPercentWithPlus(prev, now) {
        const x = PriceCalculation.toPercent(prev, now);
        if (Number(x) >= 0) {
            return '+' + x;
        }

        return x;
    }
}
