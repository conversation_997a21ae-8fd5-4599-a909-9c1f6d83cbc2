import * as toastr from "toastr";

export const success = (message: string, title?: string, overrides?: ToastrOptions): void => {
    let options: ToastrOptions = {
        timeOut: 1000,
        positionClass: 'toast-bottom-right',
    }

    if (overrides) {
        options = overrides
    }
    toastr.success(message, title, options)
}

export const error = (message: string, title?: string, overrides?: ToastrOptions): void => {
    let options: ToastrOptions = {
        timeOut: 1000,
        positionClass: 'toast-bottom-right',
    }

    if (overrides) {
        options = overrides
    }
    toastr.error(message, title, options)
}

export const warning = (message: string, title?: string, overrides?: ToastrOptions): void => {
    let options: ToastrOptions = {
        timeOut: 1000,
        positionClass: 'toast-bottom-right',
    }

    if (overrides) {
        options = overrides
    }
    toastr.warning(message, title, options)
}