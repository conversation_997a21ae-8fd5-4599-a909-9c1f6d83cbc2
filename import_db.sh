#!/bin/bash

DIR=$(ls -d /Volumes/backup_server/Server/dssrv/Acti* | tail -1)
echo "Using backup: $DIR"

cp "$DIR/root/mbql.tar.lz" /tmp/mbql.tar.lz
echo "Copy mbql.tar.lz"

cd /tmp
echo "Unpack lz4"
lz4 -d mbql.tar.lz mbql

db_file=mbql

echo "Unpack tar"
tar xfv mbql

FOLDER=$(ls var/backups/mongodb)

echo "Starting restore"
#mongorestore --preserveUUID \
#        --keepIndexVersion \
#        --noIndexRestore \
#        --numInsertionWorkersPerCollection=1 \
#        --numParallelCollections=1 \
#        --drop \
#        --db mbql \
#        --gzip \
#        --archive="$db_file" \
#        mongodb://localhost:27017

mongorestore --preserveUUID \
        --keepIndexVersion \
        --noIndexRestore \
        --numInsertionWorkersPerCollection=1 \
        --numParallelCollections=1 \
        --drop "var/backups/mongodb/$FOLDER"

rm -rf mbql
rm -rf /tmp/mbql.tar.lz
