name: hummingbot
channels:
  - conda-forge
  - defaults
dependencies:
  ### Packages needed for the build/install process
  - conda-build>=3.26.0
  - coverage>=7.2.7
  - cython
  - flake8>=6.0.0
  - diff-cover>=7.7.0
  - pip>=23.2.1
  - pre-commit>=3.3.3
  - python>=3.10.12
  - pytest>=7.4.0
  - pytest-asyncio>=0.16.0
  - setuptools>=68.0.0
  ### Packages used within HB and helping reduce the footprint of pip-installed packages
  - aiohttp>=3.8.5
  - asyncssh>=2.13.2
  - aioprocessing>=2.0.1
  - aioresponses>=0.7.4
  - aiounittest>=1.4.2
  - async-timeout>=4.0.2,<5
  - bidict>=0.22.1
  - bip-utils
  - cachetools>=5.3.1
  - commlib-py>=0.11
  - cryptography>=41.0.2
  - injective-py==1.10.*
  - eth-account >=0.13.0
  - msgpack-python
  - numpy>=1.25.0,<2
  - objgraph
  - pandas>=2.0.3
  - pandas-ta>=0.3.14b
  - prompt_toolkit>=3.0.39
  - protobuf>=4.23.3
  - psutil>=5.9.5
  # No linux-aarch64/ppc64le conda package available ... yet
  # - ptpython>=3.0.25
  - pydantic>=2
  - pyjwt>=2.3.0
  - pyperclip>=1.8.2
  - requests>=2.31.0
  - ruamel.yaml>=0.2.5
  - rust
  - safe-pysha3
  - scalecodec
  - scipy>=1.11.1
  - six>=1.16.0
  - sqlalchemy>=1.4.49
  - tabulate>=0.9.0
  - ujson>=5.7.0
  # This needs to be restricted to <2.0 - tests fail otherwise
  - urllib3>=1.26.15,<2.0
  - web3
  - xrpl-py>=4.1.0
  - yaml>=0.2.5
  - zlib>=1.2.13
